"use client";
import { useState, useEffect } from "react";

interface LocationState {
  latitude: number | null;
  longitude: number | null;
  loading: boolean;
  error: string | null;
  refreshLocation: () => void;
}

export function useGetLocation(): LocationState {
  const [latitude, setLatitude] = useState<number | null>(null);
  const [longitude, setLongitude] = useState<number | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);

  //new function to be called when page first loads and they dont click find me
  const getFirstLoadLocationData = async (): Promise<void> => {
    setLoading(true);
    setError(null);
    await fallbackToIpLocation();
  };

  //this is the original funxtion i wrote, now this will be called only on find me button as per deepesh
  const getLocationData = (): void => {
    setLoading(true);
    setError(null);

    // This will auto request location when component mounts, if u click deny for the permission it won't ask again
    if (navigator.geolocation) {
      navigator.geolocation.getCurrentPosition(
        (position: GeolocationPosition) => {
          setLatitude(position.coords.latitude);
          setLongitude(position.coords.longitude);
          setLoading(false);
        },
        async (err: GeolocationPositionError) => {
          console.log("Unable to retrieve your browser location");
          console.error("Error Code = " + err.code + " - " + err.message);

          //This is when user will say no we fall back to IP-based like deepesh wanted
          await fallbackToIpLocation();
        },
        {
          maximumAge: 60000, //So that if user is refreshing consecutively for no reason we dont check again and again
          timeout: 15000, // Wait up to 15 seconds for a position from gps but then if it hangs we just go for fallback
        }
      );
    } else {
      console.log("Geolocation is not supported by this browser.");
      //Browser doesnt support so we go straight to fallback
      fallbackToIpLocation();
    }
  };

  const fallbackToIpLocation = async (): Promise<void> => {
    try {
      const response = await fetch("https://get-location.qranalytica.com", {
        method: "POST",
      });

      if (!response.ok) {
        console.log(`Failed to fetch location! Status: ${response.status}`);
      } else {
        const data = await response.json();

        if (data.latitude && data.longitude) {
          setLatitude(parseFloat(data.latitude));
          setLongitude(parseFloat(data.longitude));
        } else {
          console.log("Unable to determine location with fallback");
          setError("Unable to determine location with fallback");
        }
      }
    } catch (error) {
      console.log("Unable to determine location with fallback");
      console.error("IP Geolocation error:", error);
      setError("Unable to determine location with fallback");
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    //first time and there is no latitude longitude
    if (!latitude && !longitude) {
      getFirstLoadLocationData();
    }
  }, []);

  //only used to test , not used anywhere else
  const refreshLocation = (): void => {
    getLocationData();
  };

  return { latitude, longitude, loading, error, refreshLocation };
}
