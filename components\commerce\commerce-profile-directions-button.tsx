import React from "react";
import { Button } from "../ui/button";
import { FaDirections } from "react-icons/fa";

const CommerceProfileDirectionsButton = ({
  latitude,
  longitude,
}: {
  latitude: any;
  longitude: any;
}) => {
  const handleOpenGoogleMaps = () => {
    const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;

    window.open(googleMapsUrl, "_blank");
  };

  return (
    <Button
      className="w-full sm:w-fit h-8 px-3 flex gap-1 justify-center items-center bg-white border-[2px] border-[#ff8a00] text-[#ff8a00] hover:bg-white rounded-full"
      onClick={handleOpenGoogleMaps}
    >
      <FaDirections size={14} />
      {" Directions"}
    </Button>
  );
};

export default CommerceProfileDirectionsButton;
