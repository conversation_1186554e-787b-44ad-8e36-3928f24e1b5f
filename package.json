{"name": "justcravin-website", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@auth/core": "^0.34.2", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.1", "@radix-ui/react-alert-dialog": "^1.1.2", "@radix-ui/react-avatar": "^1.1.7", "@radix-ui/react-checkbox": "^1.2.3", "@radix-ui/react-dialog": "^1.1.11", "@radix-ui/react-dropdown-menu": "^2.1.12", "@radix-ui/react-hover-card": "^1.1.2", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.11", "@radix-ui/react-progress": "^1.1.4", "@radix-ui/react-scroll-area": "^1.2.6", "@radix-ui/react-select": "2.0.0", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.9", "@tanstack/react-query": "^5.59.0", "class-variance-authority": "^0.7.0", "clsx": "^2.1.1", "dayjs": "^1.11.13", "embla-carousel-autoplay": "^8.3.0", "embla-carousel-react": "^8.3.0", "fuse.js": "^7.0.0", "lodash": "^4.17.21", "lucide-react": "^0.447.0", "next": "14.2.14", "next-auth": "^4.24.11", "react": "^18", "react-dom": "^18", "react-hook-form": "^7.56.1", "react-icons": "^5.3.0", "sharp": "^0.33.5", "sonner": "^2.0.3", "tailwind-merge": "^2.5.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.3"}, "devDependencies": {"@types/lodash": "^4.17.16", "@types/node": "^20", "@types/react": "^18.3.11", "@types/react-dom": "^18", "eslint": "^8", "eslint-config-next": "14.2.14", "postcss": "^8", "tailwindcss": "^3.4.1", "typescript": "^5"}}