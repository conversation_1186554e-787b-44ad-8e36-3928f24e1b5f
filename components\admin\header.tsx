"use client";

import { signOut, useSession } from "next-auth/react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"; // Import Sheet components
import { LogOut, User, Menu } from "lucide-react"; // Import Menu icon
import SidebarNav from "./sidebar-nav"; // Import SidebarNav for mobile sheet
import { usePathname } from "next/navigation";
import Link from "next/link";
import { useState } from "react";

// Helper function to get title from pathname (customize as needed)
const getTitleFromPathname = (pathname: string): string => {
  if (pathname === "/admin") return "Dashboard";
  if (pathname.startsWith("/admin/products")) return "Products";
  if (pathname.startsWith("/admin/users")) return "Users";
  if (pathname.startsWith("/admin/settings")) return "Settings";
  // Add more mappings as your admin panel grows
  return "Admin"; // Default title
};


export default function Header() {
  const { data: session } = useSession();
  const pathname = usePathname();
  const [isSheetOpen, setIsSheetOpen] = useState(false); // State to control sheet

  const handleLogout = async () => {
    await signOut({ callbackUrl: "/auth/login" }); // Corrected callback URL
  };

  // ... existing getInitials function ...
  const getInitials = (name?: string | null) => {
    if (!name) return "?";
    return name
      .split(" ")
      .map((n) => n[0])
      .join("")
      .toUpperCase();
  };

  return (
    <header className="sticky top-0 z-30 flex h-16 items-center justify-between gap-4 border-b bg-background px-4 md:px-6">
      <div className="flex items-center gap-2">
        {/* Mobile Menu Button */}
        <Sheet open={isSheetOpen} onOpenChange={setIsSheetOpen}>
          <SheetTrigger asChild>
            <Button variant="outline" size="icon" className="md:hidden flex-shrink-0">
              <Menu className="h-5 w-5" />
              <span className="sr-only">Toggle navigation menu</span>
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="flex flex-col p-0 w-64">
             <div className="flex items-center h-16 px-6 border-b">
                {/* Optional: Add Logo */}
                <Link href="/admin" className="text-lg font-semibold tracking-tight text-foreground" onClick={() => setIsSheetOpen(false)}>
                  Admin Panel
                </Link>
              </div>
            <SidebarNav isMobile={true} onLinkClick={() => setIsSheetOpen(false)} />
          </SheetContent>
        </Sheet>

        {/* Page Title */}
        <h1 className="text-lg font-semibold hidden md:block">{getTitleFromPathname(pathname)}</h1>
      </div>

      {/* User Dropdown */}
      <div>
        {session?.user && (
          <DropdownMenu>
            {/* ... existing DropdownMenu code ... */}
             <DropdownMenuTrigger asChild>
              <Button variant="ghost" className="relative h-9 w-9 rounded-full">
                <Avatar className="h-9 w-9">
                  <AvatarImage src={session.user.image ?? undefined} alt={session.user.name ?? "User"} />
                  <AvatarFallback>{getInitials(session.user.name)}</AvatarFallback>
                </Avatar>
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-56" align="end" forceMount>
              <DropdownMenuLabel className="font-normal">
                <div className="flex flex-col space-y-1">
                  <p className="text-sm font-medium leading-none">{session.user.name}</p>
                  <p className="text-xs leading-none text-muted-foreground">
                    {session.user.email}
                  </p>
                </div>
              </DropdownMenuLabel>
              <DropdownMenuSeparator />
              {/* Add other items like Profile, Settings etc. */}
              {/* <DropdownMenuItem>
                <User className="mr-2 h-4 w-4" />
                <span>Profile</span>
              </DropdownMenuItem> */}
              <DropdownMenuItem onClick={handleLogout} className="cursor-pointer text-destructive focus:text-destructive focus:bg-destructive/10">
                <LogOut className="mr-2 h-4 w-4" />
                <span>Log out</span>
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        )}
      </div>
    </header>
  );
}
