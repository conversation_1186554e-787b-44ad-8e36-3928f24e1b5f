/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    loader: "custom",
    loaderFile: "./lib/img-proxy-loader.ts",
    remotePatterns: [
      {
        protocol: "https",
        hostname: "images.pexels.com",
        port: "",
      },

      {
        protocol: "https",
        hostname: "cravin.s3.me-central-1.amazonaws.com",
        port: "",
      },
      {
        protocol: "https",
        hostname: "cravin-food-merchant.vercel.app",
        port: "",
      },
      // **.whatsapp.net
      {
        protocol: "https",
        hostname: "**.whatsapp.net",
        port: "",
      },
      // digitaloceanspaces.com
      {
        protocol: "https",
        hostname: "**.digitaloceanspaces.com",
        port: "",
      },
    ],
  },
};

export default nextConfig;
