import React from "react";
import { But<PERSON> } from "./ui/button";
import { useRouter } from "next/navigation";
import Link from "next/link";

const ProductSelector = ({
  setSelectedProduct,
}: {
  setSelectedProduct: React.Dispatch<React.SetStateAction<string>>;
}) => {
  const router = useRouter();
  return (
    <div className="h-full w-full max-w-[960px] flex flex-col px-8 py-10 items-center gap-3 text-center">
      <p className="font-medium text-2xl">
        Not Sure What You&apos;re Looking For?
      </p>
      <p className="font-normal text-lg">
        Browse Listings Of Your Choice Below
      </p>
      <div className="h-full w-full bg-transparent flex flex-wrap items-center justify-center gap-3 p-3">
        <Link href="/food" passHref className="w-full sm:w-fit">
          <Button
            size="lg"
            className="w-full sm:w-fit bg-white border-[2px] border-[#FF0000] text-[#FF0000] hover:bg-[#FF0000] hover:text-white px-5 rounded-full"
          >
            Restaurants
          </Button>
        </Link>
        <Link href="/commerce" passHref className="w-full sm:w-fit">
          <Button
            size="lg"
            className="w-full sm:w-fit bg-white border-[2px] border-[#ff8a00] text-[#ff8a00] hover:bg-[#ff8a00] hover:text-white px-5 rounded-full"
          >
            Commercial Outlets
          </Button>
        </Link>
        <Link href="/sports" passHref className="w-full sm:w-fit">
          <Button
            size="lg"
            className="w-full sm:w-fit bg-white border-[2px] border-[#a4c719] text-[#a4c719] hover:bg-[#a4c719] hover:text-white px-5 rounded-full"
          >
            Sports Venues
          </Button>
        </Link>
      </div>
    </div>
  );
};

export default ProductSelector;
