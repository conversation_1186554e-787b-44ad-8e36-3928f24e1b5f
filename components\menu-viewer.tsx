import React, { useState, useRef, useEffect } from "react";
import { ChevronLeft, ChevronRight } from "lucide-react";
import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Button } from "./ui/button";

const MenuViewer = ({ menuImages }: { menuImages: any }) => {
  const [open, setOpen] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);

  const [dragStart, setDragStart] = useState({ x: 0, y: 0 });

  const imageContainerRef: any = useRef(null);
  const imageRef: any = useRef(null);

  const hasImages = menuImages && menuImages.length > 0;
  const placeholderSrc = "/Cravin_food_placeholder.png";
  const thumbnailImage = hasImages ? menuImages[0] : placeholderSrc;

  //keyboard controls for arrows and escape button
  useEffect(() => {
    if (!open) return;

    const handleKeyDown = (e: any) => {
      if (e.key === "ArrowLeft") {
        handlePrevious();
      } else if (e.key === "ArrowRight") {
        handleNext();
      } else if (e.key === "Escape") {
        setOpen(false);
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [open, currentIndex]);

  const handlePrevious = () => {
    if (!hasImages || menuImages.length <= 1) return;
    setCurrentIndex((prev) => (prev > 0 ? prev - 1 : menuImages.length - 1));
  };

  const handleNext = () => {
    if (!hasImages || menuImages.length <= 1) return;
    setCurrentIndex((prev) => (prev < menuImages.length - 1 ? prev + 1 : 0));
  };

  const handleTouchStart = (e: any) => {
    //This is for mobile swipe we want to change page, for that we store the touch points for calculations later
    if (e.touches.length === 1) {
      //we check e.touches.length === 1 to make sure its single finger swipe , we now mark where the finger first touched the screen
      setDragStart({ x: e.touches[0].clientX, y: e.touches[0].clientY });
    }
  };

  const handleTouchEnd = (e: any) => {
    //same thing we check if only single finger is swiping at the time the swipe ends
    if (e.changedTouches.length === 1) {
      //we get the final position of the finger
      const touch = e.changedTouches[0];
      //now we have start and end position of the finger along both axis, so now we math the calculations
      const deltaX = touch.clientX - dragStart.x;
      const deltaY = touch.clientY - dragStart.y;

      //now this part is also tricky, basically we can count it as a swipe only when movement along x axis is significant and along y is minimal
      //this is so it doesnt mix up with scroll
      if (Math.abs(deltaX) > 50 && Math.abs(deltaY) < 50) {
        //finally if x axis value positive we go next , else previous
        if (deltaX < 0) {
          handleNext();
        } else {
          handlePrevious();
        }
      }
    }
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <div className="relative w-full h-64 rounded-lg overflow-hidden cursor-pointer">
          <img
            alt="Menu preview"
            src={thumbnailImage}
            className="object-cover w-full h-full"
          />
          <div className="absolute bottom-0 left-0 right-0 bg-black/50 text-white py-2 px-4 text-center text-sm">
            Tap to view
          </div>
        </div>
      </DialogTrigger>

      <DialogContent className="max-w-[100dvw] p-0 bg-black border-0 h-[100dvh] max-h-[100dvh] overflow-auto flex flex-col py-4">
        <div className="relative flex-1 bg-black flex justify-center">
          {/*Arrow buttons we show only on desktop and only when we have more than one image*/}
          {hasImages && menuImages.length > 1 && (
            <>
              <Button
                onClick={handlePrevious}
                className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black p-2 rounded-full text-white hover:bg-black hidden md:flex z-10 border-none outline-none"
                aria-label="Previous image"
              >
                <ChevronLeft size={40} />
              </Button>

              <Button
                onClick={handleNext}
                className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black p-2 rounded-full text-white hover:bg-black hidden md:flex z-10 border-none outline-none"
                aria-label="Next image"
              >
                <ChevronRight size={40} />
              </Button>
            </>
          )}

          <div
            ref={imageContainerRef}
            className="w-full md:w-11/12 max-w-6xl h-full flex items-center justify-center"
            onTouchStart={handleTouchStart}
            onTouchEnd={handleTouchEnd}
          >
            <div className="relative transition-transform duration-150">
              <img
                ref={imageRef}
                src={hasImages ? menuImages[currentIndex] : placeholderSrc}
                alt={`Menu page ${currentIndex + 1}`}
                className="max-w-full max-h-full object-contain select-none"
                onDragStart={(e) => e.preventDefault()}
                loading="lazy"
              />
            </div>
          </div>
        </div>

        {/*DOT PAGINATION INDICATOR PROBLEM IS TOO MANY IMAGES TOOM ANY DOTS*/}
        {/* {hasImages && menuImages.length > 1 && (
          <div className="bg-black text-white p-2 flex justify-center items-center">
            <div className="flex gap-1">
              {menuImages.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index === currentIndex ? "bg-white" : "bg-gray-600"
                  }`}
                />
              ))}
            </div>
          </div>
        )} */}

        {hasImages && menuImages.length > 1 && (
          <span className="text-sm text-white mx-auto">
            Page {currentIndex + 1} of {menuImages.length}
          </span>
        )}

        {/*For customers who wont swipe unless I tell them to*/}
        <div className="bg-black text-white p-2 text-xs text-center md:hidden">
          • Swipe to navigate •
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default MenuViewer;
