"use client";
import React from "react";
import { useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import Sports from "./sports";
import { useGetLocation } from "@/components/use-get-location-hook";

export default function SportsParent() {
  const { latitude, longitude, loading, refreshLocation } = useGetLocation();
  const searchParams = useSearchParams();
  const queryEmirate = searchParams.get("emirate");
  const querySearch = searchParams.get("search");
  const queryPage = searchParams.get("page") || "1";

  // console.log({
  //   queryEmirate: queryEmirate,
  //   querySearch: querySearch,
  //   queryLatitude: queryLatitude,
  //   queryLongitude: queryLongitude,
  // });

  const { data: sportsListData, isLoading } = useQuery({
    queryKey: [
      "getSportsListings",
      latitude,
      longitude,
      loading,
      queryPage,
      queryEmirate,
      querySearch,
    ],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL +
          `/clubs/landing-page/listing-page-details?pageNumber=${queryPage}${
            queryEmirate && queryEmirate !== "All"
              ? `&emirate=${queryEmirate}`
              : ""
          }${querySearch ? `&search=${querySearch}` : ""}${
            latitude && longitude
              ? `&latitude=${latitude}&longitude=${longitude}`
              : ""
          }`
      );
      const data = await response.json();
      // console.log(data.data);
      return data.data;
    },
    enabled: !loading,
  });
  return (
    <>
      {/* {sportsListData && ( */}
      <Sports
        queryEmirate={queryEmirate}
        querySearch={querySearch}
        sportsListData={sportsListData?.listingData}
        currentPage={sportsListData?.currentPage || 1}
        totalPages={sportsListData?.totalPages || 1}
        isLoading={isLoading}
        refreshLocation={refreshLocation}
      />
      {/* )} */}
    </>
  );
}
