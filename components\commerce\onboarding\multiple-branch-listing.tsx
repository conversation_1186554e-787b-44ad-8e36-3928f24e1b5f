"use client";

import { useState, useEffect, useRef } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronLeft, ChevronRight, Info, Copy } from "lucide-react";
import ListingDetailsForm from "./listing-details-form";
import { BranchData, type ListingFormValues } from "./listing-details-types";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";
import Link from "next/link";
import { useSession } from "next-auth/react";
import { Card } from "@/components/ui/card";
// Add Dialog imports
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
  DialogClose,
} from "@/components/ui/dialog";
// Add Select imports
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface Branch {
  id: string;
  data: ListingFormValues;
}

// Define copyable fields for commerce
const allCopyableFields: (keyof ListingFormValues)[] = [
  "branch_tags",
  "more_info",
  "popular_items", // Commerce uses "popular_items" instead of "popular_dishes"
  "known_for",
  "average_spend",
  "show_in_landing_page",
  "image_gallery",
  "listing_image",
];

// Define fields that should be selected by default
const defaultSelectedFields: (keyof ListingFormValues)[] = [
  "branch_tags",
  "more_info",
  "popular_items",
  "known_for",
  "average_spend",
];

// Helper function to create the initial state for fieldsToCopy
const createDefaultFieldsToCopyState = (): Record<
  keyof ListingFormValues,
  boolean
> => {
  return allCopyableFields.reduce((acc, field) => {
    acc[field] = defaultSelectedFields.includes(field);
    return acc;
  }, {} as Record<keyof ListingFormValues, boolean>);
};

// Helper to get display names for fields
const getFieldDisplayName = (field: keyof ListingFormValues): string => {
  switch (field) {
    case "branch_tags":
      return "Shop Tags";
    case "more_info":
      return "Branch Features";
    case "popular_items":
      return "Popular Items";
    case "known_for":
      return "Known For";
    case "average_spend":
      return "Average Spend";
    case "show_in_landing_page":
      return "Show in Landing Page";
    case "image_gallery":
      return "Image Gallery";
    case "listing_image":
      return "Listing Image";
    default:
      return field;
  }
};

export default function MultipleBranchListing({ shopId }: { shopId: string }) {
  const [branches, setBranches] = useState<Branch[]>([]);
  const [currentBranchIndex, setCurrentBranchIndex] = useState(0);
  const session = useSession();
  const token = session.data?.user?.access_token;
  // Add state for copy dialog
  const [copyDialogOpen, setCopyDialogOpen] = useState(false);
  const [selectedSourceBranchIndexStr, setSelectedSourceBranchIndexStr] =
    useState<string | null>(null);
  const [fieldsToCopy, setFieldsToCopy] = useState<
    Record<keyof ListingFormValues, boolean>
  >(createDefaultFieldsToCopyState());
  const dialogTriggerRef = useRef<HTMLButtonElement>(null);

  useEffect(() => {
    session.update();
  }, []);

  const queryClient = useQueryClient();

  const { mutateAsync: updateBranch } = useMutation({
    mutationKey: ["updateBranch"],
    mutationFn: async (data: ListingFormValues) => {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL}/shops/landing-page-listing/${shopId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            branchId: data.branchId ?? branches[currentBranchIndex].id,
            branchName: data.branchName,
            branch_display_name: data.branch_display_name,
            branch_emirate: data.branch_emirate,
            branch_tags: data.branch_tags,
            more_info: data.more_info,
            popular_items: data.popular_items,
            known_for: data.known_for,
            average_spend: data.average_spend  ,
            show_in_landing_page: data.show_in_landing_page,
            image_gallery: data.image_gallery,
            listing_image: data.listing_image,
            shop_landline_numbers: data.shop_landline_numbers || [],
            branch_menus: data.branch_menus || [],
            social_links: data.social_links || {},
          }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.message || "Failed to save branch details");
      }

      return response.json();
    },
    onSuccess: async () => {
      toast.success("Branch details saved successfully");
      await queryClient.invalidateQueries({
        queryKey: ["branchData"],
      });
    },
    onError: (error: Error) => {
      toast.error(error.message || "Failed to save branch details");
    },
  });

  const handleBranchUpdate = async (data: ListingFormValues) => {
    try {
      // Update local state
      setBranches((prevBranches) =>
        prevBranches.map((branch, index) =>
          index === currentBranchIndex ? { ...branch, data } : branch
        )
      );

      // Save to backend using mutation
      await updateBranch(data);
    } catch (error) {
      console.error("Error saving branch details:", error);
    }
  };

  const {
    data: branchData,
    isLoading,
    error,
  } = useQuery<BranchData[]>({
    queryKey: ["branchData"],
    queryFn: async () => {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL}/shops/landing-page-listing-details/${shopId}`
      );
      const json = await response.json();
      return json.data;
    },
  });

  // Parse branch_menus from JSON strings and ensure menuImages contains only valid URLs
  const convertBranchMenus = (menus: any) => {
    if (!menus) return [];

    if (Array.isArray(menus)) {
      return menus.map((menuItem: any) => {
        // If it's a JSON string, try to parse it
        if (typeof menuItem === 'string') {
          try {
            const parsedMenu = JSON.parse(menuItem);
            return {
              menuName: parsedMenu.menuName || "Catalog",
              menuImages: Array.isArray(parsedMenu.menuImages)
                ? parsedMenu.menuImages.filter((img: any) =>
                    typeof img === 'string' && img.startsWith('http')
                  )
                : []
            };
          } catch (e) {
            // If it's a string but not valid JSON, check if it's a URL
            if (typeof menuItem === 'string' && menuItem.startsWith('http')) {
              return {
                menuName: "Catalog",
                menuImages: [menuItem]
              };
            }
            // If parsing fails, return empty menu
            return {
              menuName: "Catalog",
              menuImages: []
            };
          }
        }
        // If it's already an object, validate it
        else if (typeof menuItem === 'object' && menuItem !== null) {
          return {
            menuName: menuItem.menuName || "Catalog",
            menuImages: Array.isArray(menuItem.menuImages)
              ? menuItem.menuImages.filter((img: any) =>
                  typeof img === 'string' && img.startsWith('http')
                )
              : []
          };
        }
        // Default fallback
        return {
          menuName: "Catalog",
          menuImages: []
        };
      });
    }

    return [];
  };

  useEffect(() => {
    if (branchData) {
      const formattedBranches: any = branchData.map((branch, index) => {
        // Convert branch_menus if needed
        const updatedBranch = {
          ...branch,
          branch_menus: convertBranchMenus(branch.branch_menus)
        };

        return {
          id: branch.branchId ?? String(index + 1), // Use nullish coalescing for undefined
          data: updatedBranch,
        };
      });
      setBranches(formattedBranches);
    }
  }, [branchData]);

  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error loading branches</div>;

  const navigateBranch = (direction: "prev" | "next") => {
    if (direction === "prev" && currentBranchIndex > 0) {
      setCurrentBranchIndex((prev) => prev - 1);
    } else if (
      direction === "next" &&
      currentBranchIndex < branches.length - 1
    ) {
      setCurrentBranchIndex((prev) => prev + 1);
    }
  };

  // Add handlers for copy feature
  // Handle source branch selection within the dialog's Select component
  const handleDialogSourceSelect = (value: string) => {
    setSelectedSourceBranchIndexStr(value);
    // Reset fields to default selection when source changes
    setFieldsToCopy(createDefaultFieldsToCopyState());
  };

  // Handle checkbox changes for field selection
  const handleFieldSelectionChange = (
    field: keyof ListingFormValues,
    checked: boolean
  ) => {
    setFieldsToCopy((prev) => ({ ...prev, [field]: checked }));
  };

  // Handle "Select All" checkbox
  const handleSelectAllFields = (checked: boolean) => {
    setFieldsToCopy(
      allCopyableFields.reduce(
        (acc, field) => ({ ...acc, [field]: checked }),
        {} as Record<keyof ListingFormValues, boolean>
      )
    );
  };

  // Check if all copyable fields are selected
  const areAllFieldsSelected = allCopyableFields.every(
    (field) => fieldsToCopy[field]
  );

  // Perform the copy operation based on selected fields
  const confirmCopy = () => {
    // Convert selected index string back to number
    const selectedSourceBranch =
      selectedSourceBranchIndexStr !== null
        ? parseInt(selectedSourceBranchIndexStr, 10)
        : null;

    if (selectedSourceBranch === null || isNaN(selectedSourceBranch)) {
      toast.error("Invalid source branch selected.");
      return;
    }

    const sourceBranch = branches[selectedSourceBranch];
    const targetBranch = branches[currentBranchIndex];

    if (!sourceBranch || !targetBranch) return;

    const selectedFields = Object.entries(fieldsToCopy)
      .filter(([_, isSelected]) => isSelected)
      .map(([field]) => field as keyof ListingFormValues);

    if (selectedFields.length === 0) {
      toast.info("No fields selected to copy.");
      return;
    }

    // Create new data object, copying only selected fields
    const newData = { ...targetBranch.data }; // Start with target data
    selectedFields.forEach((field) => {
      // Ensure arrays are copied correctly, handle potential undefined values
      const sourceValue = sourceBranch.data[field];
      if (Array.isArray(sourceValue)) {
        (newData[field] as unknown[]) = [...sourceValue]; // Deep copy array with proper type assertion
      } else {
        (newData[field] as any) =
          sourceValue ?? (Array.isArray(newData[field]) ? [] : ""); // Use source value or default
      }
    });

    // Close dialog before updating state
    setCopyDialogOpen(false);

    // Update local state
    setBranches((prevBranches) =>
      prevBranches.map((branch, index) =>
        index === currentBranchIndex ? { ...branch, data: newData } : branch
      )
    );

    // Show success message
    toast.success(
      `Copied ${selectedFields.length} field(s) from ${sourceBranch.data.branchName}. Remember to save your changes.`
    );
  };

  // Handle closing the dialog - Reset state
  const handleDialogOpenChange = (open: boolean) => {
    setCopyDialogOpen(open);
    if (!open) {
      // Reset selections to default when dialog closes
      setSelectedSourceBranchIndexStr(null);
      setFieldsToCopy(createDefaultFieldsToCopyState());
    }
  };

  return (
    <div className="space-y-6">
      {/* Branch navigation */}
      {branches.length > 0 && (
        <Card className="p-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            <div className="flex items-center">
              <span className="text-lg font-medium">
                Branch {currentBranchIndex + 1} of {branches.length}:{" "}
                {branches[currentBranchIndex]?.data.branchName ||
                  `Branch ${currentBranchIndex + 1}`}
              </span>
            </div>

            <div className="flex items-center gap-2 flex-wrap">
              {branches.length > 1 && (
                <>
                  {/* Copy Dialog */}
                  <Dialog
                    open={copyDialogOpen}
                    onOpenChange={handleDialogOpenChange}
                  >
                    <DialogTrigger asChild>
                      <Button
                        ref={dialogTriggerRef}
                        variant="outline"
                        size="sm"
                        className="h-9"
                      >
                        <Copy className="h-4 w-4 mr-2" />
                        Copy Details...
                      </Button>
                    </DialogTrigger>
                    <DialogContent className="sm:max-w-lg">
                      <DialogHeader>
                        <DialogTitle>Copy Branch Details</DialogTitle>
                        <DialogDescription>
                          Select a source branch and the fields you want to copy
                          to{" "}
                          <span className="font-medium">
                            {branches[currentBranchIndex]?.data.branchName ||
                              `Branch ${currentBranchIndex + 1}`}
                          </span>
                          .
                        </DialogDescription>
                      </DialogHeader>

                      <div className="grid gap-4 py-4">
                        {/* Source Branch Selection */}
                        <div className="grid grid-cols-4 items-center gap-4">
                          <Label
                            htmlFor="source-branch-select"
                            className="text-right col-span-1"
                          >
                            Source
                          </Label>
                          <Select
                            value={selectedSourceBranchIndexStr ?? undefined}
                            onValueChange={handleDialogSourceSelect}
                          >
                            <SelectTrigger
                              id="source-branch-select"
                              className="col-span-3"
                            >
                              <SelectValue placeholder="Select source branch" />
                            </SelectTrigger>
                            <SelectContent>
                              {branches.map(
                                (branch, index) =>
                                  index !== currentBranchIndex && (
                                    <SelectItem
                                      key={branch.id}
                                      value={String(index)}
                                    >
                                      {branch.data.branchName ||
                                        `Branch ${index + 1}`}
                                    </SelectItem>
                                  )
                              )}
                            </SelectContent>
                          </Select>
                        </div>

                        {/* Field Selection - Conditional Rendering */}
                        {selectedSourceBranchIndexStr !== null && (
                          <div className="grid gap-2 border rounded-md p-4 mt-4">
                            {/* Select All Checkbox */}
                            <div className="flex items-center space-x-2 border-b pb-2 mb-2">
                              <Checkbox
                                id="select-all-fields-dialog"
                                checked={areAllFieldsSelected}
                                onCheckedChange={(checked) =>
                                  handleSelectAllFields(checked === true)
                                }
                              />
                              <Label
                                htmlFor="select-all-fields-dialog"
                                className="font-medium"
                              >
                                Select All Fields
                              </Label>
                            </div>
                            {/* Individual Field Checkboxes */}
                            {allCopyableFields.map((field) => (
                              <div
                                key={field}
                                className="flex items-center space-x-2"
                              >
                                <Checkbox
                                  id={`copy-field-dialog-${field}`}
                                  checked={fieldsToCopy[field] ?? false}
                                  onCheckedChange={(checked) =>
                                    handleFieldSelectionChange(
                                      field,
                                      checked === true
                                    )
                                  }
                                />
                                <Label
                                  htmlFor={`copy-field-dialog-${field}`}
                                  className="text-sm font-normal"
                                >
                                  {getFieldDisplayName(field)}
                                </Label>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>

                      <DialogFooter className="sm:justify-between items-center">
                        <p className="text-xs text-amber-600 text-left sm:text-right mt-2 sm:mt-0 flex-1 mr-4">
                          Note: Copied details overwrite current data. Save
                          required.
                        </p>
                        <div className="flex gap-2">
                          <DialogClose asChild>
                            <Button type="button" variant="outline">
                              Cancel
                            </Button>
                          </DialogClose>
                          <Button
                            type="button"
                            onClick={confirmCopy}
                            disabled={
                              selectedSourceBranchIndexStr === null ||
                              !Object.values(fieldsToCopy).some((v) => v)
                            }
                          >
                            Copy Selected
                          </Button>
                        </div>
                      </DialogFooter>
                    </DialogContent>
                  </Dialog>

                  {/* Navigation Buttons */}
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigateBranch("prev")}
                    disabled={currentBranchIndex === 0}
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" /> Previous Branch
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => navigateBranch("next")}
                    disabled={currentBranchIndex === branches.length - 1}
                  >
                    Next Branch <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </>
              )}
            </div>
          </div>
        </Card>
      )}

      {/* Branch details */}
      {branches.length > 0 && branches[currentBranchIndex] ? (
        <ListingDetailsForm
          key={`branch-${currentBranchIndex}-${JSON.stringify(
            branches[currentBranchIndex].data
          )}`}
          shopId={shopId}
          onSubmit={handleBranchUpdate}
          initialData={branches[currentBranchIndex].data}
        />
      ) : (
        <div className="bg-orange-50 p-6 rounded-lg border border-orange-200 text-orange-800">
          <h3 className="font-semibold mb-2 flex items-center">
            <Info className="h-5 w-5 mr-2" /> No branches found
          </h3>
          <p className="text-sm mb-4">
            There are no branches associated with this shop. Please add branches
            in the onboarding section first.
          </p>
          <Button
            variant="outline"
            className="bg-white text-orange-600 border-orange-300"
            onClick={() =>
              (window.location.href = `/admin/commerce/onboarding?id=${shopId}`)
            }
          >
            Go to Shop Onboarding
          </Button>
        </div>
      )}
    </div>
  );
}
