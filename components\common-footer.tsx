"use client";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import {
  FaFacebook,
  FaInstagram,
  FaLinkedin,
  FaTwitter,
  FaYoutube,
} from "react-icons/fa";
import cravinLogoBlack from "@/public/Cravin logo black.png";

const CommonFooter = () => {
  return (
    <footer className="h-full w-full bg-white pt-3 shadow-[0_-2px_10px_rgba(0,0,0,0.05)]">
      <div className="max-w-[1220px] mx-auto p-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center">
          <div className="flex gap-12 mb-6 sm:mb-0">
            <div className="flex flex-col gap-2">
              <h3 className="font-medium text-gray-700 mb-2">FOR BUSINESSES</h3>
              <a
                target="_blank"
                href="https://cravintechnologies.com/cravinfood"
                className="text-muted-foreground hover:text-muted-foreground"
              >
                Cravin Food
              </a>
              <a
                target="_blank"
                href="https://cravintechnologies.com/cravincommerce"
                className="text-muted-foreground hover:text-muted-foreground"
              >
                Cravin Commerce
              </a>
              <a
                target="_blank"
                href="https://cravintechnologies.com/cravinsports"
                className="text-muted-foreground hover:text-muted-foreground"
              >
                Cravin Sports
              </a>
            </div>

            <div className="flex flex-col gap-2">
              <h3 className="font-medium text-gray-700 mb-2">LEARN MORE</h3>
              <Link
                target="_blank"
                href="/privacy-policy"
                className="text-muted-foreground hover:text-muted-foreground"
              >
                Privacy
              </Link>
            </div>
          </div>

          <div className="flex flex-col items-start sm:items-end gap-4">
            <Image
              src={cravinLogoBlack}
              alt="Cravin Logo Black"
              className="hidden sm:block w-[150px] h-auto"
            />
            <div className="flex gap-3">
              <a
                target="_blank"
                href="https://www.facebook.com/people/Cravin/100093296476687/"
                className="bg-black hover:bg-black rounded-full p-2"
                aria-label="Facebook"
              >
                <FaFacebook size={12} className="text-white" />
              </a>
              <a
                target="_blank"
                href="https://x.com/cravinmena"
                className="bg-black hover:bg-black rounded-full p-2"
                aria-label="Twitter"
              >
                <FaTwitter size={12} className="text-white" />
              </a>
              <a
                target="_blank"
                href="https://www.instagram.com/cravin.mena/"
                className="bg-black hover:bg-black rounded-full p-2"
                aria-label="Instagram"
              >
                <FaInstagram size={12} className="text-white" />
              </a>
              <a
                target="_blank"
                href="https://www.youtube.com/@justcravin"
                className="bg-black hover:bg-black rounded-full p-2"
                aria-label="YouTube"
              >
                <FaYoutube size={12} className="text-white" />
              </a>
              <a
                target="_blank"
                href="https://www.linkedin.com/company/cravintechnologies/"
                className="bg-black hover:bg-black rounded-full p-2"
                aria-label="LinkedIn"
              >
                <FaLinkedin size={12} className="text-white" />
              </a>
            </div>
          </div>
        </div>
      </div>
      <div className="max-w-[1220px] mx-auto px-8 py-6 border-t-2 border-gray-400">
        <p className="text-sm text-muted-foreground text-center">
          © {new Date().getFullYear()}{" "}
          <span className="text-gray-700 font-medium">
            Cravin Technologies.
          </span>{" "}
          All rights reserved.
        </p>
      </div>
    </footer>
  );
};

export default CommonFooter;
