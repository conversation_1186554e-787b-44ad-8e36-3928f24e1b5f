"use client";
import React, { useEffect, useState } from "react";
// import ProductSelector from "./product-selector";
// import { Input } from "./ui/input";
import Image from "next/image";
import cravinLogo from "@/public/Cravin black stroke Logo.png";
import bigplaceholder from "@/public/PLACEHOLDER.png";
import landingbanner from "@/public/Landing banner1.png";
// import { Button } from "./ui/button";
// import {
//   Select,
//   SelectContent,
//   SelectGroup,
//   SelectItem,
//   SelectLabel,
//   SelectTrigger,
//   SelectValue,
// } from "@/components/ui/select";
// import { useRouter } from "next/navigation";
// import RotatingWords from "./rotating-words";
// import Link from "next/link";
import MainPageImageCards from "./main-page-image-cards";
import CommonFooter from "./common-footer";

const WholeBody = () => {
  //search removed for now
  // const router = useRouter();
  // const [selectedProduct, setSelectedProduct] = useState("");
  // const [selectedEmirate, setSelectedEmirate] = useState("");
  // const [searchInput, setSearchInput] = useState("");

  //made custom hook for the actual listing pages so we dont ask location here anymore for now
  // useEffect(() => {
  //   // This will auto request location when component mounts, if u click deny for the permission it won't ask again
  //   if (navigator.geolocation) {
  //     navigator.geolocation.getCurrentPosition(
  //       (position) => {
  //         setLatitude(position.coords.latitude);
  //         setLongitude(position.coords.longitude);
  //       },
  //       async (err) => {
  //         console.log("Unable to retrieve your browser location");
  //         console.error("Error Code = " + err.code + " - " + err.message);

  //         //This is when user will say no we fall back to IP-based like deepesh wanted
  //         try {
  //           //geoplugin that nookesh provided
  //           //should automatically use the client's IP if none is provided,test and confirm
  //           const response = await fetch("http://www.geoplugin.net/json.gp");
  //           const data = await response.json();

  //           if (data.geoplugin_latitude && data.geoplugin_longitude) {
  //             setLatitude(parseFloat(data.geoplugin_latitude));
  //             setLongitude(parseFloat(data.geoplugin_longitude));
  //           } else {
  //             console.log("Unable to determine location with fallback");
  //           }
  //         } catch (error) {
  //           console.log("Unable to determine location with fallback");
  //           console.error("IP Geolocation error:", error);
  //         }
  //       }
  //     );
  //   } else {
  //     console.log("Geolocation is not supported by this browser.");
  //     //Browser doesnt support so we go straight to fallback
  //     try {
  //       fetch("http://www.geoplugin.net/json.gp")
  //         .then((response) => response.json())
  //         .then((data) => {
  //           if (data.geoplugin_latitude && data.geoplugin_longitude) {
  //             setLatitude(parseFloat(data.geoplugin_latitude));
  //             setLongitude(parseFloat(data.geoplugin_longitude));
  //           } else {
  //             console.log(
  //               "Unable to determine location with fallback and browser incompatible with geolocation"
  //             );
  //           }
  //         });
  //     } catch (error) {
  //       console.log(
  //         "Unable to determine location with fallback and browser incompatible with geolocation"
  //       );
  //       console.error("IP Geolocation error:", error);
  //     }
  //   }
  // }, []);

  return (
    <div className="h-full min-h-[100dvh] w-full flex flex-col items-center">
      <header className="w-full bg-white h-20 fixed top-0 z-50">
        <div className="h-full w-full max-w-[1220px] flex justify-between items-center px-4 py-2 mx-auto">
          <div className="relative w-36 h-16">
            <Image
              alt="Cravin"
              src={cravinLogo}
              fill
              sizes="160px"
              className="object-contain"
              priority
            />
          </div>

          {/*temporary removed for hrishi re-added feb 2025 , REMOVED AGAIN ON 15 MAY 2025*/}
          {/* <Link href="https://cravintechnologies.com" passHref>
          <Button className="rounded-full bg-black hover:bg-black px-5">
            For Business
          </Button>
        </Link> */}

          {/* hrishi national day button 
          <Button
            className="font-medium rounded-full bg-black hover:bg-black px-5 w-fit"
            onClick={() => {
              window.open("https://forms.gle/NH28kL3xZ4hD4SnC7", "_blank");
            }}
          >
            For Business
          </Button> */}
        </div>
      </header>

      <main className="h-full w-full flex-1 bg-[#f1f3f2]">
        <div className="h-full w-full max-w-[1220px] flex flex-col items-center justify-center mx-auto mt-28 mb-8 px-4">
          <div className="w-full mb-6 overflow-hidden rounded-xl relative aspect-[1186/420]">
            <Image
              src={landingbanner}
              alt="Wide banner image"
              fill
              sizes="(max-width: 1200px) 100vw, 1186px"
              priority
              style={{
                objectFit: "cover",
              }}
            />
          </div>

          <MainPageImageCards />

          {/* THIS CODE WAS ADDED DURING NATIONAL DAY , NOT COMMENTED FOR ETHAN TO SHOW INVESTORS
         <div className="flex flex-col h-full gap-0 items-center">
          <div className="w-full max-w-[900px] min-h-[40dvh] lg:min-h-[50dvh] xl:min-h-[75dvh] 2xl:min-h-[60dvh] flex flex-col justify-center items-center px-8 py-10 mt-32 text-black">
            <div className="flex flex-col items-center w-full font-black text-3xl sm:text-5xl xl:text-6xl text-center capitalize gap-8">
              <p className="text-black">
                UAE&apos;s 1st listing platform for restaurants that do their
                own delivery.
              </p>
              <Button
                className="font-medium rounded-full bg-[#ff0000] hover:bg-[#ff0000] px-5 w-fit"
                onClick={() => {
                  window.open("https://forms.gle/NnmfxkecB9xTChNt8", "_blank");
                }}
              >
                List For Absolutely Free
              </Button>
            </div>
          </div>

          <div className="w-full max-w-[960px] min-h-[40dvh] lg:min-h-[50dvh] xl:min-h-[75dvh] 2xl:min-h-[60dvh] flex flex-col justify-center items-center px-8 py-10 text-black">
            <div className="flex flex-col items-center w-full  text-center capitalize gap-8">
              <div className="flex flex-col items-center w-full text-center capitalize gap-4">
                <p className="text-black font-black text-3xl sm:text-5xl xl:text-6xl">
                  On a mission to save restaurants{" "}
                  <b className="text-[#ff0000] font-minako">AED 15M</b> and{" "}
                  <b className="text-[#ff0000] font-minako">173,000 hours</b> by
                  2025 end.
                </p>
                <p className="text-black max-w-[720px] font-inters text-xs sm:text-sm xl:text-lg">
                  On average a restaurant spends 7.5 minutes per order and loses
                  4 customers for every 10 orders handled manually. They also
                  pay almost 30% as commissions to delivery aggregators.
                </p>
              </div>
              <Button
                className="font-medium rounded-full bg-black hover:bg-black px-5 w-fit"
                onClick={() => {
                  window.open("https://forms.gle/NH28kL3xZ4hD4SnC7", "_blank");
                }}
              >
                Interested!
              </Button>
            </div>
          </div>
        </div> */}

          {/* THIS CODE WAS REMOVED ON 15 MAY 2025 AFTER A REDESIGN WAS DECIDED FOR THE WHOLE PAGE
         <div className="w-full max-w-[960px] h-[150px] flex flex-col justify-center items-center px-8 py-10 mt-32">
          <RotatingWords />
        </div>
        <div className="w-full max-w-[960px] flex items-center justify-center px-8 py-10">
          <div className="w-full flex flex-col lg:flex-row gap-2 border-2 border-black/30 rounded-lg lg:rounded-full p-2">
            <Select onValueChange={(value) => setSelectedProduct(value)}>
              <SelectTrigger className="w-full lg:w-[450px] border-none">
                <SelectValue placeholder="Select An Industry" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>INDUSTRIES</SelectLabel>
                  <SelectItem value="sports">Sports Venues</SelectItem>
                  <SelectItem value="food">Restaurants</SelectItem>
                  <SelectItem value="commerce">Commercial Outlets</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>

            <div className="border border-black/30" />

            <Select onValueChange={(value) => setSelectedEmirate(value)}>
              <SelectTrigger className="w-full lg:w-[350px]  border-none">
                <SelectValue placeholder="Select An Emirate" />
              </SelectTrigger>
              <SelectContent>
                <SelectGroup>
                  <SelectLabel>Emirates</SelectLabel>
                  <SelectItem value="Abu Dhabi">Abu Dhabi</SelectItem>
                  <SelectItem value="Dubai">Dubai</SelectItem>
                  <SelectItem value="Sharjah">Sharjah</SelectItem>
                  <SelectItem value="Ajman">Ajman</SelectItem>
                  <SelectItem value="Umm Al Quwain">Umm Al Quwain</SelectItem>
                  <SelectItem value="Ras Al Khaimah">Ras Al Khaimah</SelectItem>
                  <SelectItem value="Fujairah">Fujairah</SelectItem>
                </SelectGroup>
              </SelectContent>
            </Select>

            <div className="border border-black/30" />

            <Input
              className="w-full border-none placeholder:text-black outline-none"
              placeholder="Search for sports venues, restaurants or stores."
              value={searchInput}
              onChange={(e) => setSearchInput(e.target.value)}
            />

            <Button
              className="rounded-full bg-black hover:bg-black px-5"
              disabled={!selectedProduct}
              onClick={() => {
                router.push(
                  `/${selectedProduct}?emirate=${selectedEmirate}&search=${searchInput}`
                );
              }}
            >
              Search
            </Button>
          </div>
        </div>
        <ProductSelector setSelectedProduct={setSelectedProduct} /> */}
        </div>
      </main>

      {/* THIS IS THE OLD FOOTER , REDESIGNING AS PER ETHAN DESIGN NOW
        <footer className="h-full w-full bg-white pt-3 shadow-[0_-2px_10px_rgba(0,0,0,0.05)]">
          <div className="w-full max-w-[1220px] flex flex-col sm:flex-row gap-6 sm:gap-0 justify-between items-start px-8 py-8 bg-white">
            <div className="flex flex-col items-start justify-center gap-1">
              <div className="flex h-full items-center justify-center gap-2 bg-white">
                <p className="font-medium text-xl bg-white">Get In Touch</p>
              </div>
              <div className="flex h-full items-center justify-center gap-2">
                <MdOutlinePhoneInTalk size={24} className="min-w-6" />
                <div className="flex flex-col">
                  <p>+971 52 501 8033</p>
                  <p>+971 58 569 1229</p>
                </div>
              </div>
              <div className="flex h-full items-center justify-center gap-2">
                <MdOutlineEmail size={24} className="min-w-6" />
                <p><EMAIL></p>
              </div>
              <div className="flex h-full items-center justify-center gap-2">
                <MdLocationPin size={24} className="min-w-6" />
                <p>
                  In5 Tech Dubai, Al Sufouh 2, King Salman Street, Dubai
                  Internet City, Dubai, UAE
                </p>
              </div>
            </div>

            <div className="flex flex-col items-start sm:items-end justify-center gap-1 bg-white">
              <p className="font-medium text-xl bg-white">Find Us At</p>
              <div className="flex h-full items-center justify-center gap-2 text-black">
                <a
                  target="_blank"
                  href="https://www.facebook.com/people/Cravin/100093296476687/"
                  aria-label="Facebook"
                >
                  <MdFacebook size={32} />
                </a>
                <a
                  target="_blank"
                  href="https://x.com/cravinmena"
                  aria-label="Twitter"
                >
                  <FaSquareXTwitter size={32} />
                </a>
                <a
                  target="_blank"
                  href="https://www.instagram.com/cravin.mena/"
                  aria-label="Instagram"
                >
                  <RiInstagramFill size={32} />
                </a>
                <a
                  target="_blank"
                  href="https://www.linkedin.com/company/cravintechnologies/"
                  aria-label="LinkedIn"
                >
                  <FaLinkedin size={32} />
                </a>
                <a
                  target="_blank"
                  href="https://www.youtube.com/@justcravin"
                  aria-label="YouTube"
                >
                  <FaYoutube size={32} />
                </a>
              </div>
              <Image
                alt="techproviderlogo"
                src={cravintechproviderLogo}
                width={180}
              />
            </div>
          </div>

          <div className="w-full border-t border-gray-200 flex justify-center items-center px-8 py-5 bg-white text-center text-gray-600 text-sm">
            <p>
              © {new Date().getFullYear()} Cravin Technologies. All rights
              reserved.
            </p>
          </div>
        </footer> */}

      <CommonFooter />
    </div>
  );
};

export default WholeBody;
