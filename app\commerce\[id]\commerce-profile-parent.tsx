"use client";
import React from "react";
import { useQuery } from "@tanstack/react-query";
import CommerceProfile from "./commerce-profile";

export default function CommerceProfileParent({
  branchId,
}: {
  branchId: string;
}) {
  //useQuery on get api here
  const { data: commerceProfileData } = useQuery({
    queryKey: ["getCommerceProfileInfo", branchId],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL +
          "/branches/landing-page/profile-page-details/" +
          branchId
      );
      const data = await response.json();
      // console.log(data.data);
      return data.data;
    },
  });

  const { data: commerceFAQData } = useQuery({
    queryKey: ["getCommerceFAQ", branchId],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL +
          "/branches/landing-page/get-branch-faqs/" +
          branchId
      );
      const data = await response.json();
      // console.log(data.data);
      return data.data;
    },
  });
  return (
    <>
      {commerceProfileData &&
        commerceProfileData.branchTiming &&
        commerceProfileData.branchTiming.length > 0 && (
          <CommerceProfile
            commerceProfileData={commerceProfileData}
            commerceFAQData={commerceFAQData}
          />
        )}
    </>
  );
}
