import React, { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { Loader2, AlertTriangle } from "lucide-react";

interface DeleteConfirmationDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  title: string;
  description: string;
  onConfirm: () => void;
  isLoading?: boolean;
  itemName?: string;
  confirmText?: string;
}

const DeleteConfirmationDialog = ({
  open,
  onOpenChange,
  title,
  description,
  onConfirm,
  isLoading = false,
  itemName = "",
  confirmText = "delete my project"
}: DeleteConfirmationDialogProps) => {
  const [inputValue, setInputValue] = React.useState("");
  const isConfirmDisabled = inputValue !== confirmText || isLoading;
  const dialogRef = React.useRef<HTMLDivElement>(null);

  const handleConfirm = () => {
    if (inputValue === confirmText) {
      onConfirm();
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(e.target.value);
  };

  // Close dialog when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dialogRef.current && !dialogRef.current.contains(event.target as Node) && !isLoading) {
        onOpenChange(false);
      }
    };
    
    // Close on escape key
    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && !isLoading) {
        onOpenChange(false);
      }
    };

    if (open) {
      document.addEventListener('mousedown', handleClickOutside);
      document.addEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = 'hidden'; // Prevent scrolling when modal is open
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
      document.removeEventListener('keydown', handleEscapeKey);
      document.body.style.overflow = ''; // Restore scrolling when modal is closed
    };
  }, [open, onOpenChange, isLoading]);

  // Reset input value when dialog opens/closes
  useEffect(() => {
    if (open) {
      setInputValue("");
    }
  }, [open]);

  if (!open) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm" onClick={() => !isLoading && onOpenChange(false)} />
      <div 
        ref={dialogRef}
        className="relative max-w-md w-full bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 rounded-lg overflow-hidden shadow-xl transform transition-all animate-in fade-in duration-200"
      >
        <div className="px-6 pt-6">
          <div className="flex flex-col space-y-1.5 pb-2">
            <h2 className="text-xl font-semibold">{title}</h2>
            <p className="text-sm text-gray-500 dark:text-gray-400">{description}</p>
          </div>
        </div>
        
        <div className="px-6 py-4 space-y-5">
          <div className="bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-900/50 rounded-md p-4 flex items-start space-x-3">
            <AlertTriangle className="h-5 w-5 text-amber-500 dark:text-amber-400 mt-0.5 flex-shrink-0" />
            <div className="text-sm text-amber-800 dark:text-amber-300">
              <p className="font-medium mb-1">Warning</p>
              <p className="opacity-90">This action is permanent and cannot be undone. All related data will be deleted.</p>
            </div>
          </div>

          {itemName && (
            <div className="space-y-2.5">
              <label className="text-sm font-medium block">
                Enter the {itemName} name to continue:
              </label>
              <input 
                type="text"
                value={inputValue}
                onChange={handleInputChange}
                className="w-full bg-gray-50 dark:bg-zinc-800 border border-gray-300 dark:border-zinc-700 rounded-md px-3 py-2 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all"
                placeholder={itemName}
              />
            </div>
          )}

          <div className="space-y-2.5">
            <label className="text-sm font-medium block">
              To verify, type <span className="font-mono bg-gray-100 dark:bg-zinc-800 px-1.5 py-0.5 rounded text-gray-800 dark:text-gray-200 text-xs">{confirmText}</span> below:
            </label>
            <input 
              type="text"
              value={inputValue}
              onChange={handleInputChange}
              autoFocus
              className="w-full bg-gray-50 dark:bg-zinc-800 border border-gray-300 dark:border-zinc-700 rounded-md px-3 py-2 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-primary/20 focus:border-primary transition-all"
              placeholder={confirmText}
            />
            {inputValue && inputValue !== confirmText && (
              <p className="text-xs text-red-600 dark:text-red-400 mt-1">Text {"doesn't"} match. Please type exactly as shown above.</p>
            )}
          </div>
        </div>
        
        <div className="border-t border-gray-200 dark:border-zinc-800 px-6 py-4 flex items-center justify-end space-x-3 bg-gray-50 dark:bg-zinc-900/70">
          <Button 
            variant="outline" 
            onClick={() => onOpenChange(false)}
            className="font-medium"
            disabled={isLoading}
          >
            Cancel
          </Button>
          <Button
            variant="destructive"
            onClick={handleConfirm}
            disabled={isConfirmDisabled}
            className={`font-medium ${isConfirmDisabled ? 'opacity-70' : 'hover:bg-red-700'}`}
          >
            {isLoading ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Deleting...
              </>
            ) : (
              "Delete"
            )}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default DeleteConfirmationDialog;
