import React from 'react';
import { AlertCircle } from 'lucide-react';

interface ErrorProps {
  message?: string;
  className?: string;
}

const Error: React.FC<ErrorProps> = ({ 
  message = 'Something went wrong. Please try again later.',
  className = ''
}) => {
  return (
    <div className={`flex items-center gap-2 rounded-md border border-destructive/50 bg-destructive/10 p-3 text-sm text-destructive ${className}`}>
      <AlertCircle className="h-4 w-4" />
      <p>{message}</p>
    </div>
  );
};

export default Error;
