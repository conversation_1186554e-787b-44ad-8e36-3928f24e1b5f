"use client";

import { useMutation } from "@tanstack/react-query";
import Script from "next/script";
import React, { useEffect, useState } from "react";
import { toast } from "sonner";
import Image from "next/image";
import cravinLogo from "@/public/Cravin black stroke Logo.png";
import { Button } from "@/components/ui/button";
import {
  ArrowRight,
  CheckCircle2,
  Loader2,
  MessageSquare,
  ChevronLeft,
  Building2,
  Copy,
  ClipboardCheck,
} from "lucide-react";
import WhatsAppBusinessProfile from "@/components/whatsapp/business-profile";
import Link from "next/link";
import { FaWhatsapp } from "react-icons/fa6";

interface OnboardingPageProps {
  token: string | undefined;
  params: { slug: string[] };
}

export default function OnboardingPage({ token, params }: OnboardingPageProps) {
  const [isLoading, setLoading] = useState(false);
  const [isProfileEditMode, setIsProfileEditMode] = useState(false);
  const [phoneNumberId, setPhoneNumberId] = useState<string>("");
  const [productName, setProductName] = useState<string>("");
  const [businessId, setBusinessId] = useState<string>("");
  const [isCopied, setIsCopied] = useState(false);
  const [businessType, setBusinessType] = useState<
    "food" | "commerce" | "sports"
  >("food");

  useEffect(() => {
    // Get business type from URL params
    if (params?.slug && params?.slug.length > 0) {
      const type = params.slug[0];
      if (type === "food" || type === "commerce" || type === "sports") {
        setBusinessType(type);
      }
    }

    // Check if we're in profile editing mode or onboarding mode
    if (params?.slug[0] === "profile" && params?.slug[1]) {
      setIsProfileEditMode(true);
      // Use the provided phoneNumberId from URL
      setProductName(params.slug[1]);
      setBusinessId(params.slug[2]);

      // In profile mode, the business type is the second param
      if (
        params.slug[1] === "food" ||
        params.slug[1] === "commerce" ||
        params.slug[1] === "sports"
      ) {
        setBusinessType(params.slug[1] as "food" | "commerce" | "sports");
      }
    } else {
      // Normal onboarding flow
      (window as any).fbAsyncInit = () => {
        (window as any).FB.init({
          appId: "381749537600070", // Facebook App ID
          cookie: true, // enable cookies
          xfbml: true, // parse social plugins on this page
          version: "v19.0", // Graph API version
        });
      };

      window.addEventListener("message", sessionInfoListener);
    }

    return () => {
      if (!isProfileEditMode) {
        window.removeEventListener("message", sessionInfoListener);
      }
    };
  }, [params?.slug, isProfileEditMode]);

  const { mutateAsync: onboardCustomer } = useMutation({
    mutationKey: ["onboardingObject"],
    mutationFn: async (onboardingObject: {
      token: string;
      phone_number_id: string;
      waba_id: string;
    }) => {
      return fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/wa-business-management/onboarding-customer/${params?.slug[0]}/${params?.slug[1]}`,
        {
          method: "POST",
          headers: new Headers({
            "content-type": "application/json",
            Authorization: `Bearer ${onboardingObject.token + params?.slug[0]}`,
          }),
          body: JSON.stringify(onboardingObject),
        }
      );
    },
    onSuccess: async (data) => {
      const response = await data.json();
      if (response.error && response.statusCode === 400) {
        toast.error(response.message);
        setLoading(false);
      } else {
        toast.success("Onboarded successfully!");
        setLoading(false);
      }
    },
    onError: () => {
      toast.error("Issue in Onboarding. Please try again!");
      setLoading(false);
    },
  });

  const launchWhatsAppSignup = (): void => {
    if (typeof window !== "undefined") {
      (window as any).fbq?.("trackCustom", "WhatsAppOnboardingStart", {
        appId: "381749537600070",
        feature: "whatsapp_embedded_signup",
      });

      (window as any).FB.login(
        (response: { authResponse: { code: any } }) => {
          if (response.authResponse) {
            const code = response.authResponse.code;
            if (code) {
              const phone_number_id = localStorage.getItem("phone_number_id");
              const waba_id = localStorage.getItem("waba_id");
              localStorage.setItem("code", code);

              if (phone_number_id && waba_id) {
                setTimeout(async () => {
                  setLoading(true);
                  await onboardCustomer({
                    token: code,
                    phone_number_id,
                    waba_id,
                  });
                });
              }
            }
          } else {
            console.log("User cancelled login or did not fully authorize.");
          }
        },
        {
          config_id: "726742329668472",
          response_type: "code",
          override_default_response_type: true,
          extras: { sessionInfoVersion: 2 },
        }
      );
    }
  };

  interface WAEmbeddedSignupData {
    phone_number_id: string;
    waba_id: string;
  }

  interface WAEmbeddedSignupEvent {
    type: string;
    event: string;
    data: WAEmbeddedSignupData;
  }

  interface FacebookMessageEvent extends MessageEvent {
    origin: string;
    data: string;
  }

  const sessionInfoListener = (event: FacebookMessageEvent): void => {
    if (event.origin !== "https://www.facebook.com") return;
    try {
      const data: WAEmbeddedSignupEvent = JSON.parse(event.data);
      if (data.type === "WA_EMBEDDED_SIGNUP") {
        if (data.event === "FINISH") {
          const { phone_number_id, waba_id } = data.data;
          localStorage.setItem("phone_number_id", phone_number_id);
          localStorage.setItem("waba_id", waba_id);
        }
      }
    } catch (err) {
      console.log("🚀 ~ sessionInfoListener ~ err:", err);
    }
  };

  const getBusinessTypeLabel = () => {
    switch (businessType) {
      case "food":
        return "Restaurant";
      case "commerce":
        return "Shop";
      case "sports":
        return "Sports Club";
      default:
        return "Business";
    }
  };

  const getAdminPath = () => {
    return `/admin/${businessType}${
      params?.slug.length > 2
        ? `/onboarding?id=${params?.slug[2]}`
        : params?.slug.length > 1
        ? `/onboarding?id=${params?.slug[1]}`
        : ""
    }`;
  };

  return (
    <div className="flex flex-col flex-1 bg-gradient-to-b   text-gray-800">
      {!isProfileEditMode && (
        <Script id="facebook-pixel">
          {`
            (function (d, s, id) {
              let js,
                fjs = d.getElementsByTagName(s)[0];
              if (d.getElementById(id)) return;
              js = d.createElement(s);
              js.id = id;
              js.src = "https://connect.facebook.net/en_US/sdk.js";
              fjs.parentNode.insertBefore(js, fjs);
            })(document, "script", "facebook-jssdk");
          `}
        </Script>
      )}

      {/* Top Navigation Bar - Enhanced with better styling and context */}
      <header className="flex-shrink-0 w-full bg-white shadow-sm border-b">
        <div className="max-w-[1400px] mx-auto py-6 px-6">
          <div className="flex flex-col space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                {token && (
                  <Link href={getAdminPath()}>
                    <Button variant="outline" size="icon" className="h-8 w-8">
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                  </Link>
                )}
                <div className="flex gap-5">
                  {!token && (
                    <div>
                      <Image
                        alt="Cravin"
                        src={cravinLogo}
                        width={100}
                        height={100}
                        className="object-contain"
                        priority
                      />
                    </div>
                  )}
                  <div>
                    <h1 className="text-2xl font-semibold text-gray-900">
                      {isProfileEditMode
                        ? "WhatsApp Business Profile"
                        : "WhatsApp Business Setup"}
                    </h1>
                    <p className="text-sm text-gray-500">
                      {isProfileEditMode
                        ? `Customize your ${getBusinessTypeLabel()} WhatsApp business profile`
                        : `Connect your ${getBusinessTypeLabel()} with WhatsApp Business API`}
                    </p>
                  </div>
                </div>
              </div>

              {token && (
                <div className="flex items-center gap-3 flex-wrap">
                  {!isProfileEditMode ? (
                    <Link
                      href={
                        "/admin/onboard-whatsapp/profile/" +
                        params?.slug.join("/")
                      }
                    >
                      <Button
                        variant="outline"
                        className="flex items-center gap-2 bg-white hover:bg-green-50 text-green-600 border-green-200 hover:border-green-300 transition-all duration-200 shadow-sm"
                      >
                        <FaWhatsapp className="h-4 w-4" />
                        <span className="font-medium">
                          Edit Business Profile
                        </span>
                      </Button>
                    </Link>
                  ) : (
                    <Link
                      href={
                        "/admin/onboard-whatsapp/" +
                        params?.slug.slice(1).join("/")
                      }
                    >
                      <Button
                        variant="outline"
                        className="flex items-center gap-2 bg-white hover:bg-green-50 text-green-600 border-green-200 hover:border-green-300 transition-all duration-200 shadow-sm"
                      >
                        <FaWhatsapp className="h-4 w-4" />
                        <span className="font-medium">
                          Back to WhatsApp Setup
                        </span>
                      </Button>
                    </Link>
                  )}

                  <Link href={getAdminPath()}>
                    <Button
                      variant="outline"
                      className="flex items-center gap-2 bg-white hover:bg-orange-50 text-orange-600 border-orange-200 hover:border-orange-300 transition-all duration-200 shadow-sm"
                    >
                      <Building2 className="h-4 w-4" />
                      <span className="font-medium">
                        {businessType === "food"
                          ? "Restaurant"
                          : businessType === "commerce"
                          ? "Shop"
                          : "Club"}{" "}
                        Details
                      </span>
                    </Button>
                  </Link>

                  {/* Copy Share Onboarding link to merchant */}
                  <Button
                    variant="outline"
                    onClick={() => {
                      const url =
                        window.location.origin +
                        "/onboard/" +
                        params?.slug.join("/");
                      navigator.clipboard
                        .writeText(url)
                        .then(() => {
                          toast.success("Onboarding link copied to clipboard!");
                          setIsCopied(true);
                          setTimeout(() => setIsCopied(false), 2000); // Reset icon after 2 seconds
                        })
                        .catch(() => {
                          toast.error("Failed to copy link");
                        });
                    }}
                    className="flex items-center gap-2 bg-white hover:bg-orange-50 text-orange-600 border-orange-200 hover:border-orange-300 transition-all duration-200 shadow-sm"
                    disabled={isCopied} // Disable button briefly after copy
                  >
                    {isCopied ? (
                      <ClipboardCheck className="h-4 w-4 text-green-500" />
                    ) : (
                      <Copy className="h-4 w-4" />
                    )}
                    <span className="font-medium">Copy Onboarding Link</span>
                  </Button>
                </div>
              )}
            </div>

            {/* Breadcrumb Navigation */}
            {token && (
              <div className="flex items-center text-sm text-muted-foreground">
                <Link
                  href={`/admin/${businessType}`}
                  className="hover:text-primary"
                >
                  {businessType === "food"
                    ? "Restaurants"
                    : businessType === "commerce"
                    ? "Shops"
                    : "Sports Clubs"}
                </Link>
                <ChevronLeft className="h-3 w-3 mx-2 rotate-180" />
                <Link href={getAdminPath()} className="hover:text-primary">
                  {businessType === "food"
                    ? "Restaurant"
                    : businessType === "commerce"
                    ? "Shop"
                    : "Club"}{" "}
                  Details
                </Link>
                <ChevronLeft className="h-3 w-3 mx-2 rotate-180" />
                <span className="text-foreground font-medium">
                  {isProfileEditMode ? "WhatsApp Profile" : "WhatsApp Setup"}
                </span>
              </div>
            )}
          </div>
        </div>
      </header>

      <main className="flex-1 overflow-y-auto px-4 py-8 mx-auto max-w-[1400px]">
        {isProfileEditMode ? (
          // Render the WhatsApp Business Profile Component
          <WhatsAppBusinessProfile
            phoneNumberId={phoneNumberId}
            productName={productName}
            businessId={businessId}
            authToken={token || ""}
          />
        ) : (
          // Enhanced onboarding UI
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div className="space-y-8">
              <div>
                <h1 className="text-4xl font-bold text-gray-900 mb-4">
                  WhatsApp Business API Integration
                </h1>
                <p className="text-xl text-gray-600">
                  Connect with your customers where they already are - on
                  WhatsApp
                </p>
              </div>

              <div className="space-y-5">
                <h2 className="text-xl font-semibold text-gray-800 flex items-center">
                  <MessageSquare className="mr-2 text-green-600" size={20} />
                  Benefits
                </h2>
                <ul className="space-y-3">
                  {[
                    "Engage customers with rich messaging features",
                    "Automate responses and provide 24/7 support",
                    "Track metrics and optimize your communication",
                    "Build a verified business presence on WhatsApp",
                  ].map((item, index) => (
                    <li key={index} className="flex items-start">
                      <CheckCircle2
                        className="mr-2 text-green-500 flex-shrink-0 mt-1"
                        size={18}
                      />
                      <span>{item}</span>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="space-y-5">
                <h2 className="text-xl font-semibold text-gray-800">
                  Requirements
                </h2>
                <div className="bg-white rounded-lg shadow-sm p-5 space-y-4 border border-gray-100">
                  <div className="flex items-start">
                    <div className="bg-green-100 rounded-full p-2 mr-3 flex-shrink-0">
                      <CheckCircle2 className="text-green-600" size={20} />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">
                        Registered Business
                      </h3>
                      <p className="text-gray-600 text-sm mt-1">
                        Your business must be officially registered with valid
                        documentation
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-green-100 rounded-full p-2 mr-3 flex-shrink-0">
                      <CheckCircle2 className="text-green-600" size={20} />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">
                        Valid Website
                      </h3>
                      <p className="text-gray-600 text-sm mt-1">
                        An active website that represents your business
                      </p>
                    </div>
                  </div>
                  <div className="flex items-start">
                    <div className="bg-green-100 rounded-full p-2 mr-3 flex-shrink-0">
                      <CheckCircle2 className="text-green-600" size={20} />
                    </div>
                    <div>
                      <h3 className="font-medium text-gray-900">
                        Facebook Business Account
                      </h3>
                      <p className="text-gray-600 text-sm mt-1">
                        Connect with your Facebook Business account
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="flex flex-col items-center justify-center">
              <div className="bg-white rounded-xl shadow-lg p-8 w-full max-w-md border border-gray-200">
                <div className="text-center mb-8">
                  <div className="mb-4 inline-flex items-center justify-center w-16 h-16 rounded-full bg-green-50">
                    <MessageSquare size={32} className="text-green-600" />
                  </div>
                  <h2 className="text-2xl font-bold text-gray-900">
                    Get Started
                  </h2>
                  <p className="text-gray-600 mt-2">
                    Connect your WhatsApp Business account to start engaging
                    with your customers
                  </p>
                </div>

                <Button
                  onClick={launchWhatsAppSignup}
                  disabled={isLoading}
                  className="bg-green-600 hover:bg-green-700 text-white w-full py-6 flex justify-center items-center rounded-lg shadow-sm transition-all duration-200"
                >
                  {isLoading ? (
                    <>
                      <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                      <span className="font-medium">Connecting...</span>
                    </>
                  ) : (
                    <>
                      <span className="font-medium mr-2">
                        Continue with Facebook
                      </span>
                      <ArrowRight size={18} />
                    </>
                  )}
                </Button>

                <p className="text-gray-500 text-xs text-center mt-4">
                  By continuing, you agree to {"WhatsApp's"} Terms of Service
                  and Privacy Policy
                </p>
              </div>

              <div className="mt-8 flex items-center text-sm text-gray-500">
                <span className="mr-2">Powered by</span>
                <Image
                  src="https://upload.wikimedia.org/wikipedia/commons/6/6b/WhatsApp.svg"
                  alt="WhatsApp"
                  width={20}
                  height={20}
                  className="mr-1"
                />
                <span className="font-medium">WhatsApp Business API</span>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
}
