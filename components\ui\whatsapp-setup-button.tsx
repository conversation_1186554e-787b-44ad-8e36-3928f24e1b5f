import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { FaWhatsapp } from "react-icons/fa6";

interface WhatsAppSetupButtonProps {
  businessType: "food" | "commerce" | "sports";
  businessId: string | null;
}

/**
 * A reusable button component for WhatsApp setup across different business types
 *
 * @param businessType - The type of business ('food', 'commerce', or 'sports')
 * @param businessId - The ID of the business entity
 */
const WhatsAppSetupButton = ({
  businessType,
  businessId,
}: WhatsAppSetupButtonProps) => {
  if (!businessId) return null;

  const handleOpenWhatsAppSetup = () => {
    window.open(`/admin/onboard-whatsapp/${businessType}/${businessId}`);
  };

  return (
    <Button
      variant="outline"
      className="flex items-center gap-2 bg-white hover:bg-green-50 text-green-600 border-green-200 hover:border-green-300 transition-all duration-200"
      onClick={handleOpenWhatsAppSetup}
    >
      <FaWhatsapp className="h-4 w-4" />
      <span className="font-medium">WhatsApp Setup</span>
    </Button>
  );
};

export default WhatsAppSetupButton;
