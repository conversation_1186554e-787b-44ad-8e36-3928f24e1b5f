"use client";
import React from "react";
import { useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import Commerce from "./commerce";
import { useGetLocation } from "@/components/use-get-location-hook";

export default function CommerceParent() {
  const { latitude, longitude, loading, refreshLocation } = useGetLocation();
  const searchParams = useSearchParams();
  const queryEmirate = searchParams.get("emirate");
  const querySearch = searchParams.get("search");
  const queryPage = searchParams.get("page") || "1";

  // console.log({
  //   queryEmirate: queryEmirate,
  //   querySearch: querySearch,
  //   queryLatitude: queryLatitude,
  //   queryLongitude: queryLongitude,
  // });

  const { data: commerceListData, isLoading } = useQuery({
    queryKey: [
      "getCommerceListings",
      latitude,
      longitude,
      loading,
      queryPage,
      queryEmirate,
      querySearch,
    ],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL +
          `/shops/landing-page/listing-page-details?pageNumber=${queryPage}${
            queryEmirate && queryEmirate !== "All"
              ? `&emirate=${queryEmirate}`
              : ""
          }${querySearch ? `&search=${querySearch}` : ""}${
            latitude && longitude
              ? `&latitude=${latitude}&longitude=${longitude}`
              : ""
          }`
      );
      const data = await response.json();
      // console.log(data.data);
      return data.data;
    },
    enabled: !loading,
  });
  return (
    <>
      {/* {commerceListData && ( */}
      <Commerce
        queryEmirate={queryEmirate}
        querySearch={querySearch}
        commerceListData={commerceListData?.listingData}
        currentPage={commerceListData?.currentPage || 1}
        totalPages={commerceListData?.totalPages || 1}
        isLoading={isLoading}
        refreshLocation={refreshLocation}
      />
      {/* )} */}
    </>
  );
}
