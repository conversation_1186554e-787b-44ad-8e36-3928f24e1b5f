import { getServerSession, type Next<PERSON><PERSON>Options } from "next-auth";
import Credentials<PERSON>rovider from "next-auth/providers/credentials";

interface CustomUser {
  id: string;
  name: string;
  user_type: string;
  access_token: string;
}
export const authOptions: NextAuthOptions = {
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        username: { label: "Username", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials, req) {
        if (!credentials?.username || !credentials?.password) return null;
        const { username, password } = credentials;

        const response = await fetch(
          process.env.NEXT_PUBLIC_FOOD_BACKEND_URL +
            `/auth/onboarding/admin/login`,
          {
            method: "POST",
            body: JSON.stringify({
              username: username,
              password: password,
            }),
            headers: {
              "Content-Type": "application/json",
            },
          }
        );

        if (response.ok) {
          const data = await response.json();
          const user = {
            id: data.data.user_id,
            name: data.data.username,
            user_type: data.data.user_type,
            access_token: data.data.access_token,
          };
          // Authentication successful, return a user object
          return user;
        } else {
          const data = await response.json();
          if (data.statusCode === 400) {
            throw new Error("Invalid Username / Username Does Not Exist");
          } else if (data.statusCode === 401) {
            throw new Error("Invalid/Wrong Password");
          } else if (data.statusCode === 403) {
            throw new Error("Cancelled");
          } else {
            // Handle other errors
            throw new Error("An unexpected error occurred");
          }
        }
      },
    }),
  ],
  pages: {
    signIn: "/auth/login",
  },
  session: {
    strategy: "jwt",
  },
  callbacks: {
    async session({ session, token }: { session: any; token: any }) {
      /**disable all typescript error  */
      session.user = session.user || ({} as CustomUser);
      session.user.id = token.id;
      session.user.name = token.name;
      session.user.user_type = token.user_type;

      session.user.access_token = token.accessToken;

      return session;
    },
    async jwt({ token, user }) {
      if (user) {
        token.accessToken = (user as CustomUser).access_token;
        token.id = user.id;
        token.name = user.name;
        token.user_type = (user as CustomUser).user_type;
      }
      return token;
    },
  },
};

export const getAuthSession = () => getServerSession(authOptions);
