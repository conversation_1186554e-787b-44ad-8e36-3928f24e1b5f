import { HydrationBoundary, dehydrate } from "@tanstack/react-query";
import { queryClient } from "@/lib/query-client";
import CommerceProfileParent from "./commerce-profile-parent";
import { Metadata, ResolvingMetadata } from "next";

type Props = {
  params: { id: string };
};

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const profile = params;
  const branchId = profile.id;

  const commerce_response = await fetch(
    process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL +
      "/branches/landing-page/profile-page-details/" +
      branchId
  );
  const commerce_data = await commerce_response.json();
  return {
    title:
      commerce_data?.data?.shopName &&
      commerce_data?.data?.shopName.length > 0 &&
      commerce_data?.data?.branchName &&
      commerce_data?.data?.branchName.length > 0
        ? `${commerce_data?.data?.shopName}, ${commerce_data?.data?.branchName}`
        : "Outlet On Cravin Commerce",
    description:
      commerce_data?.data?.knownFor && commerce_data?.data?.knownFor.length > 0
        ? commerce_data?.data?.knownFor
        : "Outlet Enlisted On Cravin Commerce",
    icons: {
      icon: "/Cravin_logo_favi.png",
    },
    openGraph: {
      title:
        commerce_data?.data?.shopName &&
        commerce_data?.data?.shopName.length > 0 &&
        commerce_data?.data?.branchName &&
        commerce_data?.data?.branchName.length > 0
          ? `${commerce_data?.data?.shopName}, ${commerce_data?.data?.branchName}`
          : "Outlet On Cravin Commerce",
      description:
        commerce_data?.data?.knownFor &&
        commerce_data?.data?.knownFor.length > 0
          ? commerce_data?.data?.knownFor
          : "Outlet Enlisted On Cravin Commerce",
      url: `https://justcravin.com/commerce/${branchId}`,
      type: "website",
    },
    alternates: {
      canonical: "https://justcravin.com",
    },
  };
}

export default async function Page({ params }: { params: { id: string } }) {
  //prefetch will be here
  const profile = params;
  const branchId = profile.id;
  // console.log(branchId);

  await queryClient.prefetchQuery({
    queryKey: ["getCommerceProfileInfo", branchId],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL +
          "/branches/landing-page/profile-page-details/" +
          branchId
      );
      const data = await response.json();
      return data.data;
    },
  });

  await queryClient.prefetchQuery({
    queryKey: ["getCommerceFAQ", branchId],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL +
          "/branches/landing-page/get-branch-faqs/" +
          branchId
      );
      const data = await response.json();
      return data.data;
    },
  });

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <CommerceProfileParent branchId={branchId} />
    </HydrationBoundary>
  );
}
