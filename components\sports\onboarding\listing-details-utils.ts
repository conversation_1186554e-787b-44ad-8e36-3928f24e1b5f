export const handleImageUpload = async (files: FileList | null, isGallery: boolean): Promise<string[]> => {
  if (!files) return [];

  // TODO: Implement actual image upload logic here
  // This is a placeholder for demonstration
  return Array.from(files).map(file => URL.createObjectURL(file));
};

export const addArrayItem = (currentValues: string[], newValue: string): string[] => {
  if (!newValue.trim()) return currentValues;
  return currentValues.includes(newValue.trim())
    ? currentValues
    : [...currentValues, newValue.trim()];
};

export const removeArrayItem = (currentValues: string[], index: number): string[] => {
  return currentValues.filter((_, i) => i !== index);
};

export const removeImage = (currentImages: string[], index: number): string[] => {
  return currentImages.filter((_, i) => i !== index);
};

export const getDefaultValues = () => ({
  average_spend: 0,
  club_tags: [],
  show_in_landing_page: true,
  image_gallery: [],
  listing_image: "",
  club_emirate: "",
  amenities: [],
  about_venue: "",
  latitude: "",
  longitude: "",
  club_landline_numbers: [],
  facility_details: [],
  branch_pricings: [],
  social_links: {
    facebook: "",
    instagram: "",
    twitter: "",
    website: ""
  },
});
