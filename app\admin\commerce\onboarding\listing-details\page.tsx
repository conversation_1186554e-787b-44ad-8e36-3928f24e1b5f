"use client";

import MultipleBranchListing from "@/components/commerce/onboarding/multiple-branch-listing";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { ArrowLeft, Edit, Loader2, Store } from "lucide-react";
import { Button } from "@/components/ui/button";
import { FaWhatsapp } from "react-icons/fa";
import { Suspense } from "react";

function ListingDetailsPage() {
  const searchParams = useSearchParams();
  const shopId = searchParams.get("id") as string;

  return (
    <main className="h-screen flex flex-col bg-gradient-to-b  ">
      <div className="bg-white border-b py-4">
        <div className="max-w-[1400px] mx-auto px-6 flex items-center justify-between">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Link href={`/admin/commerce/onboarding?id=${shopId}`}>
                <Button variant="ghost" size="sm" className="gap-1">
                  <ArrowLeft className="h-4 w-4" />
                  Back to Onboarding
                </Button>
              </Link>
              <h1 className="text-xl font-semibold">Branch Listing Details</h1>
            </div>
          </div>
          <div className="flex flex-wrap gap-2">
            <Link href={`/admin/commerce`}>
              <Button variant="outline" size="sm" className="gap-2">
                <Store className="h-4 w-4" />
                All Shops
              </Button>
            </Link>

            {/* WhatsApp onboarding */}
            <Link href={`/admin/onboard-whatsapp/commerce/${shopId}`}>
              <Button
                variant="outline"
                size="sm"
                className="gap-2 bg-white hover:bg-green-50 text-green-600 border-green-200 hover:border-green-300"
              >
                <FaWhatsapp className="h-3.5 w-3.5" />
                WhatsApp Setup
              </Button>
            </Link>

            {/* Shop details */}
            <Link href={`/admin/commerce/onboarding?id=${shopId}`}>
              <Button variant="outline" size="sm" className="gap-2">
                <Edit className="h-4 w-4" />
                Shop Details
              </Button>
            </Link>
          </div>
        </div>
      </div>
      <div className="max-w-[1400px] mx-auto w-full py-6 px-6">
        <MultipleBranchListing shopId={shopId} />
      </div>
    </main>
  );
}
// add suspense and return fallback UI
export default function MainListingDetailsPage() {
  return (
    <Suspense
      fallback={<Loader2 className="h-8 w-8 animate-spin text-primary" />}
    >
      <ListingDetailsPage />
    </Suspense>
  );
}
