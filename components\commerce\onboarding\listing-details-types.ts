import { z } from "zod";

export const listingFormSchema = z.object({
  branchId: z.string().optional(),
  branchName: z.string().min(1, { message: "Branch name is required" }),
  branch_emirate: z.string().min(1, { message: "Emirate is required" }),
  branch_tags: z.array(z.string()).min(1, { message: "At least one tag is required" }),
  more_info: z.array(z.string()).min(1, { message: "At least one info item is required" }),
  popular_items: z.array(z.string()).min(1, { message: "At least one popular dish is required" }),
  known_for: z.string().min(10, { message: "Please provide a description of what your shop is known for" }),
  average_spend: z.number().min(0, { message: "Average spend must be a positive number" }).optional().default(0),
  show_in_landing_page: z.boolean(),
  image_gallery: z.array(z.string()).min(1, { message: "At least one gallery image is required" }),
  listing_image: z.string().min(1, { message: "Listing image is required" }),
  branch_display_name: z.string().min(1, { message: "Display name is required" }),
  shop_landline_numbers: z.array(z.string()).optional().default([]),
  branch_menus: z.array(
    z.object({
      menuName: z.string().min(1, { message: "Catalog name is required" }),
      menuImages: z.array(z.string()).optional().default([])
    })
  ).optional(),
  social_links: z.object({
    facebook: z.string().url().optional().or(z.literal("")),
    instagram: z.string().url().optional().or(z.literal("")),
    twitter: z.string().url().optional().or(z.literal("")),
    website: z.string().url().optional().or(z.literal(""))
  }).optional().default({})
});

export type ListingFormValues = z.infer<typeof listingFormSchema>;
export interface BranchData {
  branchId: string | undefined;
  branchName: string;
  branch_emirate: string;
  branch_tags: string[];
  more_info: string[];
  popular_items: string[];
  known_for: string;
  average_spend?: number;
  show_in_landing_page: boolean;
  image_gallery: string[];
  listing_image: string;
  branch_display_name: string;
  shop_landline_numbers?: string[];
  branch_menus?: {
    menuName: string;
    menuImages: string[];
  }[];
  social_links?: {
    facebook?: string;
    instagram?: string;
    twitter?: string;
    website?: string;
  };
}
