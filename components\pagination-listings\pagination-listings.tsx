"use client";
import React from "react";
import {
  Pa<PERSON><PERSON>,
  PaginationContent,
  Pagination<PERSON>llipsis,
  PaginationItem,
  PaginationLink,
  PaginationNext,
  PaginationPrevious,
} from "@/components/ui/pagination";

const PaginationListings = ({
  navigateToPage,
  totalPages,
  currentPage,
}: {
  navigateToPage: (pageNumber: number) => void;
  totalPages: number;
  currentPage: number;
}) => {
  return (
    <>
      <Pagination className="mt-5">
        <PaginationContent>
          {/* Here we hardcode the button to send us to first page */}
          <PaginationItem>
            <PaginationLink
              href="#"
              onClick={(e) => {
                e.preventDefault();
                navigateToPage(1);
              }}
              className={`bg-white hover:bg-white text-black hover:text-black
            ${currentPage === 1 ? "pointer-events-none opacity-50" : ""}
          `}
            >
              <span className="sr-only">First page</span>
              <span aria-hidden="true">«</span>
            </PaginationLink>
          </PaginationItem>

          {/* Back button which we will use single arrow for now, if team doesnt like we will make text */}
          <PaginationItem>
            <PaginationLink
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (currentPage > 1) {
                  navigateToPage(currentPage - 1);
                }
              }}
              className={`bg-white hover:bg-white text-black hover:text-black
            ${currentPage === 1 ? "pointer-events-none opacity-50" : ""}
          `}
            >
              <span className="sr-only">Previous</span>
              <span aria-hidden="true">‹</span>
            </PaginationLink>
          </PaginationItem>

          {/*page numbers logic where we show at most 3 pages */}
          {(() => {
            let startPage, endPage;

            if (totalPages <= 3) {
              //show all in this case
              startPage = 1;
              endPage = totalPages;
            } else {
              //this case we will have to calculate range, 3 cases total
              if (currentPage <= 2) {
                //first two pages
                startPage = 1;
                endPage = 3;
              } else if (currentPage >= totalPages - 1) {
                //last two pages
                startPage = totalPages - 2;
                endPage = totalPages;
              } else {
                //middle of the list , i am not sure if this is correct need to test for odd and even cases
                startPage = currentPage - 1;
                endPage = currentPage + 1;
              }
            }

            const pages = [];

            //we put dots to show there is a range there
            if (startPage > 1) {
              pages.push(
                <PaginationItem key="ellipsis-start">
                  <PaginationEllipsis className="bg-white hover:bg-white text-black hover:text-black" />
                </PaginationItem>
              );
            }

            for (let i = startPage; i <= endPage; i++) {
              pages.push(
                <PaginationItem key={i}>
                  <PaginationLink
                    href="#"
                    onClick={(e) => {
                      e.preventDefault();
                      navigateToPage(i);
                    }}
                    className={`${
                      i === currentPage
                        ? `bg-black hover:bg-black text-white hover:text-white`
                        : `bg-white hover:bg-white text-black hover:text-black`
                    }`}
                    isActive={i === currentPage}
                  >
                    {i}
                  </PaginationLink>
                </PaginationItem>
              );
            }

            //dots at the end when we are at the start to show range
            if (endPage < totalPages) {
              pages.push(
                <PaginationItem key="ellipsis-end">
                  <PaginationEllipsis className="bg-white hover:bg-white text-black hover:text-black" />
                </PaginationItem>
              );
            }

            return pages;
          })()}

          {/*next page button*/}
          <PaginationItem>
            <PaginationLink
              href="#"
              onClick={(e) => {
                e.preventDefault();
                if (currentPage < totalPages) {
                  navigateToPage(currentPage + 1);
                }
              }}
              className={`bg-white hover:bg-white text-black hover:text-black
            ${
              currentPage === totalPages ? "pointer-events-none opacity-50" : ""
            }
          `}
            >
              <span className="sr-only">Next</span>
              <span aria-hidden="true">›</span>
            </PaginationLink>
          </PaginationItem>

          {/*direct to last page*/}
          <PaginationItem>
            <PaginationLink
              href="#"
              onClick={(e) => {
                e.preventDefault();
                navigateToPage(totalPages);
              }}
              className={`bg-white hover:bg-white text-black hover:text-black
            ${
              currentPage === totalPages ? "pointer-events-none opacity-50" : ""
            }
          `}
            >
              <span className="sr-only">Last page</span>
              <span aria-hidden="true">»</span>
            </PaginationLink>
          </PaginationItem>
        </PaginationContent>
      </Pagination>
    </>
  );
};

export default PaginationListings;
