import { z } from "zod";

// Define the facility details schema
const facilityDetailsSchema = z.object({
  facilityName: z.string().min(1),
  facilityCategory: z.string().min(1)
});

export type FacilityDetails = z.infer<typeof facilityDetailsSchema>;

export const listingFormSchema = z.object({
  average_spend: z.number().min(0, { message: "Average spend must be a positive number" }),
  club_tags: z.array(z.string()).min(1, { message: "At least one tag is required" }),
  show_in_landing_page: z.boolean(),
  image_gallery: z.array(z.string()).min(1, { message: "At least one gallery image is required" }),
  listing_image: z.string().min(1, { message: "Listing image is required" }),
  club_emirate: z.string().min(1, { message: "Emirate is required" }),
  amenities: z.array(z.string()).min(1, { message: "At least one amenity is required" }),
  about_venue: z.string().min(10, { message: "Please provide a description about your venue" }),
  latitude: z.string().min(1, { message: "Latitude is required" }),
  longitude: z.string().min(1, { message: "Longitude is required" }),
  club_landline_numbers: z.array(z.string()).optional().default([]),
  facility_details: z.array(facilityDetailsSchema).optional().default([]),
  branch_pricings: z.array(
    z.object({
      pricingName: z.string().min(1, { message: "Pricing name is required" }),
      pricingImages: z.array(z.string()).optional().default([])
    })
  ).optional().default([]),
  social_links: z.object({
    facebook: z.string().url().optional().or(z.literal("")),
    instagram: z.string().url().optional().or(z.literal("")),
    twitter: z.string().url().optional().or(z.literal("")),
    website: z.string().url().optional().or(z.literal(""))
  }).optional().default({})
});

export type ListingFormValues = z.infer<typeof listingFormSchema>;
