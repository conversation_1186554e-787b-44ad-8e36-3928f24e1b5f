"use client";
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import {
  Search,
  Plus,
  Filter,
  MoreHorizontal,
  Edit,
  Trash,
  Eye,
  CheckCircle,
  XCircle,
  MessageSquare,
  RefreshCcw,
  Download,
  ArrowUpDown,
  Users,
  CheckSquare,
  Clock,
  Building2,
  MoreVertical,
  Trash2,
  EyeOff,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";

import Link from "next/link";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FaWhatsapp } from "react-icons/fa";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { Loader2, AlertCircle } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import DeleteConfirmationDialog from "@/components/ui/delete-confirmation-dialog";
import { toast } from "sonner";
import { Switch } from "@/components/ui/switch";

// Interface for club data structure
interface Club {
  id: string;
  clubId: string;
  name: string;
  whatsappNumber: string;
  isWhatsappOnboardingCompleted: boolean;
  addedOn: string;
  is_only_for_listing?: boolean;
  is_visible?: boolean; // Add visibility property
}

// Function to fetch clubs from API
const fetchClubs = async (token: string | undefined) => {
  const response = await fetch(
    `${
      process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL ||
      "https://cravin-backend-server.onrender.com"
    }/clubs/onboarding-dashboard/clubs-list`,
    {
      headers: {
        accept: "application/json",
        Authorization: token ? `Bearer ${token}` : "",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch clubs");
  }

  const data = await response.json();
  return data.data;
};

// Add function to toggle club visibility
const toggleClubVisibility = async (clubId: string, isVisible: boolean, token: string) => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/clubs/toggle-visibility/${clubId}`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ is_visible: isVisible }),
      }
    );

    if (!response.ok) {
      throw new Error("Failed to update club visibility");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

const ClubPage = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [filter, setFilter] = useState("");
  const [listingTypeFilter, setListingTypeFilter] = useState<string>("all"); // Add new filter state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [clubToDelete, setClubToDelete] = useState<Club | null>(null);

  const { data: session } = useSession();
  const accessToken = session?.user.access_token;

  const queryClient = useQueryClient();

  // Fetch clubs with React Query
  const {
    data: clubsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["clubs", accessToken],
    queryFn: () => fetchClubs(accessToken),
    enabled: !!accessToken,
  });

  // Delete club mutation
  const { mutate: deleteClub, isPending: isDeleting } = useMutation({
    mutationFn: async (clubId: string) => {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/clubs/delete/${clubId}`,
        {
          method: "DELETE",
          headers: {
            accept: "application/json",
            Authorization: accessToken ? `Bearer ${accessToken}` : "",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete club");
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success("Club deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["clubs"] });
      setDeleteDialogOpen(false);
      setClubToDelete(null);
    },
    onError: (error) => {
      toast.error(`Error deleting club: ${error.message}`);
      setDeleteDialogOpen(false);
    },
  });

  // Mutation for toggling visibility
  const { mutate: updateVisibility } = useMutation({
    mutationFn: async ({ clubId, isVisible }: { clubId: string; isVisible: boolean }) => {
      return toggleClubVisibility(clubId, isVisible, accessToken || "");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["clubs"] });
      toast.success("Club visibility updated successfully");
    },
    onError: (error) => {
      toast.error(`Error updating club visibility: ${error.message}`);
    },
  });

  const handleDeleteClick = (club: Club) => {
    setClubToDelete(club);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (clubToDelete) {
      deleteClub(clubToDelete.id);
    }
  };

  const handleVisibilityToggle = (club: Club, newVisibility: boolean) => {
    updateVisibility({ clubId: club.id, isVisible: newVisibility });
  };

  // Use fetched data if available, otherwise empty array
  const allClubs = clubsData || [];

  // Filter clubs based on search term, active tab, and listing type
  const filteredClubs = allClubs.filter((club: Club) => {
    const matchesSearch =
      filter === "" || club.name.toLowerCase().includes(filter.toLowerCase());

    // Filter by tab (onboarding status)
    let matchesTab = true;
    if (activeTab === "onboarded") {
      matchesTab = club.isWhatsappOnboardingCompleted;
    } else if (activeTab === "pending") {
      matchesTab = !club.isWhatsappOnboardingCompleted;
    }

    // Filter by listing type
    let matchesListingType = true;
    if (listingTypeFilter === "cravin_users") {
      matchesListingType = !club.is_only_for_listing;
    } else if (listingTypeFilter === "only_listing") {
      matchesListingType = !!club.is_only_for_listing;
    }

    return matchesSearch && matchesTab && matchesListingType;
  });

  // Count for each category
  const onboardedCount = allClubs.filter(
    (club: Club) => club.isWhatsappOnboardingCompleted
  ).length;
  const pendingCount = allClubs.filter(
    (club: Club) => !club.isWhatsappOnboardingCompleted
  ).length;

  // Pagination logic
  const totalPages = Math.ceil(filteredClubs.length / itemsPerPage);

  const paginatedClubs = filteredClubs.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Handle loading state
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 flex items-center justify-center h-[50vh]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading clubs data...</p>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load sports clubs data. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 pb-4 border-b">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Club Management</h1>
          <p className="text-muted-foreground mt-2 text-base">
            Manage and monitor all clubs in the Cravin platform
          </p>
        </div>
        <div className="flex gap-3 self-end sm:self-auto">
          <Link href="/admin/sports/onboarding">
            <Button className="gap-2 shadow-sm">
              <Plus size={16} />
              Onboard New Club
            </Button>
          </Link>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
          <TabsList className="w-full sm:w-auto bg-muted/60 p-1 rounded-lg">
            <TabsTrigger
              value="all"
              className="flex gap-2 rounded data-[state=active]:bg-background data-[state=active]:shadow-sm"
            >
              All
              <Badge variant="secondary" className="ml-1 bg-primary/10">
                {allClubs.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger
              value="onboarded"
              className="flex gap-2 rounded data-[state=active]:bg-background data-[state=active]:shadow-sm"
            >
              Onboarded
              <Badge
                variant="secondary"
                className="ml-1 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
              >
                {onboardedCount}
              </Badge>
            </TabsTrigger>
            <TabsTrigger
              value="pending"
              className="flex gap-2 rounded data-[state=active]:bg-background data-[state=active]:shadow-sm"
            >
              Pending
              <Badge
                variant="secondary"
                className="ml-1 bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400"
              >
                {pendingCount}
              </Badge>
            </TabsTrigger>
          </TabsList>

          <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search clubs..."
                className="pl-9 w-full border-muted bg-background focus-visible:ring-1"
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
              />
            </div>

            {/* Add listing type filter */}
            <Select
              value={listingTypeFilter}
              onValueChange={(value) => {
                setListingTypeFilter(value);
                setCurrentPage(1); // Reset to first page when filter changes
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="cravin_users">Cravin Users</SelectItem>
                <SelectItem value="only_listing">Only For Listings</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <TabsContent value="all" className="m-0 mt-0">
          <ClubsTable
            clubs={paginatedClubs}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={filteredClubs.length}
            onPreviousPage={() =>
              setCurrentPage((prev) => Math.max(prev - 1, 1))
            }
            onNextPage={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            setFilter={setFilter}
            onDeleteClick={handleDeleteClick}
            setCurrentPage={setCurrentPage}
            setListingTypeFilter={setListingTypeFilter}
            onVisibilityToggle={handleVisibilityToggle}
          />
        </TabsContent>

        <TabsContent value="onboarded" className="m-0 mt-0">
          <ClubsTable
            clubs={paginatedClubs}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={filteredClubs.length}
            onPreviousPage={() =>
              setCurrentPage((prev) => Math.max(prev - 1, 1))
            }
            onNextPage={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            setFilter={setFilter}
            onDeleteClick={handleDeleteClick}
            setCurrentPage={setCurrentPage}
            setListingTypeFilter={setListingTypeFilter}
            onVisibilityToggle={handleVisibilityToggle}
          />
        </TabsContent>

        <TabsContent value="pending" className="m-0 mt-0">
          <ClubsTable
            clubs={paginatedClubs}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={filteredClubs.length}
            onPreviousPage={() =>
              setCurrentPage((prev) => Math.max(prev - 1, 1))
            }
            onNextPage={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            setFilter={setFilter}
            onDeleteClick={handleDeleteClick}
            setCurrentPage={setCurrentPage}
            setListingTypeFilter={setListingTypeFilter}
            onVisibilityToggle={handleVisibilityToggle}
          />
        </TabsContent>
      </Tabs>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Club"
        description={`This club will be deleted, along with all of its Courts, Facilities, and Settings.`}
        onConfirm={confirmDelete}
        isLoading={isDeleting}
        confirmText="delete my club"
      />
    </div>
  );
};

// Separated table component with pagination
const ClubsTable = ({
  clubs,
  currentPage,
  totalPages,
  totalItems,
  onPreviousPage,
  onNextPage,
  setFilter,
  onDeleteClick,
  setCurrentPage,
  setListingTypeFilter,
  onVisibilityToggle,
}: {
  clubs: Club[];
  currentPage: number;
  totalPages: number;
  totalItems: number;
  onPreviousPage: () => void;
  onNextPage: () => void;
  setFilter: (value: string) => void;
  onDeleteClick: (club: Club) => void;
  setCurrentPage: (page: number) => void;
  setListingTypeFilter: (value: string) => void;
  onVisibilityToggle: (club: Club, isVisible: boolean) => void;
}) => {
  return (
    <Card className="shadow-sm border-muted overflow-hidden">
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/30 hover:bg-muted/30">
                <TableHead className="w-[250px] font-semibold">
                  <div className="flex items-center gap-1">
                    Club Name
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 ml-1 rounded-full"
                    >
                      <ArrowUpDown
                        size={14}
                        className="text-muted-foreground"
                      />
                    </Button>
                  </div>
                </TableHead>

                <TableHead className="font-semibold">
                  WhatsApp Onboarding
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-1">
                    Added On
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 ml-1 rounded-full"
                    >
                      <ArrowUpDown
                        size={14}
                        className="text-muted-foreground"
                      />
                    </Button>
                  </div>
                </TableHead>
                {/* <TableHead className="font-semibold text-center">
                  Turn OFF Customer Side
                </TableHead> */}
                <TableHead className="text-right font-semibold">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {clubs.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={8}
                    className="text-center py-12 text-muted-foreground h-[200px]"
                  >
                    <div className="flex flex-col items-center gap-2">
                      <Search className="h-8 w-8 text-muted-foreground/60" />
                      <p>No sports clubs found matching your criteria</p>
                      <Button
                        variant="link"
                        className="mt-2"
                        onClick={() => {
                          setFilter("");
                          setCurrentPage(1);
                          setListingTypeFilter("all");
                        }}
                      >
                        Clear filters
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                clubs.map((club) => (
                  <TableRow key={club.id} className="group">
                    <TableCell className="font-medium">
                      <div className="flex flex-col">
                        <span className="text-base">{club.name}</span>
                        <span className="text-xs text-muted-foreground mt-1">
                          ID: {club.clubId}
                        </span>
                      </div>
                    </TableCell>

                    <TableCell>
                      {club.isWhatsappOnboardingCompleted ? (
                        <div className="flex items-center gap-2">
                          <Badge
                            variant="outline"
                            className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950/30 dark:text-green-400 dark:border-green-800 flex items-center gap-1.5 py-1.5"
                          >
                            <CheckCircle className="h-3.5 w-3.5" />
                            <span>Completed</span>
                          </Badge>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Badge
                            variant="outline"
                            className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950/30 dark:text-amber-400 dark:border-amber-800 flex items-center gap-1.5 py-1.5"
                          >
                            <Clock className="h-3.5 w-3.5" />
                            <span>Pending</span>
                          </Badge>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(club.addedOn).toLocaleDateString("en-US", {
                        year: "numeric",
                        month: "short",
                        day: "numeric",
                      })}
                    </TableCell>
                    {/* <TableCell className="text-center">
                      <div className="flex items-center justify-center gap-2">
                        <span className="text-sm text-muted-foreground mr-2">
                          {club.is_visible !== false ? (
                            <Eye className="h-4 w-4 text-green-600" />
                          ) : (
                            <EyeOff className="h-4 w-4 text-gray-400" />
                          )}
                        </span>
                        <Switch
                          checked={club.is_visible !== false}
                          onCheckedChange={(checked) => onVisibilityToggle(club, checked)}
                          className="data-[state=checked]:bg-green-500"
                        />
                      </div>
                    </TableCell> */}
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        {!club.isWhatsappOnboardingCompleted ? (
                          <Link
                            href={`/admin/onboard-whatsapp/sports/${club.id}`}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 shadow-sm bg-white hover:bg-green-50 text-green-600 border-green-200 hover:border-green-300"
                            >
                              <FaWhatsapp className="h-3.5 w-3.5 mr-1" />
                              Onboard WhatsApp
                            </Button>
                          </Link>
                        ) : (
                          <Link
                            href={`/admin/onboard-whatsapp/profile/sports/${club.id}`}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 shadow-sm bg-white hover:bg-green-50 text-green-600 border-green-200 hover:border-green-300"
                            >
                              <FaWhatsapp className="h-3.5 w-3.5 mr-1" />
                              Edit WhatsApp
                            </Button>
                          </Link>
                        )}

                        <Link
                          href={`/admin/sports/onboarding/listing-details?id=${club.id}`}
                        >
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 shadow-sm bg-white hover:bg-orange-50 text-orange-600 border-orange-200 hover:border-orange-300"
                          >
                            <Building2 className="h-3.5 w-3.5 mr-1" />
                            Court Listings
                          </Button>
                        </Link>

                        <Link href={`/admin/sports/onboarding?id=${club.id}`}>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 shadow-sm hover:bg-muted"
                          >
                            <Edit className="h-3.5 w-3.5 mr-1" />
                            Edit Details
                          </Button>
                        </Link>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              className="text-destructive focus:text-destructive flex items-center cursor-pointer"
                              onClick={() => onDeleteClick(club)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete Club
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
        <div className="flex items-center justify-between px-6 py-4 border-t">
          <div className="text-sm text-muted-foreground">
            Showing <strong>{clubs.length}</strong> of{" "}
            <strong>{totalItems}</strong> clubs
          </div>
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage <= 1}
              onClick={onPreviousPage}
              className="h-8 px-3"
            >
              Previous
            </Button>
            {totalPages > 0 && (
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-4 bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground"
              >
                {currentPage}
              </Button>
            )}
            {totalPages > 1 && currentPage < totalPages && (
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-4"
                onClick={() => onNextPage()}
              >
                {currentPage + 1}
              </Button>
            )}
            {totalPages > 2 && currentPage < totalPages - 1 && (
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-4"
                onClick={() => onNextPage()}
              >
                {currentPage + 2}
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage >= totalPages || totalPages === 0}
              onClick={onNextPage}
              className="h-8 px-3"
            >
              Next
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ClubPage;
