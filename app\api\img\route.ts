import crypto from "crypto";
import { NextRequest, NextResponse } from "next/server";

function signPath(path: string): string {
  const key = process.env.IMG_PROXY_KEY!;
  const salt = process.env.IMG_PROXY_SALT!;

  const keyBin = Buffer.from(key, "hex");
  const saltBin = Buffer.from(salt, "hex");

  const hmac = crypto.createHmac("sha256", keyBin);
  hmac.update(saltBin);
  hmac.update(path);

  return hmac.digest("base64url");
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const acceptHeader = request.headers.get("accept") || "";
    const imageUrl = searchParams.get("url");
    const width = searchParams.get("w") || "800";
    const height = searchParams.get("h") || "0";
    const quality = searchParams.get("q") || "80";

    if (!imageUrl) {
      return new NextResponse("Missing image URL", { status: 400 });
    }

    const encodedUrl = Buffer.from(imageUrl).toString("base64url");
    const resizeType = height === "0" ? "fit" : "fill";
    let format = "jpeg";
    if (acceptHeader.includes("image/avif")) {
      format = "avif";
    } else if (acceptHeader.includes("image/webp")) {
      format = "webp";
    } else if (acceptHeader.includes("image/jpeg")) {
      format = "jpeg";
    }

    const path = `/g:sm/f:${format}/q:${quality}/rs:${resizeType}:${width}:${height}:0/${encodedUrl}`;
    const signature = signPath(path);
    const imgProxyUrl = `${process.env.IMG_PROXY_URL}/${signature}${path}`;

    // console.log('Original URL:', imageUrl);
    // console.log('Path:', path);
    // console.log('Signature:', signature);
    // console.log('Final ImgProxy URL:', imgProxyUrl);

    const response = await fetch(imgProxyUrl);

    if (!response.ok) {
      const errorText = await response.text();
      console.error("ImgProxy error:", response.status, errorText);
      return new NextResponse(
        `Failed to fetch image: ${response.status} - ${errorText}`,
        { status: response.status }
      );
    }

    const imageData = await response.arrayBuffer();

    return new NextResponse(imageData, {
      headers: {
        "Content-Type": `image/${format}`,
        "Cache-Control": "public, max-age=31536000, immutable",
      },
    });
  } catch (error: any) {
    console.error("ImgProxy error:", error);
    return new NextResponse(`Internal Server Error: ${error.message}`, {
      status: 500,
    });
  }
}
