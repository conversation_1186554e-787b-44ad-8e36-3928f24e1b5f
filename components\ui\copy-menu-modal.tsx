import React, { useState, useEffect, useRef } from "react";
import { Button } from "@/components/ui/button";
import {
  Loader2,
  AlertCircle,
  Check,
  Copy,
  ArrowRight,
  X,
  Info,
} from "lucide-react";
import { toast } from "sonner";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { Separator } from "@/components/ui/separator";
import { Checkbox } from "@/components/ui/checkbox";

interface Branch {
  branch_id: string;
  branch_name: string;
}

interface BranchCopyStatus {
  branch_id: string;
  branch_name: string;
  status: "pending" | "copying" | "success" | "error";
  message?: string;
  itemsCount?: number;
  categoriesCount?: number;
}

interface CopyMenuModalProps {
  isOpen: boolean;
  onClose: () => void;
  product: {
    id: string;
    name: string;
  };
  accessToken: string;
  productType: string;
}

const CopyMenuModal = ({
  isOpen,
  onClose,
  product,
  accessToken,
  productType,
}: CopyMenuModalProps) => {
  const [isLoading, setIsLoading] = useState(false);
  const [branches, setBranches] = useState<Branch[]>([]);
  const [selectedSourceBranchId, setSelectedSourceBranchId] =
    useState<string>("");
  const [selectedTargetBranchIds, setSelectedTargetBranchIds] = useState<
    string[]
  >([]);
  const [isLoadingBranches, setIsLoadingBranches] = useState(true);
  const [copyStatus, setCopyStatus] = useState<BranchCopyStatus[]>([]);
  const [copyProgress, setCopyProgress] = useState(0);
  const [showResults, setShowResults] = useState(false);
  const modalRef = useRef<HTMLDivElement>(null);

  // Fetch branches for the product
  useEffect(() => {
    const fetchBranches = async () => {
      if (!isOpen || !product.id) return;

      setIsLoadingBranches(true);
      try {
        const response = await fetch(
          `${
            productType === "food"
              ? process.env.NEXT_PUBLIC_FOOD_BACKEND_URL
              : process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL
          }/${productType === "food" ? "restaurants" : "shops"}/${
            product.id
          }/branches`,
          {
            headers: {
              accept: "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch branches");
        }

        const data = await response.json();
        console.log("🚀 ~ fetchBranches ~ data:", data);
        setBranches(data.data || []);
      } catch (error) {
        toast.error("Failed to load branches");
        console.error(error);
      } finally {
        setIsLoadingBranches(false);
      }
    };

    fetchBranches();
  }, [product.id, isOpen, accessToken]);

  // Reset selections when dialog opens
  useEffect(() => {
    if (isOpen) {
      setSelectedSourceBranchId("");
      setSelectedTargetBranchIds([]);
      setCopyStatus([]);
      setShowResults(false);
      setCopyProgress(0);
    }
  }, [isOpen]);

  // Handle copy menu operation
  const handleCopyMenu = async () => {
    if (!selectedSourceBranchId || selectedTargetBranchIds.length === 0) {
      toast.error("Please select both source and at least one target branch");
      return;
    }

    if (selectedTargetBranchIds.includes(selectedSourceBranchId)) {
      toast.error("Source and target branches cannot be the same");
      return;
    }

    setIsLoading(true);

    // Initialize copy status for all target branches
    const initialStatus = selectedTargetBranchIds.map((branchId) => ({
      branch_id: branchId,
      branch_name:
        branches.find((b) => b.branch_id === branchId)?.branch_name || "",
      status: "pending" as const,
    }));

    setCopyStatus(initialStatus);
    setShowResults(true);

    let successCount = 0;
    let errorCount = 0;

    // Process branches one by one
    for (let i = 0; i < selectedTargetBranchIds.length; i++) {
      const targetBranchId = selectedTargetBranchIds[i];
      const branchName =
        branches.find((b) => b.branch_id === targetBranchId)?.branch_name || "";

      // Update status to copying for current branch
      setCopyStatus((prev) =>
        prev.map((item) =>
          item.branch_id === targetBranchId
            ? { ...item, status: "copying" as const }
            : item
        )
      );

      try {
        const response = await fetch(
          `${
            productType === "food"
              ? process.env.NEXT_PUBLIC_FOOD_BACKEND_URL
              : process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL
          }/branches/copy-menu/${selectedSourceBranchId}/${targetBranchId}`,
          {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${accessToken}`,
            },
          }
        );

        if (!response.ok) {
          const errorData = await response.json();
          throw new Error(errorData.message || "Failed to copy menu");
        }

        // Parse response to get item and category counts
        const responseData = await response.json();
        const itemsCount = responseData.data.itemsCount || 0;
        const categoriesCount = responseData.data.categoriesCount || 0;

        // Update status to success for this branch with count data
        setCopyStatus((prev) =>
          prev.map((item) =>
            item.branch_id === targetBranchId
              ? {
                  ...item,
                  status: "success" as const,
                  itemsCount,
                  categoriesCount,
                }
              : item
          )
        );

        successCount++;
      } catch (error) {
        // Update status to error for this branch
        setCopyStatus((prev) =>
          prev.map((item) =>
            item.branch_id === targetBranchId
              ? {
                  ...item,
                  status: "error" as const,
                  message:
                    error instanceof Error ? error.message : "Unknown error",
                }
              : item
          )
        );

        errorCount++;
      }

      // Update progress percentage
      setCopyProgress(
        Math.round(((i + 1) / selectedTargetBranchIds.length) * 100)
      );
    }

    // Show final toast message
    if (errorCount === 0) {
      toast.success(
        `Menu copied successfully to ${successCount} branch${
          successCount !== 1 ? "es" : ""
        }`
      );
    } else if (successCount === 0) {
      toast.error("Failed to copy menu to any branches");
    } else {
      toast.warning(
        `Copied to ${successCount} branch${
          successCount !== 1 ? "es" : ""
        } with ${errorCount} error${errorCount !== 1 ? "s" : ""}`
      );
    }

    // Don't close the dialog automatically so user can see results
    setIsLoading(false);
  };

  // Function to close the dialog after copy operation
  const handleClose = () => {
    if (!isLoading) {
      onClose();
    }
  };

  // Toggle target branch selection
  const toggleTargetBranch = (branchId: string) => {
    setSelectedTargetBranchIds((prev) =>
      prev.includes(branchId)
        ? prev.filter((id) => id !== branchId)
        : [...prev, branchId]
    );
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center">
      <div className="fixed inset-0 bg-black/60 backdrop-blur-sm pointer-events-none" />
      <div
        ref={modalRef}
        className="relative max-w-4xl w-full bg-white dark:bg-zinc-900 text-gray-900 dark:text-gray-100 rounded-lg overflow-hidden shadow-xl transform transition-all animate-in fade-in duration-200"
        onClick={(e) => e.stopPropagation()}
      >
        {/* Header */}
        <div className="px-6 py-5 border-b border-gray-200 dark:border-zinc-800 bg-muted/30">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3.5">
              <div className="p-2 rounded-lg bg-primary/10 shadow-sm">
                <Copy className="h-5 w-5 text-primary" />
              </div>
              <div>
                <h2 className="text-xl font-semibold">Copy Menu</h2>
                <p className="text-sm text-muted-foreground mt-0.5 max-w-[260px] truncate">
                  {product.name}
                </p>
              </div>
            </div>
            <Button
              variant="ghost"
              size="icon"
              className="h-8 w-8 p-0 rounded-full hover:bg-muted"
              onClick={onClose}
              disabled={isLoading}
            >
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </div>
        </div>

        {/* Content */}
        <div className="px-6 py-5">
          {isLoadingBranches ? (
            <div className="py-12 flex flex-col items-center justify-center gap-3">
              <div className="relative w-14 h-14 flex items-center justify-center">
                <div className="absolute inset-0 border-4 border-muted rounded-full"></div>
                <div className="absolute inset-0 border-4 border-t-primary border-r-primary border-primary/0 border-l-primary/0 rounded-full animate-spin"></div>
                <Loader2 className="h-6 w-6 animate-spin text-primary" />
              </div>
              <p className="text-sm text-muted-foreground">
                Loading branch information...
              </p>
            </div>
          ) : branches.length < 2 ? (
            <div className="bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-900/50 rounded-lg p-4 flex items-start gap-3">
              <AlertCircle className="h-5 w-5 text-amber-500 dark:text-amber-400 mt-0.5 flex-shrink-0" />
              <div className="text-sm text-amber-800 dark:text-amber-300">
                <p className="font-medium mb-1">Unable to Copy Menu</p>
                <p className="opacity-90">
                  At least two branches are required to perform a menu copy
                  operation.
                </p>
              </div>
            </div>
          ) : showResults ? (
            <div className="space-y-6">
              {/* Copy Progress */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>
                    Copying menu from branch:{" "}
                    <span className="font-medium">
                      {
                        branches.find(
                          (b) => b.branch_id === selectedSourceBranchId
                        )?.branch_name
                      }
                    </span>
                  </span>
                  <span>{copyProgress}% complete</span>
                </div>
                <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2.5">
                  <div
                    className="bg-primary h-2.5 rounded-full transition-all duration-300 ease-out"
                    style={{ width: `${copyProgress}%` }}
                  ></div>
                </div>
              </div>

              {/* Branch Copy Status List */}
              <div className="border rounded-md overflow-hidden">
                <div className="p-3 bg-muted/70 border-b">
                  <h3 className="font-medium">Copy Status</h3>
                </div>
                <ScrollArea className="h-[280px]">
                  <div className="divide-y">
                    {copyStatus.map((item) => (
                      <div
                        key={item.branch_id}
                        className="p-3 flex items-center justify-between"
                      >
                        <div className="flex items-center">
                          <div className="mr-3">
                            {item.status === "pending" && (
                              <div className="h-4 w-4 rounded-full border-2 border-gray-300 dark:border-gray-600"></div>
                            )}
                            {item.status === "copying" && (
                              <Loader2 className="h-4 w-4 text-primary animate-spin" />
                            )}
                            {item.status === "success" && (
                              <Check className="h-4 w-4 text-green-500" />
                            )}
                            {item.status === "error" && (
                              <AlertCircle className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                          <div>
                            <span className="font-medium">
                              {item.branch_name}
                            </span>
                            {item.status === "success" &&
                              item.itemsCount !== undefined && (
                                <p className="text-xs text-muted-foreground">
                                  <span className="text-primary font-medium">
                                    {item.itemsCount}
                                  </span>{" "}
                                  items,
                                  <span className="text-primary font-medium ml-1">
                                    {item.categoriesCount}
                                  </span>{" "}
                                  categories copied
                                </p>
                              )}
                          </div>
                        </div>
                        <div>
                          {item.status === "pending" && (
                            <span className="text-xs text-muted-foreground">
                              Waiting...
                            </span>
                          )}
                          {item.status === "copying" && (
                            <span className="text-xs text-primary">
                              Copying...
                            </span>
                          )}
                          {item.status === "success" && (
                            <span className="text-xs text-green-500">
                              Completed
                            </span>
                          )}
                          {item.status === "error" && (
                            <span className="text-xs text-red-500">
                              {item.message || "Failed"}
                            </span>
                          )}
                        </div>
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </div>

              {copyProgress === 100 && (
                <div className="bg-muted/40 border rounded-lg p-4 text-sm">
                  <div className="flex items-center gap-2 mb-2">
                    {copyStatus.every((item) => item.status === "success") ? (
                      <Check className="h-5 w-5 text-green-500" />
                    ) : copyStatus.some((item) => item.status === "success") ? (
                      <Info className="h-5 w-5 text-amber-500" />
                    ) : (
                      <AlertCircle className="h-5 w-5 text-red-500" />
                    )}
                    <span className="font-medium">
                      {copyStatus.every((item) => item.status === "success")
                        ? "All branches copied successfully!"
                        : copyStatus.some((item) => item.status === "success")
                        ? "Copy operation completed with some issues"
                        : "Copy operation failed for all branches"}
                    </span>
                  </div>
                  <p className="text-muted-foreground">
                    {
                      copyStatus.filter((item) => item.status === "success")
                        .length
                    }{" "}
                    of {copyStatus.length} branches updated successfully.
                  </p>
                  {copyStatus.some(
                    (item) =>
                      item.status === "success" && item.itemsCount !== undefined
                  ) && (
                    <p className="text-muted-foreground mt-1">
                      Total:{" "}
                      {copyStatus.reduce(
                        (sum, item) => sum + (item.itemsCount || 0),
                        0
                      )}{" "}
                      items and{" "}
                      {copyStatus.reduce(
                        (sum, item) => sum + (item.categoriesCount || 0),
                        0
                      )}{" "}
                      categories copied
                    </p>
                  )}
                </div>
              )}
            </div>
          ) : (
            <div>
              <div className="bg-muted/40 border rounded-lg p-4 flex items-start gap-3 mb-5">
                <Info className="h-5 w-5 text-muted-foreground mt-0.5 flex-shrink-0" />
                <div className="text-sm">
                  <p className="font-medium mb-1">Copy Menu Instructions</p>
                  <p className="text-muted-foreground">
                    Select a source branch to copy from, then select one or more
                    target branches to copy to.
                  </p>
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Left Column: Source Branch Selection */}
                <div>
                  <h3 className="text-sm font-medium mb-2.5 flex items-center">
                    <span className="bg-primary/10 text-primary text-xs rounded-full h-5 w-5 flex items-center justify-center mr-2">
                      1
                    </span>
                    Source Branch (Copy from)
                  </h3>
                  <div className="bg-card border rounded-md overflow-hidden h-[280px]">
                    <ScrollArea className="h-full">
                      <div className="divide-y">
                        {branches.map((branch) => {
                          return (
                            <div
                              key={branch.branch_id}
                              className={cn(
                                "relative flex items-center px-4 py-3",
                                selectedSourceBranchId === branch.branch_id
                                  ? "bg-primary/5"
                                  : "",
                                "cursor-pointer hover:bg-muted transition-colors"
                              )}
                              onClick={() => {
                                setSelectedSourceBranchId(branch.branch_id);
                                setSelectedTargetBranchIds([]);
                              }}
                            >
                              <div
                                className={cn(
                                  "h-4 w-4 rounded-full border flex items-center justify-center mr-3 flex-shrink-0",
                                  selectedSourceBranchId === branch.branch_id
                                    ? "border-primary"
                                    : "border-input"
                                )}
                              >
                                {selectedSourceBranchId ===
                                  branch.branch_id && (
                                  <div className="h-2 w-2 rounded-full bg-primary" />
                                )}
                              </div>
                              <div className="flex-1">
                                <p
                                  className={cn(
                                    "font-medium text-sm",
                                    selectedSourceBranchId === branch.branch_id
                                      ? "text-primary"
                                      : ""
                                  )}
                                >
                                  {branch.branch_name}
                                </p>
                              </div>
                              {selectedSourceBranchId === branch.branch_id && (
                                <Check className="h-4 w-4 text-primary ml-2" />
                              )}
                            </div>
                          );
                        })}
                      </div>
                    </ScrollArea>
                  </div>
                </div>

                {/* Right Column: Target Branch Selection */}
                <div>
                  <h3
                    className={cn(
                      "text-sm font-medium mb-2.5 flex items-center",
                      !selectedSourceBranchId && "opacity-50"
                    )}
                  >
                    <span className="bg-primary/10 text-primary text-xs rounded-full h-5 w-5 flex items-center justify-center mr-2">
                      2
                    </span>
                    Target Branches (Copy to)
                    {selectedTargetBranchIds.length > 0 && (
                      <span className="ml-2 bg-primary/10 text-primary text-xs px-2 py-0.5 rounded-full">
                        {selectedTargetBranchIds.length} selected
                      </span>
                    )}
                  </h3>
                  <div
                    className={cn(
                      "bg-card border rounded-md overflow-hidden h-[280px] transition-colors",
                      !selectedSourceBranchId && "opacity-70"
                    )}
                  >
                    <ScrollArea className="h-full">
                      <div className="divide-y">
                        {branches
                          .filter(
                            (branch) =>
                              branch.branch_id !== selectedSourceBranchId
                          )
                          .map((branch) => {
                            const isDisabled =
                              !selectedSourceBranchId ||
                              branch.branch_id === selectedSourceBranchId;
                            const isSelected = selectedTargetBranchIds.includes(
                              branch.branch_id
                            );

                            return (
                              <div
                                key={branch.branch_id}
                                className={cn(
                                  "relative flex items-center px-4 py-3",
                                  isSelected && "bg-primary/5",
                                  isDisabled
                                    ? "opacity-50 cursor-not-allowed"
                                    : "cursor-pointer hover:bg-muted transition-colors"
                                )}
                                onClick={() =>
                                  !isDisabled &&
                                  toggleTargetBranch(branch.branch_id)
                                }
                              >
                                <div className="mr-3 flex-shrink-0">
                                  <Checkbox
                                    checked={isSelected}
                                    disabled={isDisabled}
                                    className={cn(
                                      isSelected &&
                                        "border-primary bg-primary text-primary-foreground"
                                    )}
                                  />
                                </div>
                                <div className="flex-1">
                                  <p
                                    className={cn(
                                      "font-medium text-sm",
                                      isSelected ? "text-primary" : ""
                                    )}
                                  >
                                    {branch.branch_name}
                                    {branch.branch_id ===
                                      selectedSourceBranchId && (
                                      <span className="text-xs text-muted-foreground ml-2">
                                        (Source branch)
                                      </span>
                                    )}
                                  </p>
                                </div>
                              </div>
                            );
                          })}
                      </div>
                    </ScrollArea>
                  </div>
                </div>
              </div>

              <div className="mt-6 bg-amber-50 dark:bg-amber-950/20 border border-amber-200 dark:border-amber-800/30 rounded-lg p-4">
                <div className="flex gap-3">
                  <AlertCircle className="h-5 w-5 text-amber-500 dark:text-amber-400 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-amber-800 dark:text-amber-300">
                    <p className="font-medium mb-1.5">Important Warning</p>
                    <p className="opacity-90 leading-relaxed">
                      This action will copy all menu items in the target
                      branch(es) with the menu from the source branch. This
                      operation cannot be undone.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>

        {/* Footer */}
        <div className="border-t border-gray-200 dark:border-zinc-800 px-6 py-4 flex items-center justify-end gap-3 bg-muted/50">
          <Button
            variant="outline"
            onClick={handleClose}
            disabled={isLoading}
            className="font-medium"
          >
            {showResults && copyProgress === 100 ? "Close" : "Cancel"}
          </Button>

          {!showResults && (
            <Button
              variant="default"
              onClick={handleCopyMenu}
              disabled={
                isLoading ||
                isLoadingBranches ||
                branches.length < 2 ||
                !selectedSourceBranchId ||
                selectedTargetBranchIds.length === 0 ||
                selectedTargetBranchIds.includes(selectedSourceBranchId)
              }
              className="min-w-[100px] font-medium"
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Copying...
                </>
              ) : (
                `Copy to ${selectedTargetBranchIds.length} branch${
                  selectedTargetBranchIds.length !== 1 ? "es" : ""
                }`
              )}
            </Button>
          )}

          {showResults && copyProgress === 100 && (
            <Button
              variant="default"
              onClick={handleClose}
              className="min-w-[100px] font-medium"
            >
              Done
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default CopyMenuModal;
