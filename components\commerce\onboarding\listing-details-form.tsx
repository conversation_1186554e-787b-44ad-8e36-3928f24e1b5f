"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { X, Plus, Upload, Loader2, Info, Check } from "lucide-react";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";

import {
  listingFormSchema,
  type ListingFormValues,
} from "./listing-details-types";
import {
  addArrayItem,
  removeArrayItem,
  removeImage,
  getDefaultValues,
} from "./listing-details-utils";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import Image from "next/image";

interface ListingDetailsFormProps {
  shopId: string;
  onSubmit: (data: ListingFormValues) => Promise<void>;
  initialData?: ListingFormValues;
}

// Add this helper function
const uploadImage = async (file: File, shopId: string, token: string) => {
  const formData = new FormData();
  formData.append("file", file);

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL}/common/upload-images/${shopId}`,
    {
      method: "POST",
      body: formData,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );

  if (!response.ok) {
    toast.error("Failed to upload image");
    throw new Error("Failed to upload image");
  }

  const jsonResponse = await response.json();
  return jsonResponse.message;
};

export default function ListingDetailsForm({
  shopId,
  onSubmit,
  initialData,
}: ListingDetailsFormProps) {
  const session = useSession();
  const token = session.data?.user?.access_token;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newTag, setNewTag] = useState("");
  const [newInfo, setNewInfo] = useState("");
  const [newDish, setNewDish] = useState("");
  const [uploadingImages, setUploadingImages] = useState(false);
  const [uploadingCatalogImages, setUploadingCatalogImages] = useState(false);
  const [newLandlineNumber, setNewLandlineNumber] = useState("");
  const [isDeletingImage, setIsDeletingImage] = useState<boolean>(false);
  const [newCatalogName, setNewCatalogName] = useState("");
  const [currentCatalogIndex, setCurrentCatalogIndex] = useState<number | null>(null);

  // Parse branch_menus from JSON strings and ensure menuImages contains only valid URLs
  const convertBranchMenus = (menus: any) => {
    if (!menus) return [];

    if (Array.isArray(menus)) {
      return menus.map((menuItem: any) => {
        // If it's a JSON string, try to parse it
        if (typeof menuItem === 'string') {
          try {
            const parsedMenu = JSON.parse(menuItem);
            return {
              menuName: parsedMenu.menuName || "Catalog",
              menuImages: Array.isArray(parsedMenu.menuImages)
                ? parsedMenu.menuImages.filter((img: any) =>
                    typeof img === 'string' && img.startsWith('http')
                  )
                : []
            };
          } catch (e) {
            // If it's a string but not valid JSON, check if it's a URL
            if (typeof menuItem === 'string' && menuItem.startsWith('http')) {
              return {
                menuName: "Catalog",
                menuImages: [menuItem]
              };
            }
            // If parsing fails, return empty menu
            return {
              menuName: "Catalog",
              menuImages: []
            };
          }
        }
        // If it's already an object, validate it
        else if (typeof menuItem === 'object' && menuItem !== null) {
          return {
            menuName: menuItem.menuName || "Catalog",
            menuImages: Array.isArray(menuItem.menuImages)
              ? menuItem.menuImages.filter((img: any) =>
                  typeof img === 'string' && img.startsWith('http')
                )
              : []
          };
        }
        // Default fallback
        return {
          menuName: "Catalog",
          menuImages: []
        };
      });
    }

    return [];
  };

  const form = useForm<ListingFormValues>({
    resolver: zodResolver(listingFormSchema) as any,
    defaultValues: {
      ...getDefaultValues(),
      ...initialData,
      shop_landline_numbers: initialData?.shop_landline_numbers || [],
      branch_menus: convertBranchMenus(initialData?.branch_menus),
      social_links: initialData?.social_links || {
        facebook: "",
        instagram: "",
        twitter: "",
        website: "",
      },
    },
  });

  // Add this function to handle social link changes
  const handleSocialLinkChange = (platform: string, value: string) => {
    const currentLinks = form.getValues("social_links") || {};
    form.setValue("social_links", {
      ...currentLinks,
      [platform]: value,
    });
  };

  // Add this new function after uploadImage
  const deleteImage = async (imageUrl: string): Promise<boolean> => {
    setIsDeletingImage(true);
    try {
      const encodedUrl = encodeURIComponent(imageUrl);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL}/common/delete-image/${encodedUrl}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to delete image");
      }

      return true;
    } catch (error) {
      console.error("Error deleting image:", error);
      toast.error("Failed to delete image");

      return false;
    } finally {
      setIsDeletingImage(false);
    }
  };
  const handleFormSubmit = async (values: ListingFormValues) => {
    setIsSubmitting(true);
    try {
      await onSubmit(values);
    } catch (error) {
      console.error("Error submitting form:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  // Modify handleImageUploadChange
  const handleImageUploadChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
    isGallery: boolean
  ) => {
    try {
      if (!token) {
        toast.error("Session expired. Please log in again.");
        return;
      }
      setUploadingImages(true);
      const files = e.target.files;
      if (!files) return;

      if (isGallery) {
        const uploadPromises = Array.from(files).map((file) =>
          uploadImage(file, shopId, token)
        );
        const uploadedUrls = await Promise.all(uploadPromises);
        form.setValue("image_gallery", [
          ...form.getValues("image_gallery"),
          ...uploadedUrls,
        ]);
      } else {
        const uploadedUrl = await uploadImage(files[0], shopId, token);
        form.setValue("listing_image", uploadedUrl);
      }
    } catch (error) {
      console.error("Image upload failed:", error);
      // Add error handling/notification here
    } finally {
      setUploadingImages(false);
    }
  };

  // Add a separate handler for catalog image uploads
  const handleCatalogImageUploadChange = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    try {
      if (!token) {
        toast.error("Session expired. Please log in again.");
        return;
      }

      if (currentCatalogIndex === null) {
        toast.error("Please select or create a catalog first");
        return;
      }

      setUploadingCatalogImages(true);
      const files = e.target.files;
      if (!files) return;

      const uploadPromises = Array.from(files).map((file) =>
        uploadImage(file, shopId, token)
      );
      const uploadedUrls = await Promise.all(uploadPromises);

      // Get current catalogs
      const currentCatalogs = [...(form.getValues("branch_menus") || [])];

      // Add images to the selected catalog
      if (currentCatalogIndex !== null && currentCatalogs[currentCatalogIndex]) {
        const existingMenuImages = currentCatalogs[currentCatalogIndex].menuImages || [];

        // Filter out any non-string or non-URL values
        const validUploadedUrls = uploadedUrls.filter(url =>
          typeof url === 'string' && url.startsWith('http')
        );

        currentCatalogs[currentCatalogIndex] = {
          ...currentCatalogs[currentCatalogIndex],
          menuName: currentCatalogs[currentCatalogIndex].menuName || 'Catalog',
          menuImages: [
            ...existingMenuImages,
            ...validUploadedUrls
          ]
        };
      }

      // Update form value
      form.setValue("branch_menus", currentCatalogs);
    } catch (error) {
      console.error("Catalog image upload failed:", error);
      toast.error("Failed to upload catalog images");
    } finally {
      setUploadingCatalogImages(false);
    }
  };

  // Add a function to create a new catalog
  const handleAddCatalog = () => {
    if (!newCatalogName || !newCatalogName.trim()) {
      toast.error("Please enter a catalog name");
      return;
    }

    const currentCatalogs = [...(form.getValues("branch_menus") || [])];
    currentCatalogs.push({
      menuName: newCatalogName.trim(),
      menuImages: []
    });

    form.setValue("branch_menus", currentCatalogs);
    setNewCatalogName("");
    setCurrentCatalogIndex(currentCatalogs.length - 1);
  };

  // Add a function to remove a catalog
  const handleRemoveCatalog = (index: number) => {
    if (index < 0) return;

    const currentCatalogs = [...(form.getValues("branch_menus") || [])];

    if (index >= currentCatalogs.length) return;

    currentCatalogs.splice(index, 1);
    form.setValue("branch_menus", currentCatalogs);

    if (currentCatalogIndex === index) {
      setCurrentCatalogIndex(null);
    } else if (currentCatalogIndex !== null && currentCatalogIndex > index) {
      setCurrentCatalogIndex(currentCatalogIndex - 1);
    }
  };

  // Add a function to remove a catalog image
  const handleRemoveCatalogImage = async (catalogIndex: number, imageIndex: number) => {
    if (catalogIndex === null || catalogIndex < 0) return;

    const currentCatalogs = [...(form.getValues("branch_menus") || [])];

    if (!currentCatalogs[catalogIndex] ||
        !currentCatalogs[catalogIndex].menuImages ||
        !currentCatalogs[catalogIndex].menuImages[imageIndex]) {
      return;
    }

    const imageUrl = currentCatalogs[catalogIndex].menuImages[imageIndex];

    // Delete the image from the server
    const success = await deleteImage(imageUrl);

    if (success) {
      // Remove the image from the catalog
      currentCatalogs[catalogIndex].menuImages.splice(imageIndex, 1);
      form.setValue("branch_menus", currentCatalogs);
    }
  };

  // Enhanced multi-value input renderer with improved UI
  const renderEnhancedMultiInput = (
    value: string,
    setValue: (val: string) => void,
    placeholder: string,
    items: string[],
    onAdd: (item: string) => void,
    onRemove: (index: number) => void
  ) => {
    const handleAdd = () => {
      if (!value.trim()) return;

      onAdd(value.trim());
      setValue("");
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && value.trim()) {
        e.preventDefault();
        handleAdd();
      }
    };

    return (
      <div className="space-y-2">
        <div className="flex gap-2 max-w-2xl">
          <Input
            value={value}
            onChange={(e) => {
              setValue(e.target.value);
            }}
            placeholder={placeholder}
            className="focus:ring-1 focus:ring-primary/20"
            onKeyDown={handleKeyDown}
          />
          <Button
            type="button"
            variant="secondary"
            size="sm"
            onClick={handleAdd}
            disabled={!value.trim()}
            className="shrink-0"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add
          </Button>
        </div>

        {items.length > 0 && (
          <div className="max-w-3xl">
            <div className="flex flex-wrap gap-2">
              {items.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center gap-1 bg-secondary/20 text-secondary-foreground px-2 py-1 rounded-md"
                >
                  <span className="text-sm">{item}</span>
                  <button
                    type="button"
                    onClick={() => onRemove(index)}
                    className="text-muted-foreground hover:text-destructive"
                  >
                    <X className="h-3.5 w-3.5" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };



  return (
    <Form {...form}>
      <div className="bg-background p-4 md:p-6 rounded-xl shadow-sm border border-border/50  overflow-y-auto">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Info Banner */}
          <div className="bg-muted/20 p-4 rounded-lg border border-border/30 flex items-start gap-3">
            <Info className="text-primary mt-1 h-5 w-5 flex-shrink-0" />
            <p className="text-sm text-muted-foreground">
              Fill in the details for this branch. Navigate between branches
              using the controls above
            </p>
          </div>
          <form
            onSubmit={form.handleSubmit(handleFormSubmit)}
            className="space-y-6"
          >
            <div className="grid md:grid-cols-2 gap-6">
              {/* Add branch_display_name field */}
              <FormField
                control={form.control}
                name="branch_display_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Display Name</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="e.g., Downtown Branch"
                        className="focus:ring-2 focus:ring-primary/30"
                      />
                    </FormControl>
                    <FormDescription className="text-xs text-muted-foreground">
                      The name to display for this branch on the website
                    </FormDescription>
                    <FormMessage className="text-xs text-destructive" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="branch_emirate"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Emirate</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="e.g., Dubai"
                        className="focus:ring-2 focus:ring-primary/30"
                      />
                    </FormControl>
                    <FormMessage className="text-xs text-destructive" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="average_spend"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Average Spend (AED)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        {...field}
                        value={field.value || 0}
                        onChange={(e) => field.onChange(Number(e.target.value))}
                        placeholder="e.g., 150"
                        min={0}
                        className="focus:ring-2 focus:ring-primary/30"
                      />
                    </FormControl>
                    <FormDescription className="text-xs text-muted-foreground">
                      Average amount a customer spends at this branch
                    </FormDescription>
                    <FormMessage className="text-xs text-destructive" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="show_in_landing_page"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border border-border/50 p-4 hover:bg-secondary/10 transition-colors">
                    <div className="space-y-0.5">
                      <FormLabel>Show in Landing Page</FormLabel>
                      <FormDescription className="text-xs text-muted-foreground">
                        Display this branch on the main landing page
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={field.onChange}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-6">
              <FormField
                control={form.control}
                name="branch_tags"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Shop Tags</FormLabel>
                    {renderEnhancedMultiInput(
                      newTag,
                      setNewTag,
                      "e.g., Indian, Casual Dining",
                      field.value,
                      (tag) => {
                        const updated = addArrayItem(field.value, tag);
                        field.onChange(updated);
                      },
                      (index) =>
                        field.onChange(removeArrayItem(field.value, index))
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="more_info"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Branch Features</FormLabel>
                    {renderEnhancedMultiInput(
                      newInfo,
                      setNewInfo,
                      "e.g., Vegetarian Options",
                      field.value,
                      (info) => {
                        const updated = addArrayItem(field.value, info);
                        field.onChange(updated);
                      },
                      (index) =>
                        field.onChange(removeArrayItem(field.value, index))
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="popular_items"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Popular Items</FormLabel>
                    {renderEnhancedMultiInput(
                      newDish,
                      setNewDish,
                      "e.g., Biryani",
                      field.value,
                      (dish) => {
                        const updated = addArrayItem(field.value, dish);
                        field.onChange(updated);
                      },
                      (index) =>
                        field.onChange(removeArrayItem(field.value, index))
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="known_for"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Known For</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Describe what your shop is known for..."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="space-y-6">
              <FormField
                control={form.control}
                name="shop_landline_numbers"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Landline Numbers</FormLabel>
                    {renderEnhancedMultiInput(
                      newLandlineNumber,
                      setNewLandlineNumber,
                      "e.g., 04-1234567",
                      field.value,
                      (number) => {
                        const updated = addArrayItem(field.value, number);
                        field.onChange(updated);
                      },
                      (index) =>
                        field.onChange(removeArrayItem(field.value, index))
                    )}
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="social_links.facebook"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Facebook</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Facebook URL"
                        className="focus:ring-2 focus:ring-primary/30"
                        onChange={(e) =>
                          handleSocialLinkChange("facebook", e.target.value)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="social_links.instagram"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Instagram</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Instagram URL"
                        className="focus:ring-2 focus:ring-primary/30"
                        onChange={(e) =>
                          handleSocialLinkChange("instagram", e.target.value)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="social_links.twitter"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Twitter</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Twitter URL"
                        className="focus:ring-2 focus:ring-primary/30"
                        onChange={(e) =>
                          handleSocialLinkChange("twitter", e.target.value)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="social_links.website"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Website URL"
                        className="focus:ring-2 focus:ring-primary/30"
                        onChange={(e) =>
                          handleSocialLinkChange("website", e.target.value)
                        }
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="grid md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="listing_image"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Listing Image</FormLabel>
                    <FormControl>
                      <div className="space-y-4">
                        <div className="flex items-center gap-4">
                          <Input
                            type="file"
                            accept="image/*"
                            onChange={(e) => handleImageUploadChange(e, false)}
                            className="hidden"
                            id="listing-image"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() =>
                              document.getElementById("listing-image")?.click()
                            }
                            disabled={uploadingImages}
                          >
                            {uploadingImages ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Upload className="h-4 w-4 mr-2" />
                            )}
                            Upload Image
                          </Button>
                        </div>
                        {field.value && (
                          <div className="relative w-40 h-40">
                            <Image
                              src={field.value}
                              alt="Listing"
                              width={160}
                              height={160}
                              quality={50}
                              className="w-full h-full object-cover rounded-md"
                            />
                            <button
                              type="button"
                              disabled={isDeletingImage}
                              onClick={async () => {
                                const success = await deleteImage(field.value);
                                if (success) {
                                  field.onChange("");
                                }
                              }}
                              className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1"
                            >
                              {isDeletingImage ? (
                                <Loader2 className="h-4 w-4 animate-spin" />
                              ) : (
                                <X className="h-4 w-4" />
                              )}
                            </button>
                          </div>
                        )}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="image_gallery"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Image Gallery</FormLabel>
                    <FormControl>
                      <div className="space-y-4">
                        <div className="flex items-center gap-4">
                          <Input
                            type="file"
                            accept="image/*"
                            multiple
                            onChange={(e) => handleImageUploadChange(e, true)}
                            className="hidden"
                            id="gallery-images"
                          />
                          <Button
                            type="button"
                            variant="outline"
                            onClick={() =>
                              document.getElementById("gallery-images")?.click()
                            }
                            disabled={uploadingImages}
                          >
                            {uploadingImages ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Upload className="h-4 w-4 mr-2" />
                            )}
                            Upload Images
                          </Button>
                        </div>
                        <div className="grid grid-cols-3 gap-4">
                          {field.value.map((image, index) => (
                            <div key={index} className=" relative h-40">
                              <Image
                                src={image}
                                width={160}
                                height={160}
                                quality={50}
                                alt={`Gallery ${index + 1}`}
                                className="w-full h-full object-cover rounded-md"
                              />
                              <button
                                type="button"
                                disabled={isDeletingImage}
                                onClick={async () => {
                                  const success = await deleteImage(image);
                                  if (success) {
                                    field.onChange(
                                      removeImage(field.value, index)
                                    );
                                  }
                                }}
                                className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1"
                              >
                                {isDeletingImage ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <X className="h-4 w-4" />
                                )}
                              </button>
                            </div>
                          ))}
                        </div>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="branch_menus"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Catalog Images</FormLabel>
                    <FormDescription className="text-xs text-muted-foreground mb-2">
                      Create catalogs with names and upload multiple images for each catalog
                    </FormDescription>
                    <FormControl>
                      <div className="space-y-6">
                        {/* Catalog Creation Section */}
                        <div className="space-y-4 border rounded-md p-4">
                          <div className="flex flex-col space-y-4">
                            <div className="flex gap-2">
                              <Input
                                value={newCatalogName}
                                onChange={(e) => setNewCatalogName(e.target.value)}
                                placeholder="Enter catalog name (e.g., Summer Collection)"
                                className="focus:ring-1 focus:ring-primary/20"
                              />
                              <Button
                                type="button"
                                variant="secondary"
                                onClick={handleAddCatalog}
                                disabled={!newCatalogName.trim()}
                              >
                                <Plus className="h-4 w-4 mr-1" />
                                Add Catalog
                              </Button>
                            </div>

                            {/* Catalog List */}
                            {field.value && field.value.length > 0 ? (
                              <div className="space-y-2">
                                <div className="text-sm font-medium">Your Catalogs:</div>
                                <div className="flex flex-wrap gap-2">
                                  {field.value.map((catalog, index) => (
                                    <div
                                      key={index}
                                      className={`flex items-center gap-1 px-3 py-2 rounded-md cursor-pointer ${
                                        currentCatalogIndex === index
                                          ? "bg-primary text-primary-foreground"
                                          : "bg-secondary/20 text-secondary-foreground hover:bg-secondary/30"
                                      }`}
                                      onClick={() => setCurrentCatalogIndex(index)}
                                    >
                                      <span className="text-sm">{catalog.menuName || 'Catalog'}</span>
                                      <span className="text-xs ml-1">({catalog.menuImages && catalog.menuImages.length || 0} images)</span>
                                      <button
                                        type="button"
                                        onClick={(e) => {
                                          e.stopPropagation();
                                          handleRemoveCatalog(index);
                                        }}
                                        className="ml-2 text-muted-foreground hover:text-destructive"
                                      >
                                        <X className="h-3.5 w-3.5" />
                                      </button>
                                    </div>
                                  ))}
                                </div>
                              </div>
                            ) : (
                              <div className="text-sm text-muted-foreground italic">
                                No catalogs created yet. Add a catalog name and click &quot;Add Catalog&quot;.
                              </div>
                            )}
                          </div>
                        </div>

                        {/* Catalog Image Upload Section - Only show when a catalog is selected */}
                        {currentCatalogIndex !== null && (
                          <div className="space-y-4 border rounded-md p-4">
                            <div className="flex items-center justify-between">
                              <div className="font-medium">
                                {field.value && field.value[currentCatalogIndex] ? (field.value[currentCatalogIndex].menuName || 'Catalog') : 'Catalog'} Images
                              </div>
                              <div className="flex items-center gap-4">
                                <Input
                                  type="file"
                                  accept="image/*"
                                  multiple
                                  onChange={handleCatalogImageUploadChange}
                                  className="hidden"
                                  id="catalog-images"
                                />
                                <Button
                                  type="button"
                                  variant="outline"
                                  onClick={() =>
                                    document.getElementById("catalog-images")?.click()
                                  }
                                  disabled={uploadingCatalogImages}
                                >
                                  {uploadingCatalogImages ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <Upload className="h-4 w-4 mr-2" />
                                  )}
                                  Upload Images
                                </Button>
                              </div>
                            </div>

                            {/* Display catalog images */}
                            {field.value &&
                             currentCatalogIndex !== null &&
                             field.value[currentCatalogIndex] &&
                             field.value[currentCatalogIndex].menuImages &&
                             field.value[currentCatalogIndex].menuImages.length > 0 ? (
                              <div className="grid grid-cols-3 gap-4">
                                {field.value[currentCatalogIndex].menuImages.map((image, imageIndex) => (
                                  <div key={imageIndex} className="relative h-40">
                                    <Image
                                      src={typeof image === 'string' ? image : ''}
                                      width={160}
                                      height={160}
                                      quality={50}
                                      alt={`${field.value && field.value[currentCatalogIndex] ? (field.value[currentCatalogIndex].menuName || 'Catalog') : 'Catalog'} ${imageIndex + 1}`}
                                      className="w-full h-full object-cover rounded-md"
                                    />
                                    <button
                                      type="button"
                                      disabled={isDeletingImage}
                                      onClick={() => handleRemoveCatalogImage(currentCatalogIndex, imageIndex)}
                                      className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1"
                                    >
                                      {isDeletingImage ? (
                                        <Loader2 className="h-4 w-4 animate-spin" />
                                      ) : (
                                        <X className="h-4 w-4" />
                                      )}
                                    </button>
                                  </div>
                                ))}
                              </div>
                            ) : (
                              <div className="text-sm text-muted-foreground italic text-center py-8">
                                No images uploaded for this catalog yet. Click &quot;Upload Images&quot; to add some.
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="flex justify-end mt-6 ">
              <Button
                type="submit"
                size="lg"
                className="min-w-[250px] flex items-center justify-center gap-2 shadow-md hover:shadow-lg transition-all"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving Branch Details...
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-5 w-5" />
                    Save Branch Details
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </Form>
  );
}
