import React, { useEffect, useState } from "react";
import Image from "next/image";
import badminton from "@/public/badminton.png";
import squash from "@/public/cravinsportssquash.png";
import padel from "@/public/cravinsportsiconpadel.png";
import tennis from "@/public/tennis-icon.svg";
import nets from "@/public/cricketnet.png";
import bowling from "@/public/bowlingmachine.png";
import {
  MdOutlineSportsSoccer,
  MdOutlineSportsBasketball,
  MdOutlineSportsVolleyball,
  MdOutlineSportsTennis,
  MdOutlineSportsGolf,
} from "react-icons/md";
import { TbOlympics, TbPingPong, TbCricket } from "react-icons/tb";
import { AiOutlineLoading3Quarters } from "react-icons/ai";

const CardIconSet: React.FC<{ text: string }> = ({ text }) => {
  if (text === "Badminton" || text === "Common_Upro_Badminton") {
    return (
      <Image
        unoptimized
        alt="Badminton"
        src={badminton}
        className="h-6 min-w-6"
      />
    );
  } else if (text === "Upro_Nets" || text === "Nets") {
    return <Image unoptimized alt="Nets" src={nets} className="h-6 min-w-6" />;
  } else if (text === "Upro_Bowling" || text === "Bowling") {
    return (
      <Image unoptimized alt="Bowling" src={bowling} className="h-6 min-w-6" />
    );
  } else if (text === "Football") {
    return <MdOutlineSportsSoccer className="h-6 w-6" />;
  } else if (text === "Basketball" || text === "Common_Upro_Basketball") {
    return <MdOutlineSportsBasketball className="h-6 w-6" />;
  } else if (text === "Cricket") {
    return <TbCricket className="h-6 w-6" />;
  } else if (text === "Volleyball" || text === "Throwball") {
    return <MdOutlineSportsVolleyball className="h-6 w-6" />;
  } else if (text === "Tennis") {
    return <Image alt="Tennis" src={tennis} className="h-6 min-w-6" />;
  } else if (text === "Futsal") {
    return <MdOutlineSportsSoccer className="h-6 w-6" />;
  } else if (text === "Table Tennis") {
    return <TbPingPong className="h-6 w-6" />;
  } else if (text === "Golf") {
    return <MdOutlineSportsGolf className="h-6 w-6" />;
  } else if (text === "Squash") {
    return <Image alt="Squash" src={squash} className="h-7 min-w-7" />;
  } else if (text === "Padel") {
    return <Image alt="Padel" src={padel} className="h-7 min-w-7" />;
  } else
    return <AiOutlineLoading3Quarters className="animate-spin" size={24} />;
};

export default CardIconSet;
