import { HydrationBoundary, dehydrate } from "@tanstack/react-query";
import { queryClient } from "@/lib/query-client";
import SportsProfileParent from "./sports-profile-parent";
import { Metadata, ResolvingMetadata } from "next";

type Props = {
  params: { id: string };
};

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const profile = params;
  const clubId = profile.id;

  const club_response = await fetch(
    process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL +
      "/clubs/landing-page/profile-page-details/" +
      clubId
  );
  const club_data = await club_response.json();
  return {
    title:
      club_data?.data?.clubName && club_data?.data?.clubName.length > 0
        ? club_data?.data?.clubName
        : "Sports Venue On Cravin Sports",
    description:
      club_data?.data?.aboutVenue && club_data?.data?.aboutVenue.length > 0
        ? club_data?.data?.aboutVenue
        : "Sports Venue Enlisted On Cravin Sports",
    icons: {
      icon: "/Cravin_logo_favi.png",
    },
    openGraph: {
      title:
        club_data?.data?.clubName && club_data?.data?.clubName.length > 0
          ? club_data?.data?.clubName
          : "Sports Venue On Cravin Sports",
      description:
        club_data?.data?.aboutVenue && club_data?.data?.aboutVenue.length > 0
          ? club_data?.data?.aboutVenue
          : "Sports Venue Enlisted On Cravin Sports",
      url: `https://justcravin.com/sports/${clubId}`,
      type: "website",
    },
    alternates: {
      canonical: "https://justcravin.com",
    },
  };
}

export default async function Page({ params }: { params: { id: string } }) {
  //prefetch will be here
  const profile = params;
  const clubId = profile.id;
  //   console.log(clubId);

  await queryClient.prefetchQuery({
    queryKey: ["getClubProfileInfo", clubId],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL +
          "/clubs/landing-page/profile-page-details/" +
          clubId
      );
      const data = await response.json();
      return data.data;
    },
  });

  await queryClient.prefetchQuery({
    queryKey: ["getClubFAQ", clubId],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL +
          "/clubs/landing-page/get-club-faqs/" +
          clubId
      );
      const data = await response.json();
      return data.data;
    },
  });

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <SportsProfileParent clubId={clubId} />
    </HydrationBoundary>
  );
}
