"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import React from "react";
import Image from "next/image";
import cravin<PERSON>ogo from "@/public/Cravin black stroke Logo.png";
import { IoLogoWhatsapp } from "react-icons/io";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import { HiInformationCircle } from "react-icons/hi";
import {
  HoverCard,
  HoverCardContent,
  HoverCardTrigger,
} from "@/components/ui/hover-card";
import { Badge } from "@/components/ui/badge";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
dayjs.extend(customParseFormat);
import testlistingimg from "@/public/Cravin_food_placeholder.png";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
} from "@/components/ui/carousel";
import Autoplay from "embla-carousel-autoplay";
import { useRouter } from "next/navigation";
import SportsProfileShareButton from "@/components/sports/sports-profile-share-button";
import { SportsProfileGalleryCarousal } from "@/components/sports/sports-profile-gallery-carousal";
import SportsProfileDirectionsButton from "@/components/sports/sports-profile-directions-button";
import { FaCheckCircle } from "react-icons/fa";
import CardIconSet from "@/components/sports/card-icon-set";
import Link from "next/link";
import MenuViewer from "@/components/menu-viewer";
import CategoryNameSet from "@/components/sports/cateory-name";
import CommonFooter from "@/components/common-footer";
import ListingSocialLinks from "@/components/listing-social-links";

export default function SportsProfile({
  clubProfileData,
  clubFAQData,
}: {
  clubProfileData: any;
  clubFAQData: any;
}) {
  const router = useRouter();
  const currentDayOfWeek = dayjs().day();
  const currentDayName = dayjs().format("dddd");
  // console.log(clubProfileData);
  return (
    <div className="h-full min-h-[100dvh] w-full flex flex-col items-center ">
      <div className="h-full w-full max-w-[1220px] flex flex-col items-center gap-3 ">
        <header className="w-full bg-white h-20">
          <div className="h-full w-full max-w-[1220px] flex justify-between items-center px-4 py-2 mx-auto">
            <Link href="/" className="relative w-36 h-16">
              <Image
                alt="Cravin"
                src={cravinLogo}
                fill
                sizes="160px"
                className="object-contain"
                priority
              />
            </Link>
          </div>
        </header>
        <main className="h-full w-full max-w-[1220px] flex flex-col items-center gap-3">
          <div className="w-full max-w-[1220px] justify-center items-center px-4 py-2 flex flex-col">
            <div className="flex flex-col w-full justify-center items-start gap-2">
              <Breadcrumb>
                <BreadcrumbList>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/">Home</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator></BreadcrumbSeparator>
                  <BreadcrumbItem>
                    <BreadcrumbLink href="/sports">Sports</BreadcrumbLink>
                  </BreadcrumbItem>
                  <BreadcrumbSeparator></BreadcrumbSeparator>
                  <BreadcrumbItem>
                    <BreadcrumbPage>{clubProfileData.clubName}</BreadcrumbPage>
                  </BreadcrumbItem>
                </BreadcrumbList>
              </Breadcrumb>
              <div className="flex md:hidden w-full gap-2">
                <Carousel
                  className="w-full"
                  plugins={[
                    Autoplay({
                      delay: 2000,
                    }),
                  ]}
                >
                  <CarouselContent>
                    {clubProfileData.listingImage &&
                      clubProfileData.listingImage.length > 0 && (
                        <CarouselItem
                          className="relative w-full h-full rounded-lg overflow-hidden"
                          key={0}
                        >
                          <div className="relative w-full h-[206px] sm:h-[340px] rounded-lg overflow-hidden cursor-pointer">
                            <Image
                              alt="merchantlogo"
                              src={
                                clubProfileData?.listingImage &&
                                clubProfileData?.listingImage?.length > 0
                                  ? clubProfileData?.listingImage
                                  : testlistingimg
                              }
                              fill
                              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                              style={{
                                objectFit: "cover",
                              }}
                            />
                          </div>
                        </CarouselItem>
                      )}
                    {clubProfileData.imageGallery &&
                      clubProfileData.imageGallery.length > 0 &&
                      clubProfileData.imageGallery.map(
                        (galleryitem: any, index: any) => (
                          <CarouselItem
                            className="relative w-full h-full rounded-lg overflow-hidden"
                            key={index + 1}
                          >
                            <div className="relative w-full h-[206px] sm:h-[340px] rounded-lg overflow-hidden cursor-pointer">
                              <Image
                                alt="itemimg"
                                src={
                                  galleryitem.length > 0
                                    ? galleryitem
                                    : testlistingimg
                                }
                                fill
                                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                                style={{
                                  objectFit: "cover",
                                }}
                              />
                            </div>
                          </CarouselItem>
                        )
                      )}
                  </CarouselContent>
                  {/* <CarouselPrevious />
                <CarouselNext /> */}
                </Carousel>
              </div>
              <div className="hidden md:flex w-full gap-2">
                <SportsProfileGalleryCarousal
                  clubProfileData={clubProfileData}
                  position={0}
                />
                <div className="hidden md:flex flex-col gap-2">
                  <SportsProfileGalleryCarousal
                    clubProfileData={clubProfileData}
                    position={1}
                  />
                  <SportsProfileGalleryCarousal
                    clubProfileData={clubProfileData}
                    position={2}
                  />
                </div>
                <div className="hidden lg:flex flex-col gap-2">
                  <SportsProfileGalleryCarousal
                    clubProfileData={clubProfileData}
                    position={3}
                  />
                </div>
              </div>
            </div>
          </div>
          <div className="w-full max-w-[1220px] justify-center items-center px-4 flex flex-col sticky top-0 z-50 bg-white py-2">
            <div className="flex flex-col w-full justify-center items-start gap-2">
              <p className="font-medium text-2xl sm:text-4xl">
                {clubProfileData.clubName}
              </p>
              <div className="w-full flex flex-col gap-1">
                {/* <div className="w-full line-clamp-1">
                {clubProfileData.branchTags &&
                  clubProfileData.branchTags.length > 0 &&
                  clubProfileData.branchTags.map((tag: any) => (
                    <Badge
                      key={tag}
                      className="font-normal border-[1px] border-black/75 mr-1 text-black/75"
                      variant="outline"
                    >
                      {tag}
                    </Badge>
                  ))}
              </div> */}
                <p className="text-black/50 font-normal">
                  {clubProfileData.clubLocation}
                </p>
                <div className="w-full flex gap-1 justify-start items-center text-black/50 font-normal">
                  {" "}
                  {clubProfileData.clubTimings &&
                    (currentDayOfWeek === 0 || currentDayOfWeek === 6 ? (
                      <div className="line-clamp-1">
                        {clubProfileData.clubTimings.weekend}
                      </div>
                    ) : (
                      <div className="line-clamp-1">
                        {clubProfileData.clubTimings.weekday}
                      </div>
                    ))}
                  <HoverCard>
                    <HoverCardTrigger asChild>
                      <Button
                        variant="ghost"
                        size="icon"
                        className="w-25 p-0 m-0 h-fit rounded-[50%] overflow-clip flex group"
                      >
                        <HiInformationCircle
                          className="rounded-[50%]"
                          size={20}
                        />
                      </Button>
                    </HoverCardTrigger>
                    <HoverCardContent className="w-full" side="bottom">
                      <div className="w-full flex flex-col">
                        <p className="font-medium ">Working Hours</p>
                        {clubProfileData.clubTimings && (
                          <div className="flex gap-2">
                            <p className="font-medium">Mon - Fri :</p>
                            <div className="line-clamp-1">
                              {clubProfileData.clubTimings.weekday}
                            </div>
                          </div>
                        )}
                        {clubProfileData.clubTimings && (
                          <div className="flex gap-2">
                            <p className="font-medium">Sat & Sun :</p>
                            <div className="line-clamp-1">
                              {clubProfileData.clubTimings.weekend}
                            </div>
                          </div>
                        )}
                      </div>
                    </HoverCardContent>
                  </HoverCard>
                </div>
              </div>
              <div className="flex flex-col-reverse sm:flex-row-reverse gap-2 justify-between w-full pb-1 ">
                <Button
                  className="h-8 px-3 flex gap-1 justify-center items-center border-[2px] border-black  bg-[#d5ff01] hover:bg-[#d5ff01] text-black rounded-full"
                  onClick={() => {
                    router.push(
                      `https://wa.me/${clubProfileData?.clubWhatsappNumber}?text=Hi`
                    );
                  }}
                  disabled={
                    !clubProfileData.showInLandingPage ||
                    !clubProfileData.clubWhatsappNumber ||
                    clubProfileData.clubWhatsappNumber.length <= 0 ||
                    clubProfileData?.onlyForListing
                  }
                >
                  <IoLogoWhatsapp size={16} /> Book Now
                </Button>
                <div className="flex flex-row gap-2">
                  {(clubProfileData?.latitude ||
                    clubProfileData?.latitude === 0) &&
                    (clubProfileData?.longitude ||
                      clubProfileData?.longitude === 0) && (
                      <SportsProfileDirectionsButton
                        latitude={clubProfileData.latitude}
                        longitude={clubProfileData.longitude}
                      />
                    )}
                  <SportsProfileShareButton />
                </div>
              </div>
              <div className=" w-full border border-black/30" />
            </div>
          </div>

          <div className="w-full max-w-[1220px] justify-center items-start px-4 flex flex-col md:flex-row pb-5">
            <div className="w-full flex flex-col">
              {clubProfileData && clubProfileData?.aboutVenue && (
                <div className="flex flex-col p-2">
                  <p className="font-normal text-lg">About</p>
                  <p className="font-light text-base text-left">
                    {clubProfileData?.aboutVenue}
                  </p>
                </div>
              )}

              {clubProfileData &&
                clubProfileData?.clubTags &&
                clubProfileData?.clubTags?.length > 0 && (
                  <div className="flex flex-col p-2 gap-1">
                    <p className="font-normal text-lg">Sports Available</p>
                    <div className="flex items-center gap-5 flex-wrap">
                      {clubProfileData &&
                        clubProfileData.facilities &&
                        clubProfileData.facilities.length > 0 &&
                        clubProfileData.facilities.map((facility: any) => (
                          <div
                            className="flex flex-col justify-center items-center p-4 gap-1 w-20 h-20 border-[1px] border-black/75 rounded-md"
                            key={facility.facilityName}
                          >
                            <div className="max-w-7 h-fit">
                              <CardIconSet text={facility.facilityCategory} />
                            </div>
                            <CategoryNameSet text={facility.facilityCategory} />
                          </div>
                        ))}
                    </div>
                  </div>
                )}

              {clubProfileData &&
                clubProfileData?.branchPricings &&
                clubProfileData?.branchPricings?.length > 0 && (
                  <div className="flex flex-col gap-1 p-2">
                    <p className="font-normal text-lg">Menu</p>
                    <div className="flex gap-3 flex-wrap">
                      {clubProfileData?.branchPricings &&
                        clubProfileData.branchPricings.length > 0 &&
                        //Nookesh changed the data format for this so now i have to json parse to convert
                        clubProfileData.branchPricings
                          .map((menuString: string) => {
                            //incase json stringi s wrong we skip
                            if (!menuString) return null;

                            //we return either the correct parsed object or null if the parse fails
                            try {
                              return JSON.parse(menuString);
                            } catch {
                              return null;
                            }
                          })
                          //we filter out the null values
                          .filter(Boolean)
                          .map((menu: any, index: number) => (
                            <div key={index} className="flex flex-col w-52">
                              <MenuViewer menuImages={menu.menuImages || []} />
                              <p>{menu.menuName || "Untitled"}</p>
                              {menu.menuImages &&
                                menu.menuImages.length > 0 && (
                                  <p className="text-xs font-light">
                                    {menu.menuImages.length > 1
                                      ? `${menu.menuImages.length} pages`
                                      : "1 page"}
                                  </p>
                                )}
                            </div>
                          ))}
                    </div>
                  </div>
                )}

              {clubProfileData &&
                clubProfileData?.clubTags &&
                clubProfileData?.clubTags?.length > 0 && (
                  <div className="flex flex-col p-2 gap-1">
                    <p className="font-normal text-lg">Key Words</p>
                    <div className="flex gap-1 flex-wrap">
                      {clubProfileData &&
                        clubProfileData.clubTags &&
                        clubProfileData.clubTags.length > 0 &&
                        clubProfileData.clubTags.map((tag: any) => (
                          <Badge
                            key={tag}
                            className="font-normal text-sm border-[1px] border-black/75 mr-1 text-black/75 capitalize"
                            variant="outline"
                          >
                            {tag}
                          </Badge>
                        ))}
                    </div>
                  </div>
                )}

              {clubProfileData && clubProfileData?.averageSpend && (
                <div className="flex flex-col p-2">
                  <p className="font-normal text-lg">Average Cost</p>
                  <p className="font-light text-base text-left">
                    AED {clubProfileData.averageSpend} per person (approx.)
                  </p>
                </div>
              )}

              {clubProfileData &&
                clubProfileData?.amenities &&
                clubProfileData?.amenities?.length > 0 && (
                  <div className="flex flex-col p-2">
                    <p className="font-normal text-lg">Amenities</p>
                    <div className="flex flex-wrap lg:gap-3">
                      {clubProfileData.amenities.map((info: any) => (
                        <div className="flex items-center p-2 gap-1" key={info}>
                          <FaCheckCircle size={18} color="#00b562" />
                          <p className="font-light text-base">{info}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

              {clubFAQData && clubFAQData.length > 0 && (
                <div className="hidden md:flex flex-col p-2 gap-1">
                  <p className="font-normal text-lg">
                    Frequently Asked Questions
                  </p>
                  <Accordion
                    type="single"
                    collapsible
                    className="w-full bg-white"
                  >
                    {clubFAQData.map((category: any, index: any) => (
                      <AccordionItem
                        value={category.categoryId}
                        className={`border-[#D9D9D9]`}
                        key={category.categoryId}
                      >
                        <AccordionTrigger className="no-underline hover:no-underline px-5 font-normal text-base capitalize h-10">
                          {category.categoryName}
                        </AccordionTrigger>
                        <AccordionContent className="p-0 m-0">
                          <div className="flex flex-col">
                            {category.faqs && category.faqs.length <= 0 && (
                              <>
                                <p>No FAQ&apos;s in this category</p>
                              </>
                            )}
                            {category.faqs && category.faqs.length > 0 && (
                              <>
                                <Accordion
                                  type="single"
                                  collapsible
                                  className="w-full  bg-white"
                                >
                                  {category.faqs.map((faq: any, index: any) => (
                                    <AccordionItem
                                      value={faq.faqId}
                                      key={faq.faqId}
                                      className={`${
                                        index !== category.faqs.length - 1
                                          ? "border-b"
                                          : "border-none"
                                      } border-[#D9D9D9]`}
                                    >
                                      <div className="flex w-full justify-between items-center px-10">
                                        <div className="w-full">
                                          <AccordionTrigger className="h-10 font-normal capitalize">
                                            Q. {faq.question}
                                          </AccordionTrigger>
                                          <AccordionContent className="font-light capitalize">
                                            A. {faq.answer}
                                          </AccordionContent>
                                        </div>
                                      </div>
                                    </AccordionItem>
                                  ))}
                                </Accordion>
                              </>
                            )}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </div>
              )}
            </div>
            <div className="flex flex-col w-full md:w-fit">
              {clubProfileData?.landLineNumbers &&
                clubProfileData?.landLineNumbers?.length > 0 && (
                  <div className="flex flex-col p-2">
                    <p className="font-normal text-lg">Call</p>
                    {clubProfileData.landLineNumbers.map((landline: any) => (
                      <p className="font-light text-base" key={landline}>
                        +{landline}
                      </p>
                    ))}
                  </div>
                )}

              {clubProfileData &&
                clubProfileData?.socialLinks &&
                Object.keys(clubProfileData?.socialLinks).length > 0 && (
                  <div className="flex flex-col p-2 gap-0.5">
                    <p className="font-normal text-lg">Social Links</p>
                    <ListingSocialLinks data={clubProfileData?.socialLinks} />
                  </div>
                )}

              {(clubProfileData?.latitude || clubProfileData?.latitude === 0) &&
                (clubProfileData?.longitude ||
                  clubProfileData?.longitude === 0) && (
                  <div className="w-full flex flex-col gap-2 p-2">
                    <p className="font-normal text-lg">Location</p>
                    <div className="w-full sm:w-[320px] h-[200px]">
                      <iframe
                        width={"100%"}
                        height={"100%"}
                        src={`https://maps.google.com/maps?q=${clubProfileData.latitude},${clubProfileData.longitude}&hl=es;z=1&amp&output=embed`}
                      />
                    </div>
                  </div>
                )}

              {clubFAQData && clubFAQData.length > 0 && (
                <div className="flex md:hidden flex-col p-2 gap-1">
                  <p className="font-normal text-lg">
                    Frequently Asked Questions
                  </p>
                  <Accordion
                    type="single"
                    collapsible
                    className="w-full bg-white"
                  >
                    {clubFAQData.map((category: any, index: any) => (
                      <AccordionItem
                        value={category.categoryId}
                        className={`border-[#D9D9D9]`}
                        key={category.categoryId}
                      >
                        <AccordionTrigger className="no-underline hover:no-underline sm:px-5 font-normal text-base capitalize h-10">
                          {category.categoryName}
                        </AccordionTrigger>
                        <AccordionContent className="p-0 m-0">
                          <div className="flex flex-col">
                            {category.faqs && category.faqs.length <= 0 && (
                              <>
                                <p>No FAQ&apos;s in this category</p>
                              </>
                            )}
                            {category.faqs && category.faqs.length > 0 && (
                              <>
                                <Accordion
                                  type="single"
                                  collapsible
                                  className="w-full  bg-white"
                                >
                                  {category.faqs.map((faq: any, index: any) => (
                                    <AccordionItem
                                      value={faq.faqId}
                                      key={faq.faqId}
                                      className={`${
                                        index !== category.faqs.length - 1
                                          ? "border-b"
                                          : "border-none"
                                      } border-[#D9D9D9]`}
                                    >
                                      <div className="flex w-full justify-between items-center sm:px-10">
                                        <div className="w-full">
                                          <AccordionTrigger className="min-h-10 font-normal capitalize text-start">
                                            Q. {faq.question}
                                          </AccordionTrigger>
                                          <AccordionContent className="font-light capitalize text-start">
                                            A. {faq.answer}
                                          </AccordionContent>
                                        </div>
                                      </div>
                                    </AccordionItem>
                                  ))}
                                </Accordion>
                              </>
                            )}
                          </div>
                        </AccordionContent>
                      </AccordionItem>
                    ))}
                  </Accordion>
                </div>
              )}
            </div>
          </div>
        </main>
        <CommonFooter />
      </div>
    </div>
  );
}
