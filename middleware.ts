import { withAuth } from "next-auth/middleware"
import { NextResponse } from "next/server"

export default withAuth(
  function middleware(req) {
    const isAdminPanel = req.nextUrl.pathname.startsWith("/admin")
    const isLoginPage = req.nextUrl.pathname === "/auth/login"

    // If trying to access admin page without auth, redirect to login with callbackUrl
    if (isAdminPanel && !req.nextauth.token && !isLoginPage) {
      const url = new URL("/auth/login", req.url)
      url.searchParams.set("callbackUrl", req.nextUrl.pathname)
      return NextResponse.redirect(url)
    }

    return NextResponse.next()
  },
  {
    callbacks: {
      authorized: ({ token }) => !!token
    },
  }
)

// Specify paths to apply middleware to
export const config = { matcher: ["/admin/:path*"] }
