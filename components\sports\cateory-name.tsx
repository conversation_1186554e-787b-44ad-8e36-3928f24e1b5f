import React, { useEffect, useState } from "react";

const CategoryNameSet: React.FC<{ text: string }> = ({ text }) => {
  if (text === "Badminton" || text === "Common_Upro_Badminton") {
    return (
      <p className="capitalize text-xs text-center font-normal">Badminton</p>
    );
  } else if (text === "Upro_Nets" || text === "Nets") {
    return <p className="capitalize text-xs text-center font-normal">Nets</p>;
  } else if (text === "Upro_Bowling" || text === "Bowling") {
    return (
      <p className="capitalize text-xs text-center font-normal">Bowling</p>
    );
  } else if (text === "Football") {
    return <p className="capitalize text-xs text-center font-normal">{text}</p>;
  } else if (text === "Basketball" || text === "Common_Upro_Basketball") {
    return (
      <p className="capitalize text-xs text-center font-normal">Basketball</p>
    );
  } else if (text === "Cricket") {
    return <p className="capitalize text-xs text-center font-normal">{text}</p>;
  } else if (text === "Volleyball" || text === "Throwball") {
    return <p className="capitalize text-xs text-center font-normal">{text}</p>;
  } else if (text === "Tennis") {
    return <p className="capitalize text-xs text-center font-normal">{text}</p>;
  } else if (text === "Futsal") {
    return <p className="capitalize text-xs text-center font-normal">{text}</p>;
  } else if (text === "Table Tennis") {
    return <p className="capitalize text-xs text-center font-normal">{text}</p>;
  } else if (text === "Golf") {
    return <p className="capitalize text-xs text-center font-normal">{text}</p>;
  } else if (text === "Squash") {
    return <p className="capitalize text-xs text-center font-normal">{text}</p>;
  } else if (text === "Padel") {
    return <p className="capitalize text-xs text-center font-normal">{text}</p>;
  } else
    return <p className="capitalize text-xs text-center font-normal">Loading</p>;
};

export default CategoryNameSet;
