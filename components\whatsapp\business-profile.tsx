"use client";

import { useState, useEffect } from "react";
import { toast } from "sonner";
import Image from "next/image";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { Forward, Globe, HelpCircle, Loader2, X } from "lucide-react";
import { Card } from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogFooter,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import { useSession } from "next-auth/react";

interface BusinessProfileProps {
  phoneNumberId: string;
  authToken: string;
  productName: string;
  businessId: string;
}

const INDUSTRY_OPTIONS = [
  { value: "ALCOHOL", label: "Alcoholic Beverages" },
  { value: "APPAREL", label: "Clothing and Apparel" },
  { value: "AUTO", label: "Automotive" },
  { value: "BEAUTY", label: "Beauty, Spa and Salon" },
  { value: "EDU", label: "Education" },
  { value: "ENTERTAIN", label: "Entertainment" },
  { value: "EVENT_PLAN", label: "Event Planning and Service" },
  { value: "FINANCE", label: "Finance and Banking" },
  { value: "GOVT", label: "Public Service" },
  { value: "GROCERY", label: "Food and Grocery" },
  { value: "HEALTH", label: "Medical and Health" },
  { value: "HOTEL", label: "Hotel and Lodging" },
  { value: "NONPROFIT", label: "Non-profit" },
  { value: "ONLINE_GAMBLING", label: "Online Gambling & Gaming" },
  { value: "OTC_DRUGS", label: "Over-the-Counter Drugs" },
  { value: "OTHER", label: "Other" },
  { value: "PHYSICAL_GAMBLING", label: "Non-Online Gambling & Gaming" },
  { value: "PROF_SERVICES", label: "Professional Services" },
  { value: "RESTAURANT", label: "Restaurant" },
  { value: "RETAIL", label: "Shopping and Retail" },
  { value: "TRAVEL", label: "Travel and Transportation" },
];

// Define validation schema with Zod
const profileFormSchema = z
  .object({
    about: z
      .string()
      .max(139, {
        message: "About must be at most 139 characters",
      })
      .optional()
      .or(z.literal("")),
    description: z
      .string()
      .max(512, {
        message: "Description must be at most 512 characters",
      })
      .optional()
      .or(z.literal("")),
    address: z
      .string()
      .max(256, {
        message: "Address must be at most 256 characters",
      })
      .optional()
      .or(z.literal("")),
    email: z
      .string()
      .email({
        message: "Please enter a valid email address",
      })
      .max(128, {
        message: "Email must be at most 128 characters",
      })
      .optional()
      .or(z.literal("")),
    websites: z
      .array(
        z.union([
          z
            .string()
            .url({
              message: "Please enter a valid URL including http:// or https://",
            })
            .max(256),
          z.literal(""),
        ])
      )
      .min(1)
      .max(2, {
        message: "You can add a maximum of 2 websites",
      }),
    vertical: z.string().min(1, {
      message: "Please select a business category",
    }),
  })
  .strict();

type ProfileFormValues = z.infer<typeof profileFormSchema>;

export default function WhatsAppBusinessProfile({
  phoneNumberId,
  productName,
  businessId,
  authToken,
}: BusinessProfileProps) {
  const [isUploadingImage, setIsUploadingImage] = useState(false);
  const [profileImageFile, setProfileImageFile] = useState<File | null>(null);
  const [profileImagePreview, setProfileImagePreview] = useState<string>("");
  const [isDisplayNameModalOpen, setIsDisplayNameModalOpen] = useState(false);
  const [displayName, setDisplayName] = useState("");
  const [whatsappNumber, setWhatsappNumber] = useState("");
  const [savingDisplayName, setSavingDisplayName] = useState(false);
  const [businessNameError, setBusinessNameError] = useState("");
  const [showSecondaryWebsite, setShowSecondaryWebsite] = useState(false);
  // const session = useSession();
  // Define form with react-hook-form and zod validation
  const form = useForm<ProfileFormValues>({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      about: "",
      description: "",
      address: "",
      email: "",
      websites: [""],
      vertical: "",
    },
    mode: "onChange",
  });

  // Fetch business profile data
  const {
    data: businessProfileData,
    refetch: refetchBusinessProfile,
    isLoading: isProfileLoading,
  } = useQuery({
    queryKey: ["businessProfile", productName, businessId],
    queryFn: async () => {
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/wa-business-management/business-profile/${productName}/${businessId}`,
          {
            method: "GET",
            headers: {
              Authorization: `Bearer ${authToken}`,
            },
          }
        );

        if (!response.ok) {
          throw new Error("Failed to fetch business profile");
        }

        const data = await response.json();
        if (data.statusCode !== 200) {
          throw new Error(data.message || "Failed to fetch business profile");
        }

        // Set phone number from data
        // if (data.data?.phoneNumber) {
        //   setWhatsappNumber(data.data.phoneNumber);
        // }

        // Set display name from phoneNumberDetails
        if (
          data.data?.businessDetails?.phoneNumberDetails?.display_phone_number
        ) {
          setWhatsappNumber(
            data.data.businessDetails.phoneNumberDetails.display_phone_number
          );
          setDisplayName(
            data.data.businessDetails.phoneNumberDetails.verified_name
          );
        }

        // Return the profile data
        return data.data?.businessDetails?.profile?.[0] || null;
      } catch (error) {
        console.error("Error fetching business profile:", error);
        toast.error("Failed to fetch business profile");
        return null;
      }
    },
    enabled: !!productName && !!businessId,
  });

  // Update form values when profile data is loaded
  useEffect(() => {
    if (businessProfileData) {
      // Ensure vertical value is one of the supported options
      const validVertical =
        businessProfileData.vertical &&
        INDUSTRY_OPTIONS.some(
          (opt) => opt.value === businessProfileData.vertical
        )
          ? businessProfileData.vertical
          : "OTHER";

      // Use setTimeout to ensure the form is properly updated after render
      setTimeout(() => {
        form.setValue("vertical", validVertical);
      }, 0);

      // Check if there's a second website to show the secondary field
      if (
        businessProfileData.websites &&
        businessProfileData.websites.length > 1
      ) {
        setShowSecondaryWebsite(true);
      }

      form.reset({
        about: businessProfileData.about || "",
        description: businessProfileData.description || "",
        address: businessProfileData.address || "",
        email: businessProfileData.email || "",
        websites: businessProfileData.websites || [""],
      });

      if (businessProfileData.profile_picture_url) {
        setProfileImagePreview(businessProfileData.profile_picture_url);
      }

      // Set display name from profile data
      if (businessProfileData.display_phone_number) {
        setDisplayName(businessProfileData.display_phone_number);
      }
    }
  }, [businessProfileData, form]);

  // Handle profile image selection
  const handleProfileImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const files = e.target.files;
    if (!files || !files[0]) return;

    const file = files[0];

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.error("Image size must be less than 5MB");
      return;
    }

    // Validate file type
    // Validate file type - only allow JPG and PNG
    const validTypes = ["image/jpeg", "image/jpg", "image/png"];
    if (!validTypes.includes(file.type)) {
      toast.error("Only JPG and PNG files are allowed");
      return;
    }
    setProfileImageFile(file);

    // Create a preview URL
    const reader = new FileReader();
    reader.onload = (event) => {
      if (event.target?.result) {
        setProfileImagePreview(event.target.result as string);
      }
    };
    reader.readAsDataURL(file);
  };

  // Add mutation for file upload
  const { mutateAsync: uploadProfileMedia } = useMutation({
    mutationKey: ["uploadProfileMedia"],
    mutationFn: async (formData: FormData) => {
      setIsUploadingImage(true);
      try {
        const response = await fetch(
          `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/wa-business-management/get-file-handler/${productName}/${businessId}`,
          {
            method: "POST",
            headers: new Headers({
              Authorization: `Bearer ${authToken}`,
            }),
            body: formData,
          }
        );

        if (!response.ok) {
          throw new Error("Failed to upload profile picture");
        }

        return response.json();
      } catch (error) {
        console.error("Error uploading profile picture:", error);
        toast.error("Failed to upload profile picture");
        throw error;
      } finally {
        setIsUploadingImage(false);
      }
    },
    onError: () => {
      toast.error("Could not upload profile image. Please try again later!");
    },
  });

  // Replace the old uploadProfileImage method with the new one
  const uploadProfileImage = async (file: File) => {
    const formData = new FormData();
    formData.append("file", file);

    const uploadResult = await uploadProfileMedia(formData);

    if (uploadResult.statusCode === 201 && uploadResult.data?.fileHandlerKey) {
      return uploadResult.data.fileHandlerKey;
    } else {
      throw new Error("Failed to get file handler key for image");
    }
  };

  // Handle display name update submission
  const updateDisplayName = async () => {
    setBusinessNameError("");

    // Validate display name (follow WhatsApp's naming guidelines)
    if (!displayName.trim()) {
      setBusinessNameError("Display name cannot be empty");
      return;
    }

    if (displayName.trim().length > 25) {
      setBusinessNameError("Display name cannot exceed 25 characters");
      return;
    }

    setSavingDisplayName(true);

    try {
      // Call a separate API to update the display name
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/wa-business-management/update-display-name/${phoneNumberId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${authToken}`,
          },
          body: JSON.stringify({
            messaging_product: "whatsapp",
            display_phone_number: displayName.trim(),
          }),
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to update business name");
      }

      toast.success("Business name updated successfully!");
      setIsDisplayNameModalOpen(false);
      refetchBusinessProfile();
    } catch (error) {
      console.error("Error updating display name:", error);
      toast.error("Failed to update business name");
    } finally {
      setSavingDisplayName(false);
    }
  };

  // WhatsApp Preview component
  const WhatsAppPreview = () => {
    // Get current form values for the preview
    const { about, description, vertical, address, email, websites } =
      form.watch();
    const businessName =
      displayName ||
      businessProfileData?.display_phone_number ||
      "Your Business";
    const phoneNumber = whatsappNumber || "+971 56 XXX XXXX";

    // Filter out empty website URLs for display
    const filteredWebsites = websites.filter(
      (site) => site && site.trim() !== ""
    );

    return (
      <div className="sticky top-4 w-full max-w-xs rounded-lg overflow-hidden shadow-lg border border-gray-100 bg-white">
        {/* Header with back button and menu */}
        <div className="bg-gray-100 p-4 flex items-center justify-between">
          <div className="flex items-center">
            <svg
              className="w-5 h-5 text-gray-700"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M10 19l-7-7m0 0l7-7m-7 7h18"
              ></path>
            </svg>
          </div>
          <div className="flex items-center">
            <svg
              className="w-5 h-5 text-gray-700"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M12 5v.01M12 12v.01M12 19v.01M12 6a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2zm0 7a1 1 0 110-2 1 1 0 010 2z"
              ></path>
            </svg>
          </div>
        </div>

        {/* Business Profile Section */}
        <div className="p-4">
          <div className="flex flex-col items-center">
            {/* Profile Image */}
            <div className="relative h-32 w-32 rounded-full overflow-hidden border-2 border-gray-200 bg-gray-50 flex-shrink-0">
              {profileImagePreview ? (
                <Image
                  src={profileImagePreview}
                  alt="Profile Picture"
                  fill
                  className="object-cover"
                />
              ) : (
                <div className="flex h-full w-full items-center justify-center bg-gray-100">
                  <span className="text-gray-400 text-lg">No image</span>
                </div>
              )}
            </div>

            {/* Business Name */}
            <h2 className="text-lg font-semibold text-gray-800 mb-1">
              {businessName}
            </h2>

            {/* About text - directly under the business name */}
            {/* {about && (
              <p className="text-sm text-gray-600 text-center mb-2 max-w-full">
                {about}
              </p>
            )} */}

            {/* Phone Number */}
            {/* <p className="text-sm text-gray-600 mb-2">{phoneNumber}</p> */}

            {/* Share Button */}
            <div className="bg-gray-100 rounded-full px-6 py-1 flex items-center justify-center text-green-600 text-sm font-medium">
              <Forward className="mr-2" size={16} />
              Share
            </div>
          </div>
        </div>

        {/* Business Details */}
        <div className="px-4 pt-2 pb-6 space-y-3 ">
          {/* Description */}
          {description && (
            <div className="flex items-start">
              <div className="mt-1">
                <svg
                  className="w-5 h-5 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="1.5"
                    d="M7 8h10M7 12h4m1 8l-4-4H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v8a2 2 0 01-2 2h-3l-4 4z"
                  ></path>
                </svg>
              </div>
              <p className="text-sm text-gray-600 ml-3 flex-1">
                {description ||
                  "World class batting specialization academy based in Dubai, UAE."}
              </p>
            </div>
          )}

          {/* Business Category */}
          {vertical && (
            <div className="flex items-center">
              <svg
                className="w-5 h-5 text-gray-600"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="1.5"
                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                ></path>
              </svg>
              <p className="text-sm text-gray-600 ml-3">
                {INDUSTRY_OPTIONS.find((opt) => opt.value === vertical)
                  ?.label || "Other"}
              </p>
            </div>
          )}

          {/* Address */}
          {address && (
            <div className="flex items-start">
              <div className="mt-1">
                <svg
                  className="w-5 h-5 text-gray-600"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="1.5"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  ></path>
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="1.5"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  ></path>
                </svg>
              </div>
              <p className="text-sm text-gray-600 ml-3 flex-1">
                {address ||
                  "Insportz, 17A St - Al Quoz Industrial Area 3 - Dubai - UAE"}
              </p>
            </div>
          )}

          {/* Websites - Show all non-empty websites */}
          {filteredWebsites.length > 0 &&
            filteredWebsites.map((website, index) => (
              <div key={index} className="flex items-center">
                <Globe className="w-5 h-5 text-gray-600" />
                <p className="text-sm text-blue-600 ml-3 truncate">{website}</p>
              </div>
            ))}
          <div className="  pb-6 pt-1 border-t border-gray-800">
            <h3 className="text-gray-400 text-sm">About and phone number</h3>
            <p className="text-sm text-gray-600">
              {about || "Hey there! I am using WhatsApp."}
            </p>
            <p className="text-sm text-gray-600 mt-2">{phoneNumber}</p>
          </div>
          {/* Notice */}
          <p className="text-[10px] text-gray-400 italic mt-4">
            This experience may look different across devices.
          </p>
        </div>
      </div>
    );
  };

  // Update business profile mutation
  const { mutate: updateProfile, isPending: isUpdating } = useMutation({
    mutationFn: async (data: ProfileFormValues) => {
      let profilePictureHandle = undefined;

      // Upload image if a new one is selected
      if (profileImageFile) {
        try {
          profilePictureHandle = await uploadProfileImage(profileImageFile);
          console.log("Uploaded image handler key:", profilePictureHandle);

          if (!profilePictureHandle) {
            toast.warning("Failed to get profile picture handle");
            return;
          }
        } catch (error) {
          console.error("Image upload error:", error);
          toast.warning(
            "Profile picture upload failed, continuing with other updates"
          );
        }
      }

      // Create payload with only changed values
      const payload: Record<string, any> = {};

      // Compare current values with original values
      if (data.about !== businessProfileData?.about) {
        payload.about = data.about;
      }

      if (data.description !== businessProfileData?.description) {
        payload.description = data.description;
      }

      if (data.address !== businessProfileData?.address) {
        payload.address = data.address;
      }

      if (data.email !== businessProfileData?.email) {
        payload.email = data.email;
      }

      // Compare websites
      const originalWebsites = businessProfileData?.websites || [];
      const newWebsites = data.websites.filter((site) => site.trim() !== "");

      // Check if websites array has changed
      const websitesChanged =
        newWebsites.length !== originalWebsites.length ||
        newWebsites.some((site, idx) => site !== originalWebsites[idx]);

      if (websitesChanged) {
        payload.websites = newWebsites;
      }

      if (data.vertical !== businessProfileData?.vertical) {
        payload.vertical = data.vertical;
      }

      // Add profile picture handle if available
      if (profilePictureHandle) {
        payload.profile_picture_handle = profilePictureHandle;
      }

      // Don't send request if nothing changed
      if (Object.keys(payload).length === 0 && !profilePictureHandle) {
        toast.info("No changes to update");
        return { success: true };
      }

      console.log("Sending profile update payload:", payload);

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/wa-business-management/update-business-profile/${productName}/${businessId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${authToken}`,
          },
          body: JSON.stringify(payload),
        }
      );

      if (!response.ok) {
        const errorData = await response
          .json()
          .catch(() => ({ message: "Unknown error occurred" }));
        throw new Error(
          errorData.message || "Failed to update business profile"
        );
      }

      return response.json();
    },
    onSuccess: (data) => {
      if (data) {
        toast.success("Business profile updated successfully!");
        refetchBusinessProfile();
        setProfileImageFile(null); // Clear the selected file
      }
    },
    onError: (error: Error) => {
      console.error("Error updating business profile:", error);
      toast.error(error.message || "Failed to update business profile");
    },
  });

  // Form submission handler
  const onSubmit = (values: ProfileFormValues) => {
    updateProfile(values);
  };

  // Create a skeleton loader for form fields
  const FormFieldSkeleton = () => (
    <div className="space-y-2">
      <Skeleton className="h-5 w-40" />
      <Skeleton className="h-12 w-full" />
    </div>
  );

  return (
    <div className="w-full ">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800">
          WhatsApp Business Profile
        </h1>
        <p className="mt-2 text-gray-600">
          Customize how customers see your business on WhatsApp
        </p>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Form Section - Takes 2/3 of the screen on desktop */}
        <div className="lg:col-span-2 space-y-6">
          <Card className="overflow-hidden border-0 shadow-lg">
            <div className="bg-gradient-to-r from-green-50 to-blue-50 px-6 py-4 border-b">
              <h2 className="text-lg font-semibold text-gray-800">
                Profile Information
              </h2>
              <p className="text-sm text-gray-600">
                These details will be visible to customers who view your
                business on WhatsApp
              </p>
            </div>

            <div className="p-6">
              {/* Business Display Name Section */}
              <div className="flex flex-col md:flex-row gap-8 items-start pb-6 border-b border-gray-100">
                <div className="w-full md:w-1/3">
                  <h3 className="text-sm font-medium text-gray-700">
                    Business Display Name
                  </h3>
                  <p className="mt-1 text-sm text-gray-500">
                    This name will appear to your customers on WhatsApp
                  </p>
                </div>
                <div className="w-full md:w-2/3">
                  {isProfileLoading ? (
                    <Skeleton className="h-16 w-full rounded-md" />
                  ) : (
                    <div className="flex items-center justify-between p-4 bg-gray-50 rounded-md border border-gray-100">
                      <div>
                        <p className="font-medium text-gray-900">
                          {displayName ||
                            businessProfileData?.display_phone_number ||
                            "Not set"}
                        </p>
                        <p className="text-sm text-gray-500 mt-1">
                          This is your WhatsApp business display name
                        </p>
                      </div>
                      {/* <Button
                        variant="outline"
                        size="sm"
                        onClick={() => setIsDisplayNameModalOpen(true)}
                      >
                        Edit
                      </Button> */}
                    </div>
                  )}
                </div>
              </div>

              <Form {...form}>
                <form
                  onSubmit={form.handleSubmit(onSubmit)}
                  className="space-y-8"
                >
                  {/* Profile Picture Section */}
                  <div className="flex flex-col md:flex-row gap-8 items-start pb-6 border-b border-gray-100 pt-6">
                    <div className="w-full md:w-1/3">
                      <h3 className="text-sm font-medium text-gray-700">
                        Profile Photo
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Add a recognizable image that represents your business
                      </p>
                    </div>
                    <div className="w-full md:w-2/3">
                      {isProfileLoading ? (
                        <div className="flex flex-col sm:flex-row items-center gap-6">
                          <Skeleton className="h-32 w-32 rounded-full" />
                          <div className="flex flex-col gap-2 w-full">
                            <Skeleton className="h-10 w-full" />
                            <Skeleton className="h-4 w-full" />
                            <Skeleton className="h-4 w-full" />
                          </div>
                        </div>
                      ) : (
                        <div className="flex flex-col sm:flex-row items-center gap-6">
                          <div className="relative h-32 w-32 rounded-full overflow-hidden border-2 border-gray-200 bg-gray-50 flex-shrink-0">
                            {profileImagePreview ? (
                              <Image
                                src={profileImagePreview}
                                alt="Profile Picture"
                                fill
                                className="object-cover"
                              />
                            ) : (
                              <div className="flex h-full w-full items-center justify-center bg-gray-100">
                                <span className="text-gray-400 text-lg">
                                  No image
                                </span>
                              </div>
                            )}
                          </div>
                          <div className="flex flex-col gap-2 w-full">
                            <div className="relative">
                              <Input
                                id="profile_picture"
                                type="file"
                                accept="image/*"
                                onChange={handleProfileImageChange}
                                className="w-full cursor-pointer text-sm"
                              />
                            </div>
                            <div className="text-xs text-gray-500 space-y-1">
                              <p>• Maximum file size: 5MB</p>
                              <p>• Square images work best</p>
                              <p>• JPG, PNG, or GIF formats</p>
                            </div>
                          </div>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Business Information Section */}
                  <div className="flex flex-col md:flex-row gap-8 items-start pb-6 border-b border-gray-100">
                    <div className="w-full md:w-1/3">
                      <h3 className="text-sm font-medium text-gray-700">
                        Business Information
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Details about your services, location, and contact
                        information
                      </p>
                    </div>
                    <div className="w-full md:w-2/3 space-y-6">
                      {/* About Field */}
                      {isProfileLoading ? (
                        <FormFieldSkeleton />
                      ) : (
                        <FormField
                          control={form.control}
                          name="about"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gray-700">
                                About (Optional)
                              </FormLabel>
                              <FormControl>
                                <Textarea
                                  {...field}
                                  placeholder="Brief text about your business (up to 139 characters)"
                                  className="resize-none min-h-[80px] border-gray-300 focus:border-green-500 focus:ring-green-500"
                                  maxLength={139}
                                />
                              </FormControl>
                              <div className="flex justify-between text-xs mt-1">
                                <p className="text-gray-500">
                                  Maximum 139 characters
                                </p>
                                <p className="text-gray-500">
                                  {field.value?.length || 0}/139
                                </p>
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      {/* Description Field */}
                      {isProfileLoading ? (
                        <FormFieldSkeleton />
                      ) : (
                        <FormField
                          control={form.control}
                          name="description"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gray-700">
                                Business Description
                              </FormLabel>
                              <FormControl>
                                <Textarea
                                  {...field}
                                  placeholder="Tell customers about your business, services, or products"
                                  className="resize-none min-h-[120px] border-gray-300 focus:border-green-500 focus:ring-green-500"
                                  maxLength={512}
                                />
                              </FormControl>
                              <div className="flex justify-between text-xs mt-1">
                                <p className="text-gray-500">
                                  Maximum 512 characters
                                </p>
                                <p className="text-gray-500">
                                  {field.value?.length || 0}/512
                                </p>
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      {/* Business Category Field */}
                      {isProfileLoading ? (
                        <FormFieldSkeleton />
                      ) : (
                        <FormField
                          control={form.control}
                          name="vertical"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gray-700">
                                Business Category{" "}
                                <span className="text-red-500">*</span>
                              </FormLabel>
                              <Select
                                onValueChange={field.onChange}
                                value={field.value}
                                defaultValue={field.value}
                              >
                                <FormControl>
                                  <SelectTrigger className="border-gray-300 focus:border-green-500 focus:ring-green-500">
                                    <SelectValue placeholder="Select a business category" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {INDUSTRY_OPTIONS.map((option) => (
                                    <SelectItem
                                      key={option.value}
                                      value={option.value}
                                    >
                                      {option.label}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>
                  </div>

                  {/* Contact Information Section */}
                  <div className="flex flex-col md:flex-row gap-8 items-start pb-6 border-b border-gray-100">
                    <div className="w-full md:w-1/3">
                      <h3 className="text-sm font-medium text-gray-700">
                        Contact Information
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Help customers reach out to you
                      </p>
                    </div>
                    <div className="w-full md:w-2/3 space-y-6">
                      {/* Address Field */}
                      {isProfileLoading ? (
                        <FormFieldSkeleton />
                      ) : (
                        <FormField
                          control={form.control}
                          name="address"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gray-700">
                                Address
                              </FormLabel>
                              <FormControl>
                                <Textarea
                                  {...field}
                                  placeholder="Your business address"
                                  className="resize-none min-h-[80px] border-gray-300 focus:border-green-500 focus:ring-green-500"
                                  maxLength={256}
                                />
                              </FormControl>
                              <div className="flex justify-between text-xs mt-1">
                                <p className="text-gray-500">
                                  Maximum 256 characters
                                </p>
                                <p className="text-gray-500">
                                  {field.value?.length || 0}/256
                                </p>
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}

                      {/* Email Field */}
                      {isProfileLoading ? (
                        <FormFieldSkeleton />
                      ) : (
                        <FormField
                          control={form.control}
                          name="email"
                          render={({ field }) => (
                            <FormItem>
                              <FormLabel className="text-gray-700">
                                Email
                              </FormLabel>
                              <FormControl>
                                <Input
                                  {...field}
                                  type="email"
                                  placeholder="<EMAIL>"
                                  maxLength={128}
                                  className="border-gray-300 focus:border-green-500 focus:ring-green-500"
                                />
                              </FormControl>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                      )}
                    </div>
                  </div>

                  {/* Websites Section */}
                  <div className="flex flex-col md:flex-row gap-8 items-start pb-6">
                    <div className="w-full md:w-1/3">
                      <h3 className="text-sm font-medium text-gray-700">
                        Online Presence
                      </h3>
                      <p className="mt-1 text-sm text-gray-500">
                        Link to your website or online store
                      </p>
                    </div>
                    <div className="w-full md:w-2/3 space-y-4">
                      {/* Website Fields */}
                      {isProfileLoading ? (
                        <div className="space-y-4">
                          <FormFieldSkeleton />
                          {showSecondaryWebsite && <FormFieldSkeleton />}
                        </div>
                      ) : (
                        <div className="space-y-4">
                          <FormField
                            control={form.control}
                            name="websites.0"
                            render={({ field }) => (
                              <FormItem>
                                <FormLabel className="text-gray-700">
                                  Website
                                </FormLabel>
                                <FormControl>
                                  <Input
                                    {...field}
                                    placeholder="https://www.yourbusiness.com"
                                    maxLength={256}
                                    className="border-gray-300 focus:border-green-500 focus:ring-green-500"
                                  />
                                </FormControl>
                                <FormMessage />
                              </FormItem>
                            )}
                          />

                          {showSecondaryWebsite ? (
                            <div className="space-y-2">
                              <FormField
                                control={form.control}
                                name="websites.1"
                                render={({ field }) => (
                                  <FormItem>
                                    <div className="flex items-center justify-between">
                                      <FormLabel className="text-gray-700">
                                        Secondary Website
                                      </FormLabel>
                                      <Button
                                        type="button"
                                        variant="ghost"
                                        size="sm"
                                        onClick={() => {
                                          const currentWebsites =
                                            form.getValues("websites");
                                          form.setValue("websites", [
                                            currentWebsites[0],
                                          ]);
                                          setShowSecondaryWebsite(false);
                                        }}
                                        className="h-8 px-2 text-red-500 hover:text-red-700"
                                      >
                                        <X className="h-4 w-4 mr-1" />
                                        Remove
                                      </Button>
                                    </div>
                                    <FormControl>
                                      <Input
                                        {...field}
                                        placeholder="https://www.yourothersite.com"
                                        maxLength={256}
                                        className="border-gray-300 focus:border-green-500 focus:ring-green-500"
                                      />
                                    </FormControl>
                                    <FormMessage />
                                  </FormItem>
                                )}
                              />
                            </div>
                          ) : (
                            <Button
                              type="button"
                              variant="outline"
                              size="sm"
                              onClick={() => {
                                const currentWebsites =
                                  form.getValues("websites");
                                if (currentWebsites.length === 1) {
                                  form.setValue("websites", [
                                    ...currentWebsites,
                                    "",
                                  ]);
                                }
                                setShowSecondaryWebsite(true);
                              }}
                              className="text-green-600 border-green-600 hover:bg-green-50"
                            >
                              + Add Secondary Website
                            </Button>
                          )}

                          <p className="text-xs text-gray-500">
                            URLs must include http:// or https://
                          </p>
                        </div>
                      )}
                    </div>
                  </div>

                  {/* Submit Button */}
                  <div className="flex justify-end">
                    <Button
                      type="submit"
                      className="px-6 py-2 bg-green-600 hover:bg-green-700 text-white"
                      disabled={
                        isUpdating ||
                        isUploadingImage ||
                        // !form.formState.isValid ||
                        isProfileLoading
                      }
                    >
                      {isUpdating || isUploadingImage ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          {isUploadingImage
                            ? "Uploading Image..."
                            : "Updating Profile..."}
                        </>
                      ) : (
                        "Save Changes"
                      )}
                    </Button>
                  </div>
                </form>
              </Form>
            </div>
          </Card>
        </div>

        {/* Preview Section - Fixed position on desktop */}
        <div className="hidden lg:block">
          <div className="sticky top-6">
            <div className="mb-4">
              <h3 className="font-medium text-gray-700 flex items-center">
                <span className="inline-block w-3 h-3 bg-green-500 rounded-full mr-2"></span>
                Live Preview
              </h3>
              <p className="text-sm text-gray-500 mt-1">
                How customers will see your business on WhatsApp
              </p>
            </div>
            <div className="bg-gray-50 rounded-lg p-4 shadow-sm">
              {isProfileLoading ? (
                <div className="w-full max-w-xs">
                  <Skeleton className="h-12 w-full mb-4" />
                  <div className="flex justify-center">
                    <Skeleton className="h-32 w-32 rounded-full mb-4" />
                  </div>
                  <Skeleton className="h-6 w-3/4 mx-auto mb-2" />
                  <Skeleton className="h-4 w-1/2 mx-auto mb-4" />
                  <Skeleton className="h-8 w-1/3 mx-auto mb-8" />
                  <div className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                </div>
              ) : (
                <WhatsAppPreview />
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Display Name Edit Modal */}
      <Dialog
        open={isDisplayNameModalOpen}
        onOpenChange={setIsDisplayNameModalOpen}
      >
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle className="flex items-center">
              WhatsApp Business display name
              <span className="text-sm font-normal text-gray-500 ml-2">
                (Ensure that your name follows {"WhatsApp's "}
                <a
                  target="__blank"
                  href="https://www.facebook.com/business/help/338047025165344"
                  className="text-blue-500 hover:underline"
                >
                  naming guidelines
                </a>
                )
              </span>
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Input
                value={displayName}
                onChange={(e) => setDisplayName(e.target.value)}
                placeholder="Enter new display name"
                className="w-full"
                maxLength={25}
              />
              {businessNameError && (
                <p className="text-sm text-red-500">{businessNameError}</p>
              )}
              <div className="flex items-start text-xs text-gray-500 mt-1">
                <HelpCircle className="h-4 w-4 mr-1 flex-shrink-0" />
                <p>
                  Display name must follow {"WhatsApp's"} naming guidelines and
                  cannot exceed 25 characters.
                </p>
              </div>
            </div>
          </div>

          <DialogFooter className="sm:justify-between">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsDisplayNameModalOpen(false)}
                className="text-gray-500"
              >
                Cancel
              </Button>
            </div>

            <Button
              type="button"
              disabled={savingDisplayName}
              onClick={updateDisplayName}
              className="bg-blue-500 hover:bg-blue-600 text-white"
            >
              {savingDisplayName ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin mr-2" />
                  Submitting
                </>
              ) : (
                "Submit"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}
