"use client";

import { Suspense, useEffect, useState } from "react";
import { redirect, useRouter, useSearchParams } from "next/navigation";
import { Input } from "@/components/ui/input";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { z } from "zod";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { get } from "lodash";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import cravinLogo from "@/public/CravinSportsLogo.svg";

import { toast } from "sonner";
import {
  Loader2,
  Store,
  MapPin,
  Phone,
  Clock,
  CreditCard,
  Trash2,
  Plus,
  Building2,
  Globe,
  Hash,
  ChevronLeft,
  ChevronRight,
  CheckCircle2,
  Dot,
  ShieldCheck,
  Zap,
  QrCode,
  Upload,
  X,
  ArrowLeft,
} from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  SelectGroup,
  SelectLabel,
} from "@/components/ui/select";
import { useMutation, useQueryClient, useQuery } from "@tanstack/react-query";
import { Progress } from "@/components/ui/progress";
import Image from "next/image";

import ErrorMessage from "@/components/ui/error";
import { FaWhatsapp } from "react-icons/fa6";
import ClubLoginSetup from "@/components/sports/onboarding/club-login-setup";
import { useSession } from "next-auth/react";
import { cn } from "@/lib/utils";
import Link from "next/link";
import WhatsAppSetupButton from "@/components/ui/whatsapp-setup-button";

const formSchema = z.object({
  club: z.object({
    club_id: z.string().optional(),
    club_name: z.string().min(1, { message: "Club name is required" }),
    phone_number: z.string().refine(
      (val) => {
        // This is a placeholder that will be replaced by conditional validation
        return true;
      },
      { message: "Valid phone number is required" }
    ),
    club_logo_url: z.string().url().optional(),
    club_bg_img_url: z.string().url().optional(),
    club_location: z.string().min(1, { message: "Location is required" }),
    club_location_url: z
      .string()
      .url()
      .min(1, { message: "Location URL is required" }),
    club_social_links: z.object({
      tiktok: z.string().url().optional(),
      facebook: z.string().url().optional(),
      instagram: z.string().url().optional(),
    }),
    club_cancellation_number: z
      .string()
      .regex(/^\d+$/, { message: "Phone number must contain only numbers" })
      .min(8, { message: "Phone number must be at least 8 digits" }),
    club_timings: z.object({
      weekday: z.string(),
      weekend: z.string(),
    }),
    club_emails: z
      .array(
        z
          .string()
          .email({ message: "Please enter a valid email address" })
          .min(1, { message: "Email is required" })
      )
      .min(1, { message: "At least one email is required" }),
    payment_config: z.object({
      payment_method: z.enum(["ccavenue", "stripe", "tap", "network", "paytabs"]).optional(),
      access_code: z.string().optional(),
      merchant_id: z.number().optional(),
      working_key: z.string().optional(),
      payment_api_key: z.string().optional(),
      payment_webhook_secret: z.string().optional(),
      outlet_id: z.string().optional(),
      paytabs_profile_id: z.string().optional(),
    }).optional(),
    enable_support: z.boolean(),
    inbox_access_enabled: z.boolean(),
    club_partial_payment: z.number().min(0).max(100),
    auto_refundable: z.boolean(),
    is_only_for_listing: z.boolean(),
  }).refine(
    (data) => {
      // If is_only_for_listing is true, phone_number is optional
      if (data.is_only_for_listing) {
        return true;
      }
      // Otherwise, phone_number is required and must follow validation rules
      return data.phone_number && 
        /^\d+$/.test(data.phone_number) && 
        data.phone_number.length >= 8 && 
        data.phone_number.length <= 15;
    },
    {
      message: "Phone number is required unless listing-only mode is enabled",
      path: ["phone_number"],
    }
  ),
});

type FormValues = z.infer<typeof formSchema>;

const defaultTimings = [
  { day: 0, start: "6:00 AM", end: "11:00 PM", status: true }, // Sunday
  { day: 1, start: "6:00 AM", end: "11:00 PM", status: true }, // Monday
  { day: 2, start: "6:00 AM", end: "11:00 PM", status: true }, // Tuesday
  { day: 3, start: "6:00 AM", end: "11:00 PM", status: true }, // Wednesday
  { day: 4, start: "6:00 AM", end: "11:00 PM", status: true }, // Thursday
  { day: 5, start: "6:00 AM", end: "11:00 PM", status: true }, // Friday
  { day: 6, start: "6:00 AM", end: "11:00 PM", status: true }, // Saturday
];

const defaultPaymentConfig = {
  payment_method: undefined,
  access_code: "",
  merchant_id: undefined,
  working_key: "",
  payment_api_key: "",
  payment_webhook_secret: "",
  outlet_id: "",
  paytabs_profile_id: "",
};



// Convert 24-hour format to AM/PM
const to12HourFormat = (time24: string) => {
  const [hours, minutes] = time24.split(":").map(Number);
  const period = hours >= 12 ? "PM" : "AM";
  const hours12 = hours % 12 || 12;
  return `${hours12}:${minutes.toString().padStart(2, "0")} ${period}`;
};

// Convert AM/PM format to 24-hour format
const to24HourFormat = (time12: string) => {
  const [time, period] = time12.split(" ");
  const [hours, minutes] = time.split(":").map(Number);
  let hours24 = hours;

  if (period === "PM" && hours !== 12) {
    hours24 = hours + 12;
  } else if (period === "AM" && hours === 12) {
    hours24 = 0;
  }

  return `${hours24.toString().padStart(2, "0")}:${minutes
    .toString()
    .padStart(2, "0")}`;
};

const parseTimeRange = (timeRange: string) => {
  const [start, end] = timeRange.split(" to ");
  return { start, end };
};

const formatTimeRange = (start: string, end: string) => {
  return `${start} to ${end}`;
};

const dayNames = [
  "Sunday",
  "Monday",
  "Tuesday",
  "Wednesday",
  "Thursday",
  "Friday",
  "Saturday",
];

function OnboardingPage({ token }: { token: string }) {
  const [step, setStep] = useState(1);
  const totalSteps = 3; // Reduced from 4 to 3 since we removed branches

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [createNewError, setCreateNewError] = useState(false);

  const router = useRouter();
  const searchParams = useSearchParams();
  const clubId = searchParams.get("id");

  const [onboardingComplete, setOnboardingComplete] = useState(false);
  const [isOnlyForListing, setIsOnlyForListing] = useState(false);
  const queryClient = useQueryClient();
  const handleBackToOnboarding = () => {
    setOnboardingComplete(false);
  };
  const { data: session, status, update } = useSession();

  useEffect(() => {
    if (!session && status !== "loading") {
      redirect("/login");
      return;
    }
  }, [session]);

  // useEffect(() => {
  //   update()
  // },[])

  // const token = token;
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      club: {
        club_name: "",
        club_id: clubId || "",
        phone_number: "",
        club_logo_url: "",
        club_bg_img_url: "",
        club_location: "",
        club_location_url: "",
        club_social_links: {
          tiktok: "",
          facebook: "",
          instagram: "",
        },
        club_cancellation_number: "",
        club_timings: {
          weekday: "06:00 AM to 11:00 PM",
          weekend: "06:00 AM to 12:00 AM",
        },
        club_emails: [""],
        payment_config: defaultPaymentConfig,
        enable_support: true,
        inbox_access_enabled: true,
        club_partial_payment: 25,
        auto_refundable: true,
        is_only_for_listing: false,
      },
    },
  });

  const createClubMutation = useMutation({
    mutationFn: async (data: {
      club: {
        club_name: string;
        phone_number: string;
        is_only_for_listing: boolean;
      };
    }) => {
      setIsSubmitting(true);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/clubs/onboard-new-club`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            club: {
              ...data.club,
              club_id: `${data.club.club_name.trim().replace(/\s+/g, "-")}`,
            },
          }),
        }
      );

      if (!response.ok) {
        toast.error("Failed to create club. Please try again.");
      }

      return response.json();
    },
    onSuccess: (data) => {
      if (data?.statusCode === 201) {
        // Update URL with Club ID
        const params = new URLSearchParams(searchParams.toString());
        params.set("id", data.message);
        router.push(`/admin/sports/onboarding?${params.toString()}`);
        setIsSubmitting(false);
        toast.success("Successfully Created Club!");
      } else {
        setCreateNewError(true);
        setIsSubmitting(false);
        setStep(1);
      }
    },
    onError: (error) => {
      setCreateNewError(true);
      setIsSubmitting(false);
      toast.error("Failed to create club. Please try again.");
      setStep(1);
    },
  });

  const {
    data: clubData,
    isLoading,
    error,
    refetch: refetchShop,
  } = useQuery({
    queryKey: ["clubData", clubId],
    queryFn: async () => {
      if (!clubId) return null;

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/clubs/onboarding/${clubId}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        toast.error("Failed to fetch Club data");
      }

      return response.json();
    },
    enabled: !!clubId,
  });
  const { mutateAsync: submitOnboarding } = useMutation({
    mutationKey: ["clubOnboarding"],
    mutationFn: async (data: FormValues) => {
      // Handle payment config based on payment method
      let paymentConfig: {
        payment_method?: "ccavenue" | "stripe" | "tap" | "network" | "paytabs";
        access_code?: string;
        merchant_id?: number;
        working_key?: string;
        payment_api_key?: string;
        payment_webhook_secret?: string;
        outlet_id?: string;
        paytabs_profile_id?: string;
      } | undefined = undefined;

      if (data.club.payment_config?.payment_method) {
        switch (data.club.payment_config.payment_method) {
          case "ccavenue":
            paymentConfig = {
              payment_method: "ccavenue",
              access_code: data.club.payment_config.access_code,
              merchant_id: data.club.payment_config.merchant_id,
              working_key: data.club.payment_config.working_key,
            };
            break;
          case "stripe":
            paymentConfig = {
              payment_method: "stripe",
              payment_api_key: data.club.payment_config.payment_api_key,
              payment_webhook_secret: data.club.payment_config.payment_webhook_secret,
            };
            break;
          case "tap":
            paymentConfig = {
              payment_method: "tap",
              payment_api_key: data.club.payment_config.payment_api_key,
            };
            break;
          case "network":
            paymentConfig = {
              payment_method: "network",
              outlet_id: data.club.payment_config.outlet_id,
              payment_api_key: data.club.payment_config.payment_api_key,
            };
            break;
          case "paytabs":
            paymentConfig = {
              payment_method: "paytabs",
              paytabs_profile_id: data.club.payment_config.paytabs_profile_id,
              payment_api_key: data.club.payment_config.payment_api_key,
            };
            break;
        }
      }

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/clubs/onboard-new-club/update-details`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify({
            ...data.club,
            payment_config: paymentConfig,
          }),
        }
      );
      return response.json();
    },
    onSuccess: (status) => {
      if (status.statusCode !== 201) {
        toast.error("Failed to submit Club  details. Please try again.");
      } else {
        refetchShop();
        toast.success("Successfully Updated Club  Details!");
      }
    },
    onError: (error) => {
      console.error("Submission error:", error);
    },
  });

  const onSubmit = async (values: FormValues) => {
    // console.log("values", values);
    setIsSubmitting(true);
    try {
      if (step === 3) {
        await submitOnboarding(values);
        queryClient.invalidateQueries({ queryKey: ["getShops"] });
        await refetchShop();
        // // setStep(1);
        // setCurrentShopId(values.club.club_id);
        // setCurrentBranches(values.branches.map(branch => ({
        //   branch_id: branch.branch_id,
        //   branch_name: branch.branch_name
        // })));
        // setOnboardingComplete(true);
      }
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const validateStep = async () => {
    let fieldsToValidate: string[] = [];

    switch (step) {
      case 1:
        fieldsToValidate = ["club.club_name", "club.phone_number"];
        break;
      case 2:
        fieldsToValidate = [
          "club.club_location",
          "club.club_location_url",
          "club.club_logo_url",
          "club.club_bg_img_url",
        ];
        break;
      case 3:
        fieldsToValidate = [
          "club.club_emails",
          "club.club_cancellation_number",
          "club.club_social_links",
          "club.club_timings",
          "club.payment_config",
          "club.enable_support",
          "club.inbox_access_enabled",
          "club.auto_refundable",
        ];
        break;
    }

    const result = await form.trigger(fieldsToValidate as any);



    if (!result) {
      // Get all current errors for the specified fields
      const currentErrors = form.formState.errors;

      // Find the first error message to display
      let errorMessage = "Please fill in all required fields.";
      fieldsToValidate.forEach((field) => {
        const fieldError = get(currentErrors, field);
        if (fieldError?.message) {
          errorMessage = fieldError.message;
          return; // Break the loop on first error
        }
      });

      toast.error(errorMessage);
    }

    return result;
  };

  const nextStep = async () => {
    const isValid = await validateStep();
    if (!isValid) return;

    // Store the current form values before moving to next step
    const currentValues = form.getValues();

    if (step === 1 && !clubId) {
      await createClubMutation.mutateAsync({
        club: {
          club_name: currentValues.club.club_name,
          phone_number: currentValues.club.phone_number,
          is_only_for_listing: currentValues.club.is_only_for_listing,
        },
      });
    }

    if (step < totalSteps && !createNewError) {
      setStep(step + 1);
      form.reset(currentValues);
    }
  };

  const prevStep = () => {
    if (step > 1) {
      // Store the current form values before moving to previous step
      const currentValues = form.getValues();
      setStep(step - 1);
      // Ensure form values are preserved after step change
      form.reset(currentValues);
    }
  };

  const [uploadingImages, setUploadingImages] = useState(false);
  const [isDeletingImage, setIsDeletingImage] = useState<boolean>(false);
  // Add this helper function near the top of the file
  const uploadImage = async (file: File, clubId: string) => {
    const formData = new FormData();
    formData.append("file", file);
    formData.append("filepath", "club-logos");

    const response = await fetch(
      `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/common/upload-images/${clubId}`,
      {
        method: "POST",
        body: formData,
        headers: {
          Authorization: `Bearer ${token}`,
        },
      }
    );

    if (!response.ok) {
      toast.error("Failed to upload image");
    }

    const jsonResponse = await response.json();
    return jsonResponse.message;
  };
  // Add image upload handler
  const handleImageUploadChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
    fieldName: "club_logo_url" | "club_bg_img_url"
  ) => {
    try {
      setUploadingImages(true);
      const files = e.target.files;
      if (!files || !files[0]) return;

      const uploadedUrl = await uploadImage(files[0], clubId || "");
      form.setValue(`club.${fieldName}`, uploadedUrl);
    } catch (error) {
      console.error("Image upload failed:", error);
      toast.error("Failed to upload image");
    } finally {
      setUploadingImages(false);
    }
  };

  // Add image delete handler
  const deleteImage = async (
    imageUrl: string,
    fieldName: "club_logo_url" | "club_bg_img_url"
  ): Promise<void> => {
    setIsDeletingImage(true);
    try {
      const encodedUrl = encodeURIComponent(imageUrl);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/common/delete-image/${encodedUrl}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to delete image");
      }

      form.setValue(`club.${fieldName}`, "");
      toast.success("Image deleted successfully");
    } catch (error) {
      console.error("Error deleting image:", error);
      toast.error("Failed to delete image");
    } finally {
      setIsDeletingImage(false);
    }
  };

  const renderStepContent = () => {
    switch (step) {
      case 1:
        return (
          <div className="space-y-8" key={"step-1"}>
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">
                Basic Information
              </h2>
              <p className="mt-1 text-sm text-gray-600">
                {"Let's start with your club's basic details"}
              </p>
            </div>

            <div className="space-y-6">
              <FormField
                control={form.control}
                name="club.club_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Club Name</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          {...field}
                          className="h-11 pl-10"
                          placeholder="e.g., The Gourmet"
                        />
                        <Store className="h-5 w-5 text-gray-400 absolute left-3 top-3" />
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="club.phone_number"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="text-base">Whatsapp Number</FormLabel>
                    <FormControl>
                      <div className="relative">
                        <Input
                          {...field}
                          className={cn(
                            "h-11 pl-10",
                            form.formState.errors.club?.phone_number &&
                              "border-red-500 focus:border-red-500 ring-red-500"
                          )}
                          type="text"
                          maxLength={15}
                          placeholder="971XXXXXXXXX"
                          onChange={(e) => {
                            const value = e.target.value.replace(/\D/g, "");
                            field.onChange(value);
                            // Trigger validation immediately
                            form.trigger("club.phone_number");
                          }}
                        />
                        <Phone className="h-5 w-5 text-gray-400 absolute left-3 top-3" />
                      </div>
                    </FormControl>
                    <FormDescription className="text-xs text-gray-500">
                      Enter a valid phone number (8-15 digits) - Ex: 971XXXXXXXX
                    </FormDescription>
                    <FormMessage className="text-xs mt-1 text-red-500 font-medium" />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="club.is_only_for_listing"
                render={({ field }) => (
                  <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Listing Only Mode
                      </FormLabel>
                      <FormDescription>
                        Enable if this club is only for listing purposes
                      </FormDescription>
                    </div>
                    <FormControl>
                      <Switch
                        checked={field.value}
                        onCheckedChange={(checked) => {
                          field.onChange(checked);
                          // setIsOnlyForListing(checked);
                        }}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
          </div>
        );

      case 2:
        return (
          <div className="space-y-8" key={"step-2"}>
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">
                Club Images & Location
              </h2>
              <p className="mt-1 text-sm text-gray-600">
                Upload your club images and set location
              </p>
            </div>

            <div className="space-y-6">
              <div className="grid md:grid-cols-2 gap-6">
                {/* Logo Upload */}
                <FormField
                  control={form.control}
                  name="club.club_logo_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Club Logo</FormLabel>
                      <FormControl>
                        <div className="space-y-4">
                          <div className="flex items-center gap-4">
                            <Input
                              type="file"
                              accept="image/*"
                              onChange={(e) =>
                                handleImageUploadChange(e, "club_logo_url")
                              }
                              className="hidden"
                              id="club-logo"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() =>
                                document.getElementById("club-logo")?.click()
                              }
                              disabled={uploadingImages}
                            >
                              {uploadingImages ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              ) : (
                                <Upload className="h-4 w-4 mr-2" />
                              )}
                              Upload Logo
                            </Button>
                          </div>
                          {field.value && (
                            <div className="relative w-40 h-40">
                              <Image
                                src={field.value}
                                alt="Club Logo"
                                width={160}
                                height={160}
                                className="w-full h-full object-contain rounded-md"
                              />
                              <button
                                type="button"
                                disabled={isDeletingImage}
                                onClick={() =>
                                  field.value &&
                                  deleteImage(field.value, "club_logo_url")
                                }
                                className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1"
                              >
                                {isDeletingImage ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <X className="h-4 w-4" />
                                )}
                              </button>
                            </div>
                          )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                {/* Background Image Upload */}
                <FormField
                  control={form.control}
                  name="club.club_bg_img_url"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Background Image</FormLabel>
                      <FormControl>
                        <div className="space-y-4">
                          <div className="flex items-center gap-4">
                            <Input
                              type="file"
                              accept="image/*"
                              onChange={(e) =>
                                handleImageUploadChange(e, "club_bg_img_url")
                              }
                              className="hidden"
                              id="club-bg"
                            />
                            <Button
                              type="button"
                              variant="outline"
                              onClick={() =>
                                document.getElementById("club-bg")?.click()
                              }
                              disabled={uploadingImages}
                            >
                              {uploadingImages ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              ) : (
                                <Upload className="h-4 w-4 mr-2" />
                              )}
                              Upload Background
                            </Button>
                          </div>
                          {field.value && (
                            <div className="relative w-40 h-40">
                              <Image
                                src={field.value}
                                alt="Background Image"
                                width={160}
                                height={160}
                                className="w-full h-full object-contain rounded-md"
                              />
                              <button
                                type="button"
                                disabled={isDeletingImage}
                                onClick={() =>
                                  field.value &&
                                  deleteImage(field.value, "club_bg_img_url")
                                }
                                className="absolute -top-2 -right-2 bg-destructive text-destructive-foreground rounded-full p-1"
                              >
                                {isDeletingImage ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <X className="h-4 w-4" />
                                )}
                              </button>
                            </div>
                          )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Location Fields */}
              <FormField
                control={form.control}
                name="club.club_location"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Club Location</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="e.g., Sheikh Zayad Road, UAE"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="club.club_location_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Google Maps URL</FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="https://maps.google.com/..."
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
          </div>
        );

      case 3:
        return (
          <div className="space-y-8" key={"step-3"}>
            <div>
              <h2 className="text-2xl font-semibold text-gray-900">
                Business Settings & Contact
              </h2>
              <p className="mt-1 text-sm text-gray-600">
                Configure your business operations and contact details
              </p>
            </div>

            <div className="space-y-6">
              {/* Payment Settings */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Payment Configuration</h3>
                <div className="space-y-4 p-4 border rounded-lg bg-gray-50">
                  <FormField
                    control={form.control}
                    name="club.payment_config.payment_method"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Payment Gateway Configuration</FormLabel>
                        <Select
                          value={field.value}
                          onValueChange={(value: "ccavenue" | "stripe" | "tap" | "network" | "paytabs") => {
                            field.onChange(value);
                            // Reset other fields when payment method changes
                            form.setValue("club.payment_config", {
                              ...defaultPaymentConfig,
                              payment_method: value,
                            });
                          }}
                        >
                          <SelectTrigger>
                            <SelectValue placeholder="Select payment gateway" />
                          </SelectTrigger>
                          <SelectContent>
                            {/* <SelectItem value="ccavenue">CCAvenue</SelectItem> */}
                            <SelectItem value="stripe">Stripe</SelectItem>
                            <SelectItem value="tap">Tap</SelectItem>
                            <SelectItem value="network">Network</SelectItem>
                            <SelectItem value="paytabs">PayTabs</SelectItem>
                          </SelectContent>
                        </Select>
                      </FormItem>
                    )}
                  />

                  {form.watch("club.payment_config.payment_method") === "ccavenue" && (
                    <>
                      <FormField
                        control={form.control}
                        name="club.payment_config.access_code"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Access Code</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Enter CCAvenue access code" />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="club.payment_config.merchant_id"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Merchant ID</FormLabel>
                            <FormControl>
                              <Input
                                type="number"
                                {...field}
                                onChange={(e) => field.onChange(Number(e.target.value))}
                                placeholder="Enter merchant ID"
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="club.payment_config.working_key"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Working Key</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Enter working key" />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                  {form.watch("club.payment_config.payment_method") === "stripe" && (
                    <>
                      <FormField
                        control={form.control}
                        name="club.payment_config.payment_api_key"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Stripe API Key</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Enter Stripe API key" />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="club.payment_config.payment_webhook_secret"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Stripe Webhook Secret</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Enter Stripe webhook secret" />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                  {form.watch("club.payment_config.payment_method") === "tap" && (
                    <FormField
                      control={form.control}
                      name="club.payment_config.payment_api_key"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Tap API Key</FormLabel>
                          <FormControl>
                            <Input {...field} placeholder="Enter Tap API key" />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                  )}

                  {form.watch("club.payment_config.payment_method") === "network" && (
                    <>
                      <FormField
                        control={form.control}
                        name="club.payment_config.outlet_id"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Outlet ID</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Enter outlet ID" />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="club.payment_config.payment_api_key"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>API Key</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Enter API key" />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </>
                  )}

                  {form.watch("club.payment_config.payment_method") === "paytabs" && (
                    <>
                      <FormField
                        control={form.control}
                        name="club.payment_config.paytabs_profile_id"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>PayTabs Profile ID</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Enter PayTabs profile ID" />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                      <FormField
                        control={form.control}
                        name="club.payment_config.payment_api_key"
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>API Key</FormLabel>
                            <FormControl>
                              <Input {...field} placeholder="Enter API key" />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </>
                  )}
                </div>

                <FormField
                  control={form.control}
                  name="club.club_partial_payment"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Partial Payment Percentage</FormLabel>
                      <FormControl>
                        <Input
                          type="number"
                          {...field}
                          min={0}
                          max={100}
                          onChange={(e) =>
                            field.onChange(Number(e.target.value))
                          }
                        />
                      </FormControl>
                      <FormDescription>
                        Set the percentage (0-100) for partial payments
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              {/* Support Settings */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">
                  Support & Access Settings
                </h3>
                <div className="space-y-4">
                  <FormField
                    control={form.control}
                    name="club.enable_support"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Enable Support
                          </FormLabel>
                          <FormDescription>
                            Allow customers to contact support through the
                            platform
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="club.inbox_access_enabled"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Enable Inbox Access
                          </FormLabel>
                          <FormDescription>
                            Allow staff to access the support inbox
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="club.auto_refundable"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Auto Refundable
                          </FormLabel>
                          <FormDescription>
                            Enable automatic refunds for cancelled bookings
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Social Links */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Social Media Links</h3>
                <div className="grid grid-cols-3 gap-4">
                  <FormField
                    control={form.control}
                    name="club.club_social_links.facebook"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Facebook</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="https://facebook.com/..."
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="club.club_social_links.instagram"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Instagram</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="https://instagram.com/..."
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="club.club_social_links.tiktok"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>TikTok</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            placeholder="https://tiktok.com/..."
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>

              {/* Contact Information */}
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Contact Information</h3>
                <FormField
                  control={form.control}
                  name="club.club_cancellation_number"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Cancellation Phone Number</FormLabel>
                      <FormControl>
                        <Input {...field} placeholder="971XXXXXXXXX" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="club.club_emails"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Club Emails</FormLabel>
                      <div className="space-y-3">
                        {field.value.map((email, index) => (
                          <div key={index} className="flex items-center gap-2">
                            <FormControl>
                              <div className="relative">
                                <Input
                                  placeholder="Enter email address"
                                  value={email}
                                  onChange={(e) => {
                                    const newEmails = [...field.value];
                                    newEmails[index] = e.target.value;
                                    field.onChange(newEmails);
                                    // Trigger validation immediately
                                    form.trigger(`club.club_emails.${index}`);
                                  }}
                                  className={cn(
                                    form.formState.errors.club?.club_emails?.[
                                      index
                                    ] &&
                                      "border-red-500 focus-visible:ring-red-500"
                                  )}
                                />
                                {form.formState.errors.club?.club_emails?.[
                                  index
                                ] && (
                                  <span className="text-xs text-red-500 mt-1 block">
                                    {
                                      form.formState.errors.club.club_emails[
                                        index
                                      ]?.message
                                    }
                                  </span>
                                )}
                              </div>
                            </FormControl>
                            <Button
                              type="button"
                              variant="outline"
                              size="icon"
                              className="shrink-0"
                              onClick={() => {
                                const newEmails = field.value.filter(
                                  (_, i) => i !== index
                                );
                                field.onChange(
                                  newEmails.length > 0 ? newEmails : [""]
                                ); // Ensure at least one empty field
                              }}
                              disabled={field.value.length === 1} // Prevent removing the last field
                            >
                              <X className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                        <Button
                          type="button"
                          variant="outline"
                          size="sm"
                          className="mt-2"
                          onClick={() => {
                            field.onChange([...field.value, ""]);
                          }}
                        >
                          <Plus className="h-4 w-4 mr-2" />
                          Add Email
                        </Button>
                      </div>
                      <FormDescription>
                        Add multiple email addresses for club notifications
                      </FormDescription>
                      {form.formState.errors.club?.club_emails &&
                        !Array.isArray(
                          form.formState.errors.club.club_emails
                        ) && (
                          <FormMessage>
                            {form.formState.errors.club.club_emails.message}
                          </FormMessage>
                        )}
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Business Hours</h3>

                <FormField
                  control={form.control}
                  name="club.club_timings.weekday"
                  render={({ field }) => {
                    const { start, end } = parseTimeRange(field.value);
                    return (
                      <FormItem>
                        <FormLabel>Weekday Hours</FormLabel>
                        <div className="flex items-center gap-4">
                          <div className="flex-1">
                            <FormLabel className="text-sm text-gray-500">
                              Opening Time
                            </FormLabel>
                            <input
                              type="time"
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              value={to24HourFormat(start)}
                              onChange={(e) => {
                                const newStart = to12HourFormat(e.target.value);
                                field.onChange(formatTimeRange(newStart, end));
                              }}
                            />
                          </div>
                          <div className="flex-1">
                            <FormLabel className="text-sm text-gray-500">
                              Closing Time
                            </FormLabel>
                            <input
                              type="time"
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              value={to24HourFormat(end)}
                              onChange={(e) => {
                                const newEnd = to12HourFormat(e.target.value);
                                field.onChange(formatTimeRange(start, newEnd));
                              }}
                            />
                          </div>
                        </div>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />

                <FormField
                  control={form.control}
                  name="club.club_timings.weekend"
                  render={({ field }) => {
                    const { start, end } = parseTimeRange(field.value);
                    return (
                      <FormItem>
                        <FormLabel>Weekend Hours</FormLabel>
                        <div className="flex items-center gap-4">
                          <div className="flex-1">
                            <FormLabel className="text-sm text-gray-500">
                              Opening Time
                            </FormLabel>
                            <input
                              type="time"
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              value={to24HourFormat(start)}
                              onChange={(e) => {
                                const newStart = to12HourFormat(e.target.value);
                                field.onChange(formatTimeRange(newStart, end));
                              }}
                            />
                          </div>
                          <div className="flex-1">
                            <FormLabel className="text-sm text-gray-500">
                              Closing Time
                            </FormLabel>
                            <input
                              type="time"
                              className="flex h-10 w-full rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50"
                              value={to24HourFormat(end)}
                              onChange={(e) => {
                                const newEnd = to12HourFormat(e.target.value);
                                field.onChange(formatTimeRange(start, newEnd));
                              }}
                            />
                          </div>
                        </div>
                        <FormMessage />
                      </FormItem>
                    );
                  }}
                />
              </div>
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  useEffect(() => {
    if (clubData) {
      const clubDataRes = clubData?.data;
      if (!clubDataRes) return;
      setIsOnlyForListing(clubDataRes?.is_only_for_listing || false);
      form.reset({
        club: {
          club_name: clubDataRes.club_name,
          club_id: clubDataRes.club_id,
          phone_number: clubDataRes.phone_number,
          club_logo_url: clubDataRes.club_logo_url || "",
          club_bg_img_url: clubDataRes.club_bg_img_url || "",
          club_location: clubDataRes.club_location || "",
          club_location_url: clubDataRes.club_location_url || "",
          club_social_links: clubDataRes.club_social_links || {},
          club_cancellation_number: clubDataRes.club_cancellation_number || "",
          club_timings: clubDataRes.club_timings || {
            weekday: "06:00 AM to 12:00 AM",
            weekend: "06:00 AM to 12:00 AM",
          },
          club_emails: clubDataRes.club_emails || [""],
          payment_config: clubDataRes.payment_config || defaultPaymentConfig,
          enable_support: clubDataRes.enable_support || false,
          inbox_access_enabled: clubDataRes.inbox_access_enabled || false,
          club_partial_payment: clubDataRes.club_partial_payment || 0,
          auto_refundable: clubDataRes.auto_refundable || false,
          is_only_for_listing: clubDataRes.is_only_for_listing || false,
        },
      });
    }
  }, [clubData, form]);

  if (error) {
    return (
      <main className="flex-1 flex flex-col bg-gradient-to-b  ">
        <ErrorMessage message={error.message} />
      </main>
    );
  }
  return (
    <main className="flex-1 flex flex-col bg-gradient-to-b  ">
      {isLoading ? (
        <div className="flex-1 flex items-center justify-center">
          <div className="flex flex-col items-center space-y-4">
            <Loader2 className="h-8 w-8 animate-spin text-primary" />
            <p className="text-sm text-gray-500">Loading Club data...</p>
          </div>
        </div>
      ) : (
        <>
          {/* Header Section with Improved Navigation */}
          <div className="bg-white border-b shadow-sm">
            <div className="max-w-[1400px] mx-auto py-4 px-6">
              <div className="flex flex-col space-y-4">
                {/* Title Section */}
                <div className="flex flex-col md:flex-row justify-between items-start md:items-center">
                  <div className="flex items-center gap-4">
                    <Link href="/admin/sports">
                      <Button variant="outline" size="icon" className="h-8 w-8">
                        <ChevronLeft className="h-4 w-4" />
                      </Button>
                    </Link>
                    <div className="space-y-1">
                      <h1 className="text-2xl font-semibold text-gray-900">
                        {clubId ? "Edit Sports Club" : "New Sports Club"}
                      </h1>
                      <p className="text-sm text-gray-500">
                        {clubId
                          ? "Update your sports club details"
                          : "Configure a new sports club on Cravin"}
                      </p>
                    </div>
                  </div>
                  <div className="flex flex-col">
                    {clubId && (
                      <div className="flex flex-wrap items-center gap-3 mt-3 md:mt-0">
                        <Link href="/admin/sports">
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-9 flex items-center gap-2"
                          >
                            <Store className="h-4 w-4" />
                            <span className="font-medium">All Clubs</span>
                          </Button>
                        </Link>

                        {!isOnlyForListing && (
                          <WhatsAppSetupButton
                            businessType="sports"
                            businessId={clubId}
                          />
                        )}

                        <Link
                          href={`/admin/sports/onboarding/listing-details?id=${clubId}`}
                        >
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-9 flex items-center gap-2 bg-white hover:bg-orange-50 text-orange-600 border-orange-200 hover:border-orange-300 transition-all duration-200"
                          >
                            <Building2 className="h-4 w-4" />
                            <span className="font-medium">Court Listings</span>
                          </Button>
                        </Link>

                        {!isOnlyForListing && (
                          <Button
                            variant="secondary"
                            size="sm"
                            className="h-9 flex items-center gap-2"
                            onClick={() => {
                              setOnboardingComplete(true);
                            }}
                          >
                            <ShieldCheck className="h-4 w-4" />
                            <span className="font-medium">Setup Logins</span>
                          </Button>
                        )}
                      </div>
                    )}
                    {clubId &&
                      !clubData?.data?.is_only_for_listing && (
                      <div className="flex items-center space-x-6 mt-4">
                        {clubData?.data?.phone_number && (
                        <a
                          href={`https://wa.me/${clubData?.data?.phone_number}`}
                          target="_blank"
                          rel="noopener noreferrer"
                          className="flex items-center space-x-2 text-green-600 hover:text-green-700"
                        >
                          <FaWhatsapp className="h-4 w-4" />
                          <span className="text-sm font-medium">
                          WhatsApp Number{" "}
                          </span>
                        </a>
                        )}
                        <a
                        href={`https://customer.justcravin.com/${clubId}/booking`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-2 text-primary hover:text-primary/80"
                        >
                        <Globe className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          View Customer Menu
                        </span>
                        </a>

                        <a
                        href={"https://sports.justcravin.com/"}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="flex items-center space-x-2 text-primary hover:text-primary/80"
                        >
                        <Globe className="h-4 w-4" />
                        <span className="text-sm font-medium">
                          Merchant Dashboard
                        </span>
                        </a>
                      </div>
                      )}
                  </div>
                </div>

                {/* Breadcrumb Navigation */}
                {clubId && (
                  <div className="flex items-center text-sm text-muted-foreground">
                    <Link href="/admin/sports" className="hover:text-primary">
                      Sports Clubs
                    </Link>
                    <ArrowLeft className="h-3 w-3 mx-2 rotate-180" />
                    <span className="text-foreground font-medium">
                      Club Details
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>

          {/* Content Area */}
          <div className="flex-1 flex flex-col min-h-0">
            <div className="flex-1 overflow-auto">
              <div className="max-w-[1400px] mx-auto py-6 px-6">
                {onboardingComplete && clubId ? (
                  <ClubLoginSetup
                    clubId={clubId}
                    onBack={handleBackToOnboarding}
                  />
                ) : (
                  <Card className="border shadow-sm">
                    <Form {...form}>
                      <form
                        onSubmit={form.handleSubmit(onSubmit)}
                        className="flex flex-col min-h-0"
                      >
                        {/* Form Content */}
                        <div className="p-6 space-y-6  ">
                          {renderStepContent()}
                        </div>

                        {/* Navigation */}
                        {!onboardingComplete && (
                          <div className="border-t p-4 bg-white mt-auto">
                            <div className="flex justify-between items-center max-w-[1400px] mx-auto">
                              <Button
                                type="button"
                                variant="outline"
                                onClick={prevStep}
                                disabled={step === 1}
                                className="h-9 w-[100px]"
                              >
                                <ChevronLeft className="h-4 w-4 mr-2" />
                                Back
                              </Button>

                              <div className="flex items-center gap-4">
                                {step === totalSteps && (
                                  <Button
                                    type="submit"
                                    disabled={isSubmitting}
                                    className="h-9 w-[140px]"
                                  >
                                    {isSubmitting ? (
                                      <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Submitting...
                                      </>
                                    ) : (
                                      <>
                                        Update Details
                                        <ChevronRight className="h-4 w-4 ml-2" />
                                      </>
                                    )}
                                  </Button>
                                )}
                                {step === 1 && !clubId && (
                                  <Button
                                    type="button"
                                    onClick={nextStep}
                                    disabled={isSubmitting}
                                    className="h-9 w-[140px]"
                                  >
                                    {isSubmitting ? (
                                      <>
                                        <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                        Creating...
                                      </>
                                    ) : (
                                      <>
                                        Create Club
                                        <ChevronRight className="h-4 w-4 ml-2" />
                                      </>
                                    )}
                                  </Button>
                                )}
                                {step < totalSteps && clubId && (
                                  <Button
                                    type="button"
                                    onClick={nextStep}
                                    className="h-9 w-[100px]"
                                  >
                                    Next
                                    <ChevronRight className="h-4 w-4 ml-2" />
                                  </Button>
                                )}
                              </div>
                            </div>
                          </div>
                        )}
                      </form>
                    </Form>
                  </Card>
                )}
              </div>
            </div>
          </div>
        </>
      )}
    </main>
  );
}

export default function MainOnboardingPage({ token }: { token: string }) {
  return (
    <Suspense
      fallback={<Loader2 className="h-8 w-8 animate-spin text-primary" />}
    >
      <OnboardingPage token={token} />
    </Suspense>
  );
}
