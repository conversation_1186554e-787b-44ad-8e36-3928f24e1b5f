"use client";
import { But<PERSON> } from "@/components/ui/button";
import React, { useEffect, useState } from "react";
import Image from "next/image";
import cravinLogo from "@/public/Cravin black stroke Logo.png";
import {
  Select,
  SelectContent,
  SelectGroup,
  SelectItem,
  SelectLabel,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { VscSearch } from "react-icons/vsc";
// import Fuse from "fuse.js";
import { useRouter, usePathname, useSearchParams } from "next/navigation";
import CommerceMerchantCard from "@/components/commerce/commerce-merchant-card";
import Link from "next/link";
import { MdSearch } from "react-icons/md";
import PaginationListings from "@/components/pagination-listings/pagination-listings";
import { MapPin } from "lucide-react";
import CommonFooter from "@/components/common-footer";
import wideplaceholder from "@/public/PLACEHOLDER3.png";
import smallcommercebanner from "@/public/Small banner- Commerce.png";

export default function Commerce({
  queryEmirate,
  querySearch,
  commerceListData,
  currentPage,
  totalPages,
  isLoading,
  refreshLocation,
}: {
  queryEmirate: string | null;
  querySearch: string | null;
  commerceListData: any;
  currentPage: number;
  totalPages: number;
  isLoading: boolean;
  refreshLocation: () => void;
}) {
  // console.log({
  //   queryEmirate: queryEmirate,
  //   querySearch: querySearch,
  //   queryLatitude: queryLatitude,
  //   queryLongitude: queryLongitude,
  // });

  const router = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const [selectedEmirate, setSelectedEmirate] = useState(
    queryEmirate ? queryEmirate : "All"
  );

  // console.log(selectedEmirate);

  const [searchInput, setSearchInput] = useState(
    querySearch ? querySearch : ""
  );

  // console.log(searchInput);

  //removed when we made filters backend
  // const [shopsInLabel, setShopsInLabel] = useState("");
  // const [commerceListDisplayData, setCommerceListDisplayData] =
  //   useState<any>(commerceListData);

  //   console.log(commerceListData);

  // const searchOptions = {
  //   includeScore: true,
  //   threshold: 0.2,
  //   distance: 100,
  //   keys: ["shopName", "branchName", "branchTags"],
  // };

  // const fuse = new Fuse(commerceListData, searchOptions);

  const navigateToPage = (pageNumber: number) => {
    const params = new URLSearchParams(searchParams.toString());
    params.set("page", pageNumber.toString());

    if (selectedEmirate && selectedEmirate !== "All") {
      params.set("emirate", selectedEmirate);
    } else {
      params.delete("emirate");
    }

    if (searchInput) {
      params.set("search", searchInput);
    } else {
      params.delete("search");
    }

    router.push(`${pathname}?${params.toString()}`);
  };

  const applySearch = () => {
    const params = new URLSearchParams();

    if (selectedEmirate && selectedEmirate !== "All") {
      params.set("emirate", selectedEmirate);
    }

    if (searchInput) {
      params.set("search", searchInput);
    }

    params.set("page", "1"); //whenever we change emirate or search something we have to reset page number

    router.push(`${pathname}?${params.toString()}`);
  };

  //On may 12 2025, we removing this as discussed in call to do pagination and no more frontend filter because of the same reason
  // const getFilteredList = () => {
  //   if (!searchInput || searchInput.length <= 0) {
  //     const emiratesFilter = commerceListData.filter((item: any) => {
  //       if (
  //         !selectedEmirate ||
  //         selectedEmirate.length <= 0 ||
  //         selectedEmirate === "All" ||
  //         item.branchEmirate === selectedEmirate
  //       ) {
  //         return true;
  //       }
  //     });
  //     // console.log(emiratesFilter);
  //     setCommerceListDisplayData(emiratesFilter);
  //     setShopsInLabel(selectedEmirate);
  //   } else {
  //     const result = fuse.search(searchInput);
  //     // console.log(result);
  //     const filteredList = result.map((item) => item.item);
  //     // console.log(filteredList);
  //     const emiratesFilter = filteredList.filter((item: any) => {
  //       if (
  //         !selectedEmirate ||
  //         selectedEmirate.length <= 0 ||
  //         selectedEmirate === "All" ||
  //         item.branchEmirate === selectedEmirate
  //       ) {
  //         return true;
  //       }
  //     });
  //     // console.log(emiratesFilter);
  //     setCommerceListDisplayData(emiratesFilter);
  //     setShopsInLabel(selectedEmirate);
  //   }
  // };

  // useEffect(() => {
  //   if (commerceListDisplayData && commerceListDisplayData.length > 0) {
  //     getFilteredList();
  //   }
  // }, []);

  return (
    <div className="h-full min-h-[100dvh] w-full flex flex-col items-center justify-between bg-[#f1f3f2]">
      <header className="w-full bg-white min-h-20">
        <div className="h-full w-full max-w-[1220px] flex justify-between items-center px-4 py-2 mx-auto gap-10">
          <Link href="/" className="relative w-36 h-16 hidden sm:block">
            <Image
              alt="Cravin"
              src={cravinLogo}
              fill
              sizes="160px"
              className="object-contain"
              priority
            />
          </Link>

          <div className="flex flex-col-reverse sm:flex-row gap-4 mb-2 sm:mb-0 w-full sm:w-fit">
            <div className="flex gap-2 border-2 border-black/30 rounded-full p-2">
              <Select
                defaultValue={selectedEmirate}
                onValueChange={(value) => setSelectedEmirate(value)}
              >
                <SelectTrigger className="w-[200px] border-none h-6 text-xs sm:text-sm">
                  <SelectValue placeholder="Select An Emirate" />
                </SelectTrigger>
                <SelectContent>
                  <SelectGroup>
                    <SelectLabel className="text-xs sm:text-sm">
                      Emirates
                    </SelectLabel>
                    <SelectItem className="text-xs sm:text-sm" value="All">
                      All
                    </SelectItem>
                    <SelectItem
                      className="text-xs sm:text-sm"
                      value="Abu Dhabi"
                    >
                      Abu Dhabi
                    </SelectItem>
                    <SelectItem className="text-xs sm:text-sm" value="Dubai">
                      Dubai
                    </SelectItem>
                    <SelectItem className="text-xs sm:text-sm" value="Sharjah">
                      Sharjah
                    </SelectItem>
                    <SelectItem className="text-xs sm:text-sm" value="Ajman">
                      Ajman
                    </SelectItem>
                    <SelectItem
                      className="text-xs sm:text-sm"
                      value="Umm Al Quwain"
                    >
                      UAQ
                    </SelectItem>
                    <SelectItem
                      className="text-xs sm:text-sm"
                      value="Ras Al Khaimah"
                    >
                      RAK
                    </SelectItem>
                    <SelectItem className="text-xs sm:text-sm" value="Fujairah">
                      Fujairah
                    </SelectItem>
                  </SelectGroup>
                </SelectContent>
              </Select>

              <div className="border border-black/30 " />

              <Input
                className="w-full border-none placeholder:text-black/60 outline-none h-6 text-xs sm:text-sm"
                placeholder="Search for shops."
                value={searchInput}
                onChange={(e) => setSearchInput(e.target.value)}
              />

              <Button
                className="rounded-full bg-black hover:bg-black px-3 h-6"
                onClick={() => {
                  applySearch();
                }}
              >
                <VscSearch strokeWidth={2} />
              </Button>
            </div>
            <div className="flex gap-2 items-center justify-between">
              <Link href="/" className="relative w-36 h-16 sm:hidden">
                <Image
                  alt="Cravin"
                  src={cravinLogo}
                  fill
                  sizes="160px"
                  className="object-contain"
                  priority
                />
              </Link>

              <Button
                className="rounded-full bg-black hover:bg-black px-3 inline-flex items-center gap-1"
                onClick={() => {
                  refreshLocation();
                }}
              >
                <MapPin size={20} /> <span>Find Me</span>
              </Button>
            </div>
          </div>
        </div>
      </header>
      <main className="h-full w-full max-w-[1220px] flex flex-col items-center mb-12 px-4 gap-6">
        <div className="w-full overflow-hidden rounded-xl relative aspect-[1186/210] mt-6 sm:mt-12">
          <Image
            src={smallcommercebanner}
            alt="Wider banner image"
            fill
            sizes="(max-width: 1200px) 100vw, 1186px"
            priority
            style={{
              objectFit: "cover",
            }}
          />
        </div>
        <div className="w-full justify-center items-start">
          <h1 className="font-medium text-2xl md:text-4xl text-center">
            Outlets In{" "}
            <span className="font-semibold">
              {queryEmirate && queryEmirate !== "All" ? queryEmirate : "UAE"}
            </span>
          </h1>
        </div>

        <div className="w-full flex flex-wrap justify-center items-center mx-auto gap-x-4 gap-y-6 sm:py-5">
          {(isLoading || !commerceListData) && (
            <div className="w-full h-[50dvh] flex flex-col items-center justify-center py-10 px-4">
              <div className="animate-spin rounded-full h-14 w-14 border-4 border-t-transparent border-gray-400 mb-4 shadow-sm" />

              <p className="text-sm text-center text-gray-500 mt-2 max-w-md">
                If loading takes longer than expected, please check your
                internet connection or try again later.
              </p>
            </div>
          )}

          {commerceListData &&
            commerceListData.length > 0 &&
            commerceListData.map((shop: any) => (
              <CommerceMerchantCard key={shop.branchId} shop={shop} />
            ))}

          {commerceListData && commerceListData.length <= 0 && (
            <div className="w-full h-[50dvh] flex flex-col items-center justify-center py-10">
              <MdSearch size={80} className="text-gray-300 mb-4" />
              <p className="text-xl font-normal text-gray-600">
                No matching results
              </p>
              <p className="text-sm text-gray-500 mt-2">
                Try adjusting your filters or search criteria
              </p>
            </div>
          )}
        </div>

        {totalPages && totalPages > 1 && (
          <PaginationListings
            currentPage={currentPage}
            navigateToPage={navigateToPage}
            totalPages={totalPages}
          />
        )}
      </main>

      <CommonFooter />
    </div>
  );
}
