import React, { useState, useEffect } from "react";

const RotatingWords = () => {
  const words = [
    "Badminton",
    "Football",
    "Biryani",
    "Lipstick",
    "Padel",
    "Fried Chicken",
    "Sunscreen",
    "Pizza",
    "Basketball",
    "Foundation",
    "Volleyball",
    "Squash",
    "Mascara",
    "Noodles",
    "Tennis",
    "Blush",
    "Burgers",
    "Cricket",
    "Futsal",
    "Perfume",
    "Table Tennis",
    "Golf",
    "Face Wash",
    "Shampoo",
    "Sushi",
    "Moisturizer",
    "Pasta",
    "Highlighter",
    "Eyeshadow",
    "Ice Cream",
    "Lip Balm",
    "Cookies",
    "Paneer Tikka",
    "Chole Bhature",
    "Idli",
    "Dosa",
    "Samosa",
    "Rogan Josh",
    "Tandoori Chicken",
    "Butter Chicken",
    "Aloo Paratha",
    "Gulab Jamun",
    "Pizza",
    "Paneer Butter Masala",
    "<PERSON><PERSON> Bhaji",
    "Chowmein",
    "<PERSON>tton Curry",
    "<PERSON>sala Dosa",
    "<PERSON> Makhani",
    "<PERSON>a Masala",
    "<PERSON><PERSON><PERSON>",
    "<PERSON><PERSON>",
    "Malai Kofta",
    "Shahi Paneer",
    "Nail Polish",
    "Conditioner",
    "Serum",
    "Concealer",
    "Eyeliner",
    "Hand Cream",
    "Lip Gloss",
    "Hair Oil",
    "Biriyani",
    "Veg Pulao",
    "Saag Paneer",
    "Keema Naan",
    "Tikka Masala",
    "Pizza",
    "Tandoori Roti",
    "Fish Curry",
    "Tikka Kebab",
    "D<PERSON>kla",
    "Bhel Puri",
    "Curry",
    "Poha",
    "Kheer",
    "Chapati",
    "Pizza",
    "Burgers",
    "Spaghetti",
    "Garlic Bread",
    "Caesar Salad",
    "Brownie",
    "French Fries",
    "Muffin",
    "Lip Tint",
    "Face Serum",
    "Body Lotion",
    "Eye Cream",
    "BB Cream",
    "Facial Scrub",
    "Body Mist",
    "Hair Serum",
    "Mousse",
    "Body Wash",
    "Hair Spray",
    "Cuticle Oil",
    "Eyebrow Gel",
    "Setting Spray",
  ];

  const [currentWordIndex, setCurrentWordIndex] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentWordIndex((prevIndex) => (prevIndex + 1) % words.length);
    }, 2000);

    return () => clearInterval(interval); // Cleanup the interval on component unmount
  }, []);

  return (
    <div className="w-full font-medium text-5xl sm:text-7xl text-center capitalize">
      Are you Cravin{" "}
      <span className="font-bold break-words">{words[currentWordIndex]}</span>
    </div>
  );
};

export default RotatingWords;
