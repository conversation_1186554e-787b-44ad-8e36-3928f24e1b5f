"use client";
export interface SocialLinks {
  twitter?: string;
  website?: string;
  facebook?: string;
  instagram?: string;
  tiktok?: string;
}

import { FaTwitter, FaGlobe, FaFacebook, FaTiktok } from "react-icons/fa";
import { SiInstagram } from "react-icons/si";

interface SocialLinksProps {
  data: SocialLinks;
}

const ListingSocialLinks: React.FC<SocialLinksProps> = ({ data }) => {
  const socialIcons = {
    twitter: <FaTwitter size={22} />,
    website: <FaGlobe size={22} />,
    facebook: <FaFacebook size={22} />,
    instagram: <SiInstagram size={22} />,
    tiktok: <FaTiktok size={22} />,
  };

  if (Object.keys(data).length === 0) {
    return null;
  }

  return (
    <div className="flex gap-3">
      {Object.entries(data).map(([platform, url]) => {
        if (!url) return null;

        return (
          <a
            key={platform}
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="text-gray-600 hover:text-gray-900 transition-colors"
          >
            {socialIcons[platform as keyof typeof socialIcons]}
          </a>
        );
      })}
    </div>
  );
};

export default ListingSocialLinks;
