import React from "react";
import {
  MdAccessTimeFilled,
  MdDirectionsBike,
  MdLocationPin,
  MdMyLocation,
} from "react-icons/md";
import Image from "next/image";
import testlistingimg from "@/public/Cravin_food_placeholder.png";
import { Badge } from "../ui/badge";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import { useRouter } from "next/navigation";
dayjs.extend(customParseFormat);

const FoodMerchantCard = ({ restaurant }: { restaurant: any }) => {
  const router = useRouter();
  const currentDayOfWeek = dayjs().day();

  const timing = Array.isArray(restaurant?.branchTiming)
    ? restaurant.branchTiming.find(
        (timing: any) => timing.day === currentDayOfWeek
      )
    : null;
  return (
    <div
      className="w-full h-full max-w-[384px] min-h-[354px] flex flex-col items-start justify-start bg-white rounded-xl cursor-pointer overflow-hidden border border-gray-100 transition-all duration-300 hover:drop-shadow-lg hover:shadow-lg"
      onClick={() => {
        router.push(`/food/${restaurant.branchId}`);
      }}
    >
      <div className="relative w-full h-[206px] overflow-hidden">
        <Image
          alt={restaurant?.branchDisplayName || "Restaurant listing image"}
          src={
            restaurant.listingImage && restaurant.listingImage.length > 0
              ? restaurant.listingImage
              : testlistingimg
          }
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          style={{
            objectFit: "cover",
          }}
        />
        {!restaurant?.onlyForListing && (
          <div className="absolute top-0 right-5 bg-gradient-to-l from-red-500 to-red-600 text-white py-1 px-2 rounded-bl-lg rounded-br-lg font-medium shadow text-xs">
            Order with Cravin
          </div>
        )}

        {/* <div className="absolute top-3 left-3 bg-gray-200/80 opacity-50 text-gray-700 py-1 px-2 rounded-md text-[10px] font-medium ">
          Cravin Food
        </div> */}
      </div>

      <div className="flex flex-col gap-2 w-full p-4 font-normal text-sm">
        <p className="text-lg font-medium line-clamp-1">
          {restaurant?.branchDisplayName}
        </p>
        <div className="flex w-full justify-between gap-2">
          <div className="flex items-center gap-2 text-gray-700 font-normal">
            <MdAccessTimeFilled
              size={18}
              className="text-gray-700 flex-shrink-0"
            />
            {restaurant?.branchTiming && timing && (
              <div className="line-clamp-1 w-full">
                {timing.start} to {timing.end}
              </div>
            )}
          </div>
          {restaurant?.averageSpend && (
            <Badge className="text-xs max-w-[180px] line-clamp-1 font-normal text-gray-700 bg-gray-100 hover:bg-gray-200 px-2 py-1">
              AED {restaurant.averageSpend} for two
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2 text-gray-700 font-normal">
          <MdLocationPin size={18} className="text-gray-700 flex-shrink-0" />
          <p className="line-clamp-1">{restaurant?.address}</p>
        </div>

        {restaurant?.distanceFromUser && (
          <div className="flex w-full justify-between">
            <div className="flex items-center gap-2 text-gray-700 font-normal">
              <MdMyLocation size={18} className="text-gray-700 flex-shrink-0" />
              <p className="line-clamp-1">
                {restaurant?.distanceFromUser.toFixed(1)} km away
              </p>
            </div>

            {restaurant?.deliverable && (
              <Badge className="bg-green-100 hover:bg-green-200 text-green-700 border-blue-200 text-xs font-normal flex items-center gap-1">
                <MdDirectionsBike size={14} />
                Delivers to your location
              </Badge>
            )}
          </div>
        )}

        <div className="w-full truncate mt-2">
          {restaurant?.branchTags &&
            restaurant?.branchTags.length > 0 &&
            restaurant?.branchTags?.map((tag: any) => (
              <Badge
                key={tag}
                className="bg-gray-100 hover:bg-gray-200 text-gray-700 border-none font-normal mr-1  capitalize"
                variant="outline"
              >
                {tag}
              </Badge>
            ))}
        </div>
      </div>
    </div>
  );
};

export default FoodMerchantCard;
