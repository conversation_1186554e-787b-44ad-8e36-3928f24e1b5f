import { HydrationBoundary, dehydrate } from "@tanstack/react-query";
import { queryClient } from "@/lib/query-client";
import FoodProfileParent from "./food-profile-parent";
import { Metadata, ResolvingMetadata } from "next";

type Props = {
  params: { id: string };
};

export async function generateMetadata(
  { params }: Props,
  parent: ResolvingMetadata
): Promise<Metadata> {
  const profile = params;
  const branchId = profile.id;

  const food_response = await fetch(
    process.env.NEXT_PUBLIC_FOOD_BACKEND_URL +
      "/branches/landing-page/profile-page-details/" +
      branchId
  );
  const food_data = await food_response.json();
  return {
    title:
      food_data?.data?.restaurantName &&
      food_data?.data?.restaurantName.length > 0 &&
      food_data?.data?.branchName &&
      food_data?.data?.branchName.length > 0
        ? `${food_data?.data?.restaurantName}, ${food_data?.data?.branchName}`
        : "Restaurant On Cravin Food",
    description:
      food_data?.data?.knownFor && food_data?.data?.knownFor.length > 0
        ? food_data?.data?.knownFor
        : "Restaurant Enlisted On Cravin Food",
    icons: {
      icon: "/Cravin_logo_favi.png",
    },
    openGraph: {
      title:
        food_data?.data?.restaurantName &&
        food_data?.data?.restaurantName.length > 0 &&
        food_data?.data?.branchName &&
        food_data?.data?.branchName.length > 0
          ? `${food_data?.data?.restaurantName}, ${food_data?.data?.branchName}`
          : "Restaurant On Cravin Food",
      description:
        food_data?.data?.knownFor && food_data?.data?.knownFor.length > 0
          ? food_data?.data?.knownFor
          : "Restaurant Enlisted On Cravin Food",
      url: `https://justcravin.com/food/${branchId}`,
      type: "website",
    },
    alternates: {
      canonical: "https://justcravin.com",
    },
  };
}

export default async function Page({ params }: { params: { id: string } }) {
  //prefetch will be here
  const profile = params;
  const branchId = profile.id;
  // console.log(branchId);

  await queryClient.prefetchQuery({
    queryKey: ["getProfileInfo", branchId],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_FOOD_BACKEND_URL +
          "/branches/landing-page/profile-page-details/" +
          branchId
      );
      const data = await response.json();
      return data.data;
    },
  });

  await queryClient.prefetchQuery({
    queryKey: ["getFoodFAQ", branchId],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_FOOD_BACKEND_URL +
          "/branches/landing-page/get-branch-faqs/" +
          branchId
      );
      const data = await response.json();
      return data.data;
    },
  });

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <FoodProfileParent branchId={branchId} />
    </HydrationBoundary>
  );
}
