import React from "react";
import {
  MdAccessTimeFilled,
  MdDirectionsBike,
  MdLocationPin,
  MdMyLocation,
} from "react-icons/md";
import Image from "next/image";
import testlistingimg from "@/public/Cravin_food_placeholder.png";
import { Badge } from "../ui/badge";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import { useRouter } from "next/navigation";
import CardIconSet from "./card-icon-set";
dayjs.extend(customParseFormat);

const SportsMerchantCard = ({ club }: { club: any }) => {
  const router = useRouter();
  const currentDayOfWeek = dayjs().day();

  return (
    <div
      className="w-full h-full max-w-[384px] min-h-[354px] flex flex-col items-start justify-start bg-white rounded-xl cursor-pointer overflow-hidden border border-gray-100 transition-all duration-300 hover:drop-shadow-lg hover:shadow-lg"
      onClick={() => {
        router.push(`/sports/${club.clubId}`);
      }}
    >
      <div className="relative w-full h-[206px] overflow-hidden">
        <Image
          alt={club?.clubName || "Club listing image"}
          src={
            club.listingImage && club.listingImage.length > 0
              ? club.listingImage
              : testlistingimg
          }
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          style={{
            objectFit: "cover",
          }}
        />
        {!club?.onlyForListing && (
          <div className="absolute top-0 right-5 bg-[#d5ff01] text-black py-1 px-2 rounded-bl-lg rounded-br-lg font-medium shadow text-xs">
            Book with Cravin
          </div>
        )}
      </div>

      <div className="flex flex-col gap-2 w-full p-4 font-normal text-sm">
        <p className="text-lg font-medium line-clamp-1">{club?.clubName}</p>
        <div className="flex justify-start items-center gap-1 w-full line-clamp-1">
          {club?.facilities &&
            club?.facilities.length > 0 &&
            club?.facilities?.map((facility: any) => (
              <div className="flex max-w-6" key={facility.facilityName}>
                <CardIconSet text={facility.facilityCategory} />
              </div>
            ))}
        </div>
        <div className="flex w-full justify-between gap-2">
          <div className="flex items-center gap-2 text-gray-700 font-normal">
            <MdAccessTimeFilled
              size={18}
              className="text-gray-700 flex-shrink-0"
            />
            {club?.clubTimings &&
              (currentDayOfWeek === 0 || currentDayOfWeek === 6 ? (
                <div className="line-clamp-1 w-full">
                  {club?.clubTimings?.weekend}
                </div>
              ) : (
                <div className="line-clamp-1 w-full">
                  {club?.clubTimings?.weekday}
                </div>
              ))}
          </div>
          {club?.averageSpend && (
            <Badge className="text-xs max-w-[180px] line-clamp-1 font-normal text-gray-700 bg-gray-100 hover:bg-gray-200 px-2 py-1">
              AED {club.averageSpend} for one
            </Badge>
          )}
        </div>

        <div className="flex items-center gap-2 text-gray-700 font-normal">
          <MdLocationPin size={18} className="text-gray-700 flex-shrink-0" />
          <p className="line-clamp-1">{club?.clubLocation}</p>
        </div>

        {club?.distanceFromUser && (
          <div className="flex w-full justify-between">
            <div className="flex items-center gap-2 text-gray-700 font-normal">
              <MdMyLocation size={18} className="text-gray-700 flex-shrink-0" />
              <p className="line-clamp-1">
                {club?.distanceFromUser.toFixed(1)} km away
              </p>
            </div>
          </div>
        )}

        <div className="w-full truncate mt-2">
          {club?.clubTags &&
            club?.clubTags.length > 0 &&
            club?.clubTags?.map((tag: any) => (
              <Badge
                key={tag}
                className="bg-gray-100 hover:bg-gray-200 text-gray-700 border-none font-normal mr-1  capitalize"
                variant="outline"
              >
                {tag}
              </Badge>
            ))}
        </div>
      </div>
    </div>
  );
};

export default SportsMerchantCard;
