{"extends": "next/core-web-vitals", "rules": {"import/no-extraneous-dependencies": ["error", {"devDependencies": false, "optionalDependencies": false, "peerDependencies": false}], "@typescript-eslint/no-unsafe-assignment": "off", "@typescript-eslint/no-useless-template-literals": "off", "no-nested-ternary": "off", "@typescript-eslint/no-unused-vars": "off", "@typescript-eslint/no-unnecessary-condition": "off", "@typescript-eslint/naming-convention": "off", "react/no-unstable-nested-components": "off", "@typescript-eslint/no-shadow": "off", "jsx-a11y/heading-has-content": "off", "react/jsx-sort-props": "off"}}