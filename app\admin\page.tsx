"use client";
import React, { useState } from "react";
import {
  <PERSON>,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import {
  Utensils,
  Trophy,
  Store,
  BarChart3,
  ArrowUpRight,
  Filter,
  ArrowUp,
  Calendar,
  ChevronRight,
  Loader2,
  AlertCircle,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useQuery } from "@tanstack/react-query";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { useSession } from "next-auth/react";

// Define types for the dashboard statistics
interface DashboardStats {
  totalListings: {
    count: number;
    newThisMonth: number;
    breakdown: {
      restaurants: number;
      sportsClubs: number;
      shops: number;
    };
  };
  restaurants: {
    count: number;
    newThisMonth: number;
  };
  sportsClubs: {
    count: number;
    newThisMonth: number;
  };
  shops: {
    count: number;
    newThisMonth: number;
  };
}

const fetchDashboardStats = async (
  token: string | undefined
): Promise<DashboardStats> => {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_FOOD_BACKEND_URL}/restaurants/onboarding-dashboard/stats`,
    {
      headers: {
        accept: "*/*",
        Authorization: token ? `Bearer ${token}` : "",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch dashboard statistics");
  }

  const data = await response.json();
  return data.data;
};

const DashboardPage = () => {
  const { data: session } = useSession();
  const accessToken = session?.user.access_token;

  const {
    data: dashboardData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["dashboardStats", accessToken],
    queryFn: () => fetchDashboardStats(accessToken),
    enabled: !!accessToken, // Only run query when access token is available
    // Default data to display while loading
    placeholderData: {
      totalListings: {
        count: 0,
        newThisMonth: 0,
        breakdown: {
          restaurants: 0,
          sportsClubs: 0,
          shops: 0,
        },
      },
      restaurants: {
        count: 0,
        newThisMonth: 0,
      },
      sportsClubs: {
        count: 0,
        newThisMonth: 0,
      },
      shops: {
        count: 0,
        newThisMonth: 0,
      },
    },
  });

  if (error) {
    return (
      <div className="container mx-auto py-10 space-y-10 max-w-7xl">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load dashboard data. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className=" mx-auto py-10 space-y-10 max-w-7xl">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 pb-6 border-b">
        <div>
          <h1 className="text-3xl font-bold tracking-tight text-gray-900">
            Dashboard
          </h1>
          <p className="text-muted-foreground mt-2 text-base">
            Welcome back! {"Here's"} an overview of your business listings.
          </p>
        </div>
      </div>

      {/* Business Overview */}
      <div>
        <h2 className="text-xl font-semibold mb-4 text-gray-800">
          Business Overview
        </h2>
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          <Card className="overflow-hidden border-none shadow-md transition-all hover:shadow-lg bg-gradient-to-br from-white to-slate-50">
            <CardHeader className="pb-2">
              <CardTitle className="flex items-center gap-2 text-base font-medium text-gray-700">
                <BarChart3 className="h-5 w-5 text-primary" />
                Total Listings
              </CardTitle>
            </CardHeader>
            <CardContent className="pb-3">
              <div className="flex justify-between items-baseline">
                <div className="text-4xl font-bold text-gray-900">
                  {isLoading ? (
                    <Loader2 className="h-8 w-8 animate-spin text-muted" />
                  ) : (
                    dashboardData?.totalListings.count
                  )}
                </div>
                <Badge
                  variant="outline"
                  className="bg-green-50 text-green-700 border-green-200"
                >
                  <ArrowUp className="h-3 w-3 mr-1" />
                  {dashboardData?.totalListings.newThisMonth} new
                </Badge>
              </div>
              <p className="text-xs text-muted-foreground mt-1">
                Updated today
              </p>
            </CardContent>
            <CardFooter className="bg-muted/20 py-3 border-t border-gray-100">
              <div className="flex justify-between w-full text-xs">
                <span className="flex items-center text-muted-foreground">
                  <Utensils className="h-3 w-3 mr-1 text-red-500" />
                  {dashboardData?.totalListings.breakdown.restaurants}{" "}
                  Restaurants
                </span>
                <span className="flex items-center text-muted-foreground">
                  <Trophy className="h-3 w-3 mr-1 text-green-500" />
                  {dashboardData?.totalListings.breakdown.sportsClubs} Sports
                </span>
                <span className="flex items-center text-muted-foreground">
                  <Store className="h-3 w-3 mr-1 text-orange-500" />
                  {dashboardData?.totalListings.breakdown.shops} Shops
                </span>
              </div>
            </CardFooter>
          </Card>

          <Card className="shadow-md transition-all hover:shadow-lg overflow-hidden border-none">
            {/* <div className="absolute h-full w-1 bg-red-500 left-0"></div> */}
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                Restaurants
              </CardTitle>
              <div className="h-10 w-10 rounded-full bg-red-50 flex items-center justify-center">
                <Utensils className="h-5 w-5 text-red-500" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold text-gray-900">
                {isLoading ? (
                  <Loader2 className="h-8 w-8 animate-spin text-muted" />
                ) : (
                  dashboardData?.restaurants.count
                )}
              </div>
              <div className="flex justify-between items-center mt-2">
                <div className="flex items-center gap-1 text-green-600 text-xs font-medium">
                  <ArrowUpRight className="h-3 w-3" />
                  <span>
                    +{dashboardData?.restaurants.newThisMonth} new this month
                  </span>
                </div>
                <Link href="/admin/food">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 text-xs hover:bg-red-50 hover:text-red-600"
                  >
                    View All
                    <ChevronRight className="h-3 w-3 ml-1 opacity-70" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-md transition-all hover:shadow-lg overflow-hidden border-none">
            {/* <div className="absolute h-full w-1 bg-green-500 left-0"></div> */}
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                Sports Clubs
              </CardTitle>
              <div className="h-10 w-10 rounded-full bg-green-50 flex items-center justify-center">
                <Trophy className="h-5 w-5 text-green-500" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold text-gray-900">
                {isLoading ? (
                  <Loader2 className="h-8 w-8 animate-spin text-muted" />
                ) : (
                  dashboardData?.sportsClubs.count
                )}
              </div>
              <div className="flex justify-between items-center mt-2">
                <div className="flex items-center gap-1 text-green-600 text-xs font-medium">
                  <ArrowUpRight className="h-3 w-3" />
                  <span>
                    +{dashboardData?.sportsClubs.newThisMonth} new this month
                  </span>
                </div>
                <Link href="/admin/sports">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 text-xs hover:bg-green-50 hover:text-green-600"
                  >
                    View All
                    <ChevronRight className="h-3 w-3 ml-1 opacity-70" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>

          <Card className="shadow-md transition-all hover:shadow-lg overflow-hidden border-none">
            {/* <div className="absolute h-full w-1 bg-orange-500 left-0"></div> */}
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium text-gray-700">
                Shops
              </CardTitle>
              <div className="h-10 w-10 rounded-full bg-orange-50 flex items-center justify-center">
                <Store className="h-5 w-5 text-orange-500" />
              </div>
            </CardHeader>
            <CardContent>
              <div className="text-4xl font-bold text-gray-900">
                {isLoading ? (
                  <Loader2 className="h-8 w-8 animate-spin text-muted" />
                ) : (
                  dashboardData?.shops.count
                )}
              </div>
              <div className="flex justify-between items-center mt-2">
                <div className="flex items-center gap-1 text-green-600 text-xs font-medium">
                  <ArrowUpRight className="h-3 w-3" />
                  <span>
                    +{dashboardData?.shops.newThisMonth} new this month
                  </span>
                </div>
                <Link href="/admin/commerce">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 text-xs hover:bg-orange-50 hover:text-orange-600"
                  >
                    View All
                    <ChevronRight className="h-3 w-3 ml-1 opacity-70" />
                  </Button>
                </Link>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Quick Actions */}
      <div className="grid gap-6 md:grid-cols-1">
        <Card className="shadow-md border-none overflow-hidden h-full">
          <CardHeader className="pb-4 border-b">
            <CardTitle className="text-base font-medium text-gray-800">
              Quick Actions
            </CardTitle>
          </CardHeader>
          <CardContent className="p-0">
            <div className="grid grid-cols-1 sm:grid-cols-3 divide-y sm:divide-y-0 sm:divide-x">
              <Link href="/admin/food/onboarding">
                <div className="flex items-center gap-3 p-4 hover:bg-gray-50 cursor-pointer">
                  <div className="flex justify-center items-center h-8 w-8 rounded-md bg-red-100 text-red-500">
                    <Utensils className="h-4 w-4" />
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    Onboard New Restaurant
                  </span>
                </div>
              </Link>
              <Link href="/admin/sports/onboarding">
                <div className="flex items-center gap-3 p-4 hover:bg-gray-50 cursor-pointer">
                  <div className="flex justify-center items-center h-8 w-8 rounded-md bg-green-100 text-green-500">
                    <Trophy className="h-4 w-4" />
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    Onboard New Sports Club
                  </span>
                </div>
              </Link>
              <Link href="/admin/commerce/onboarding">
                <div className="flex items-center gap-3 p-4 hover:bg-gray-50 cursor-pointer">
                  <div className="flex justify-center items-center h-8 w-8 rounded-md bg-orange-100 text-orange-500">
                    <Store className="h-4 w-4" />
                  </div>
                  <span className="text-sm font-medium text-gray-700">
                    Onboard New Shop
                  </span>
                </div>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Management Categories */}
      <div>
        <h2 className="text-xl font-semibold mb-4 text-gray-800">
          Management Categories
        </h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6">
          <Link href="/admin/food">
            <Card className="shadow-md border-none hover:shadow-lg transition-all cursor-pointer overflow-hidden group">
              {/* <div className="absolute top-0 left-0 w-full h-1 bg-red-500"></div> */}
              <CardContent className="p-0">
                <div className="flex items-center p-6">
                  <div className="flex justify-center items-center h-14 w-14 rounded-xl bg-red-100 text-red-500 mr-4 group-hover:bg-red-500 group-hover:text-white transition-all">
                    <Utensils className="h-7 w-7" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-800 text-lg">
                      Restaurant Management
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      View and manage all restaurant listings
                    </p>
                  </div>
                  <div className="ml-auto bg-gray-100 rounded-full p-2 group-hover:bg-red-50 group-hover:text-red-500 transition-all">
                    <ChevronRight className="h-5 w-5 text-muted-foreground group-hover:text-red-500" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/admin/sports">
            <Card className="shadow-md border-none hover:shadow-lg transition-all cursor-pointer overflow-hidden group">
              {/* <div className="absolute top-0 left-0 w-full h-1 bg-green-500"></div> */}
              <CardContent className="p-0">
                <div className="flex items-center p-6">
                  <div className="flex justify-center items-center h-14 w-14 rounded-xl bg-green-100 text-green-500 mr-4 group-hover:bg-green-500 group-hover:text-white transition-all">
                    <Trophy className="h-7 w-7" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-800 text-lg">
                      Sports Club Management
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      View and manage all sports club listings
                    </p>
                  </div>
                  <div className="ml-auto bg-gray-100 rounded-full p-2 group-hover:bg-green-50 group-hover:text-green-500 transition-all">
                    <ChevronRight className="h-5 w-5 text-muted-foreground group-hover:text-green-500" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>

          <Link href="/admin/commerce">
            <Card className="shadow-md border-none hover:shadow-lg transition-all cursor-pointer overflow-hidden group">
              {/* <div className="absolute top-0 left-0 w-full h-1 bg-orange-500"></div> */}
              <CardContent className="p-0">
                <div className="flex items-center p-6">
                  <div className="flex justify-center items-center h-14 w-14 rounded-xl bg-orange-100 text-orange-500 mr-4 group-hover:bg-orange-500 group-hover:text-white transition-all">
                    <Store className="h-7 w-7" />
                  </div>
                  <div>
                    <h3 className="font-medium text-gray-800 text-lg">
                      Shop Management
                    </h3>
                    <p className="text-sm text-muted-foreground">
                      View and manage all shop listings
                    </p>
                  </div>
                  <div className="ml-auto bg-gray-100 rounded-full p-2 group-hover:bg-orange-50 group-hover:text-orange-500 transition-all">
                    <ChevronRight className="h-5 w-5 text-muted-foreground group-hover:text-orange-500" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </Link>
        </div>
      </div>
    </div>
  );
};

export default DashboardPage;
