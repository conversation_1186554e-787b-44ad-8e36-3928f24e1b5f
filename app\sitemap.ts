import type { MetadataRoute } from "next";

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const sitemap_array = [
    {
      url: "https://justcravin.com",
      lastModified: new Date(),
      priority: 1,
    },
    {
      url: "https://justcravin.com/food",
      lastModified: new Date(),
      priority: 1,
    },
    {
      url: "https://justcravin.com/sports",
      lastModified: new Date(),
      priority: 1,
    },
    {
      url: "https://justcravin.com/commerce",
      lastModified: new Date(),
      priority: 1,
    },
  ];

  try {
    const club_response = await fetch(
      process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL +
        `/clubs/landing-page/listing-page-details?pageNumber=${1}&&pageSize=${2000}`
    );
    const club_data = await club_response.json();
    if (club_data && club_data.data) {
      const club_sitemap_data = club_data.data.listingData.map((club: any) => {
        return {
          url: `https://justcravin.com/sports/${club.clubId}`,
          lastModified: new Date(),
          priority: 0.8,
        };
      });
      sitemap_array.push(...club_sitemap_data);
    }
  } catch (error) {
    console.error("Error fetching clubs data:", error);
  }

  try {
    const food_response = await fetch(
      process.env.NEXT_PUBLIC_FOOD_BACKEND_URL +
        `/restaurants/landing-page/listing-page-details?pageNumber=${1}&&pageSize=${2000}`
    );
    const food_data = await food_response.json();
    if (food_data && food_data.data) {
      const food_sitemap_data = food_data.data.listingData.map(
        (restaurant: any) => {
          return {
            url: `https://justcravin.com/food/${restaurant.branchId}`,
            lastModified: new Date(),
            priority: 0.8,
          };
        }
      );
      sitemap_array.push(...food_sitemap_data);
    }
  } catch (error) {
    console.error("Error fetching food data:", error);
  }

  try {
    const commerce_response = await fetch(
      process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL +
        `/shops/landing-page/listing-page-details?pageNumber=${1}&&pageSize=${2000}`
    );
    const commerce_data = await commerce_response.json();
    if (commerce_data && commerce_data.data) {
      const commerce_sitemap_data = commerce_data.data.listingData.map(
        (shop: any) => {
          return {
            url: `https://justcravin.com/commerce/${shop.branchId}`,
            lastModified: new Date(),
            priority: 0.8,
          };
        }
      );
      sitemap_array.push(...commerce_sitemap_data);
    }
  } catch (error) {
    console.error("Error fetching commerce data:", error);
  }

  return sitemap_array;
}
