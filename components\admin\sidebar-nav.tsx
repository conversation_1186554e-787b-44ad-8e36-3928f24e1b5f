"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Home,
  Settings,
  Utensils,
  Store,
  Trophy,
  BarChart4,
  Users,
  Bell,
  FileText,
  type LucideIcon,
  ShieldAlert,
  PanelLeft,
  MessageSquare,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { Badge } from "@/components/ui/badge";

interface NavItem {
  href: string;
  label: string;
  icon: LucideIcon;
  badge?: string;
  section?: string;
}

// Organized navigation specific to restaurant, club and shop management
const navItems: NavItem[] = [
  // Main section
  { href: "/admin", label: "Dashboard", icon: Home, section: "Main" },

  // Listings section
  {
    href: "/admin/sports",
    label: "Sports Clubs",
    icon: Trophy,
    badge: "28",
    section: "Listings",
  },
  {
    href: "/admin/food",
    label: "Restaurants",
    icon: Utensils,
    badge: "42",
    section: "Listings",
  },
  {
    href: "/admin/commerce",
    label: "Shops",
    icon: Store,
    badge: "36",
    section: "Listings",
  },
];

interface SidebarNavProps {
  isMobile?: boolean;
  onLinkClick?: () => void;
}

export default function SidebarNav({
  isMobile = false,
  onLinkClick,
}: SidebarNavProps) {
  const pathname = usePathname();

  // Group items by section
  const sections = navItems.reduce((acc, item) => {
    const section = item.section || "Other";
    if (!acc[section]) {
      acc[section] = [];
    }
    acc[section].push(item);
    return acc;
  }, {} as Record<string, NavItem[]>);

  // Define section icon colors for visual hierarchy
  const sectionColors: Record<string, string> = {
    Main: "bg-blue-500",
    Listings: "bg-emerald-500",
    Management: "bg-amber-500",
    System: "bg-purple-500",
    Other: "bg-gray-500",
  };

  return (
    <nav className="flex flex-col gap-6">
      {Object.entries(sections).map(([sectionName, items]) => (
        <div key={sectionName} className="space-y-1">
          <div className="flex items-center px-3 mb-2">
            <div
              className={`h-1.5 w-1.5 rounded-full ${sectionColors[sectionName]} mr-2`}
            ></div>
            <div className="text-xs font-semibold text-muted-foreground uppercase tracking-wider">
              {sectionName}
            </div>
          </div>

          {items.map((item) => {
            const isActive = pathname === item.href;
            return (
              <Link
                key={item.href}
                href={item.href}
                onClick={onLinkClick}
                className={cn(
                  "flex items-center gap-3 px-3 py-2.5 rounded-md text-sm transition-all",
                  isActive
                    ? "bg-primary/10 text-primary font-medium shadow-sm dark:bg-primary/20"
                    : "text-muted-foreground hover:bg-muted hover:text-foreground",
                  isMobile && "py-3"
                )}
              >
                <div className="flex items-center justify-center">
                  <item.icon
                    className={cn(
                      "h-5 w-5 flex-shrink-0",
                      isActive && "text-primary"
                    )}
                  />
                </div>
                <span className="flex-1">{item.label}</span>
              </Link>
            );
          })}
        </div>
      ))}
    </nav>
  );
}
