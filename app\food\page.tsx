import { HydrationBoundary, dehydrate } from "@tanstack/react-query";
import { queryClient } from "@/lib/query-client";
import { Suspense } from "react";
import FoodParent from "./(food-files)/food-parent";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: "Cravin Food | Browse Through All Our Restaurants",
  description: "Showcase Of All Restaurants Available On Cravin Food",
  icons: {
    icon: "/Cravin_logo_favi.png",
  },
  openGraph: {
    title: "Cravin Food | Browse Through All Our Restaurants",
    description: "Showcase Of All Restaurants Available On Cravin Food",
    url: "https://justcravin.com/food",
    type: "website",
  },
  alternates: {
    canonical: "https://justcravin.com",
  },
};

export default async function Page({
  searchParams,
}: {
  searchParams: { [key: string]: string | string[] | undefined };
}) {
  //trying out query params on server side
  const queryEmirate = searchParams.emirate as string | undefined;
  const querySearch = searchParams.search as string | undefined;
  const queryPage = (searchParams.page as string) || "1";

  //prefetch will be here, this thing was causing caching issues so i made it no store
  await queryClient.prefetchQuery({
    queryKey: ["getFoodListings", queryPage, queryEmirate, querySearch],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_FOOD_BACKEND_URL +
          `/restaurants/landing-page/listing-page-details?pageNumber=${queryPage}${
            queryEmirate && queryEmirate !== "All"
              ? `&emirate=${queryEmirate}`
              : ""
          }${querySearch ? `&search=${querySearch}` : ""}`,
        { cache: "no-store" }
      );
      const data = await response.json();
      return data.data;
    },
  });

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <Suspense
        fallback={
          <div className="h-[80vh] w-full flex items-center justify-center">
            <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-black"></div>
          </div>
        }
      >
        <FoodParent />
      </Suspense>
    </HydrationBoundary>
  );
}
