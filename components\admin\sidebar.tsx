"use client";

import Link from "next/link";
import SidebarNav from "./sidebar-nav";
import { cn } from "@/lib/utils";
import { Utensils } from "lucide-react";
import { signOut } from "next-auth/react";
import { Button } from "../ui/button";

export default function Sidebar() {
  const handleLogout = async () => {
    await signOut({ callbackUrl: "/auth/login" }); // Corrected callback URL
  };
  
  return (
    <aside className="hidden md:flex md:flex-col w-72 flex-shrink-0 bg-white dark:bg-zinc-950 border-r shadow-sm h-full">
      {/* Brand header */}
      <div className="flex items-center h-16 px-6 border-b bg-gradient-to-r from-slate-50 to-white dark:from-zinc-900 dark:to-zinc-950">
        <Link
          href="/admin"
          className="flex items-center gap-2.5 text-lg font-bold tracking-tight text-foreground"
        >
          <div className="flex items-center justify-center w-9 h-9 rounded-md bg-primary text-primary-foreground shadow-sm">
            <Utensils className="h-5 w-5" />
          </div>
          <span className="font-semibold">Cravin Admin</span>
        </Link>
      </div>

      {/* Main navigation */}
      <div className="flex-1 overflow-y-auto px-3 py-4">
        <SidebarNav />
      </div>
      
      {/* User actions section */}
      <div className="p-4 border-t bg-muted/30">
        <Button 
          onClick={handleLogout} 
          variant="outline" 
          className="w-full justify-start gap-2 text-muted-foreground hover:text-destructive hover:border-destructive/50 transition-colors"
        >
          <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round" className="lucide lucide-log-out">
            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
            <polyline points="16 17 21 12 16 7"></polyline>
            <line x1="21" y1="12" x2="9" y2="12"></line>
          </svg>
          Sign Out
        </Button>
        
        <div className="mt-4 pt-4 border-t text-xs text-center text-muted-foreground">
          <p>© {new Date().getFullYear()} Cravin Technologies</p>
        </div>
      </div>
    </aside>
  );
}
