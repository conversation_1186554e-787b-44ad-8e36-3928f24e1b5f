import { QueryClient } from "@tanstack/react-query";

export const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      // With SSR, we usually want to set some default staleTime
      // above 0 to avoid refetching immediately on the client
      staleTime: 1,
    },
  },
});
// import { QueryClient } from "@tanstack/query-core";
// import { cache } from "react";

// const getQueryClient = cache(() => new QueryClient());
// export default getQueryClient;
