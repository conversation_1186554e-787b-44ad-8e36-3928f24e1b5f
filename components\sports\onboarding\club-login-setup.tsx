"use client";

import { useState, useEffect, use<PERSON>allback } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { toast } from "sonner";
import {
  Loader2,
  ShieldCheck,
  Eye,
  EyeOff,
  ArrowLeft,
  Copy,
  Zap,
  Check,
  X,
  ClipboardCheck, // Import ClipboardCheck icon
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { cn } from "@/lib/utils";
import { debounce } from "lodash";

interface PasswordRequirement {
  label: string;
  regex: RegExp;
}

const passwordRequirements: PasswordRequirement[] = [
  { label: "At least 8 characters", regex: /.{8,}/ },
  { label: "Contains uppercase letter", regex: /[A-Z]/ },
  { label: "Contains lowercase letter", regex: /[a-z]/ },
  { label: "Contains number", regex: /[0-9]/ },
  { label: "Contains special character", regex: /[^A-Za-z0-9]/ },
];

interface PasswordStrengthIndicatorProps {
  password: string;
}

const PasswordStrengthIndicator = ({
  password,
}: PasswordStrengthIndicatorProps) => {
  const requirements = passwordRequirements.map((req) => ({
    ...req,
    isMet: req.regex.test(password),
  }));

  const strengthPercentage =
    (requirements.filter((req) => req.isMet).length / requirements.length) *
    100;

  return (
    <div className="space-y-3">
      <div className="flex items-center justify-between text-sm mb-1">
        <span className="font-medium">Password Strength</span>
        <span
          className={cn(
            strengthPercentage === 100
              ? "text-green-500"
              : strengthPercentage >= 60
              ? "text-yellow-500"
              : "text-red-500"
          )}
        >
          {strengthPercentage === 100
            ? "Strong"
            : strengthPercentage >= 60
            ? "Medium"
            : "Weak"}
        </span>
      </div>
      <div className="h-1.5 w-full bg-muted rounded-full overflow-hidden">
        <div
          className={cn(
            "h-full transition-all duration-300",
            strengthPercentage === 100
              ? "bg-green-500"
              : strengthPercentage >= 60
              ? "bg-yellow-500"
              : "bg-red-500"
          )}
          style={{ width: `${strengthPercentage}%` }}
        />
      </div>
      <div className="grid grid-cols-2 gap-2 mt-2">
        {requirements.map((req, index) => (
          <div key={index} className="flex items-center gap-2">
            {req.isMet ? (
              <Check className="h-4 w-4 text-green-500 flex-shrink-0" />
            ) : (
              <X className="h-4 w-4 text-red-500 flex-shrink-0" />
            )}
            <span
              className={cn(
                "text-xs",
                req.isMet ? "text-muted-foreground" : "text-foreground"
              )}
            >
              {req.label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

const loginFormSchema = z.object({
  ownerUsername: z.string().min(1, "Username is required"),
  ownerPassword: z
    .string()
    .min(8, "Password must be at least 8 characters")
    .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
    .regex(/[a-z]/, "Password must contain at least one lowercase letter")
    .regex(/[0-9]/, "Password must contain at least one number")
    .regex(
      /[^A-Za-z0-9]/,
      "Password must contain at least one special character"
    )
    .optional()
    .or(z.literal("")),
});

type LoginFormValues = {
  ownerUsername: string;
  ownerPassword: string;
};

interface ClubLoginSetupProps {
  clubId: string;
  onBack: () => void;
  isCompleted?: boolean;
}

const formatUserData = (
  data: { username: string; user_type: string; club_ids: string[] }[]
) => {
  // Return all available logins without filtering by user_type
  return data.map((user) => user.username) || [];
};

export default function ClubLoginSetup({
  clubId,
  onBack,
  isCompleted = false,
}: ClubLoginSetupProps) {
  const [isSubmitting, setIsSubmitting] = useState<{ [key: string]: boolean }>(
    {}
  );
  const [showOwnerPassword, setShowOwnerPassword] = useState(false);
  const [showManagerPassword, setShowManagerPassword] = useState(false);
  const [ownerUsernameError, setOwnerUsernameError] = useState<string | null>(
    null
  );
  const [managerUsernameError, setManagerUsernameError] = useState<
    string | null
  >(null);
  const [existingAccounts, setExistingAccounts] = useState<string[]>([]);
  const [selectedAccount, setSelectedAccount] = useState<string | "new">("");
  const [isCreatingNew, setIsCreatingNew] = useState(false);
  const [isCopied, setIsCopied] = useState(false); // State for copy status

  const router = useRouter();

  const { data: existingLogins, refetch } = useQuery({
    queryKey: ["clubLogins", clubId],
    queryFn: async () => {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/auth/club/${clubId}/logins`,
        {
          method: "GET",
          headers: {
            accept: "*/*",
          },
        }
      );
      if (!response.ok) {
        throw new Error("Failed to fetch login information");
      }
      return response.json();
    },
  });

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginFormSchema) as any,
    defaultValues: {
      ownerUsername: "",
      ownerPassword: "",
      // managerUsername: "",
      // managerPassword: "",
    },
  });
  useEffect(() => {
    if (existingLogins?.data?.data) {
      const accounts = formatUserData(existingLogins.data.data);
      setExistingAccounts(accounts);

      // If there's an existing account, select the first one by default
      if (accounts.length > 0) {
        setSelectedAccount(accounts[0]);
        form.setValue("ownerUsername", accounts[0]);
      } else {
        // If no accounts, set to create new mode
        setIsCreatingNew(true);
      }
    }
  }, [existingLogins, form]);
  const togglePasswordVisibility = (userType: "owner" | "manager") => {
    if (userType === "owner") {
      setShowOwnerPassword(!showOwnerPassword);
    } else {
      setShowManagerPassword(!showManagerPassword);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        toast.success("Username copied!"); // Updated toast message
        setIsCopied(true);
        setTimeout(() => setIsCopied(false), 2000); // Reset icon after 2 seconds
      })
      .catch(() => {
        toast.error("Failed to copy username");
      });
  };

  const checkUsername = async (
    username: string,
    userType: "owner" | "manager"
  ) => {
    if(!username) return
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/auth/auth/check-username/${username}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to check username");
      }

      const data = await response.json();
      if (data?.data) {
        if (userType === "owner") {
          setOwnerUsernameError("Username already exists");
        } else {
          setManagerUsernameError("Username already exists");
        }
        return false;
      }
      if (userType === "owner") {
        setOwnerUsernameError(null);
      } else {
        setManagerUsernameError(null);
      }
      return true;
    } catch (error) {
      console.error("Error checking username:", error);
      if (userType === "owner") {
        setOwnerUsernameError("Failed to check username availability");
      } else {
        setManagerUsernameError("Failed to check username availability");
      }
      return false;
    }
  };

  const debouncedCheckUsername = useCallback(
    debounce(
      (username: string, userType: "owner" | "manager") =>
        checkUsername(username, userType),
      500
    ),
    []
  );

  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      // Only check username if creating new
      if (
        isCreatingNew &&
        name === "ownerUsername" &&
        value.ownerUsername
      ) {
        debouncedCheckUsername(value.ownerUsername, "owner");
      }
      //  else if (name === "managerUsername" && value.managerUsername) {
      //   debouncedCheckUsername(value.managerUsername, "manager");
      // }
    });

    return () => subscription.unsubscribe();
  }, [form.watch, debouncedCheckUsername, isCreatingNew]);

  const updatePassword = async (
    userType: "owner",
    newPassword: string | undefined
  ) => {
    if (!newPassword) {
      toast.error("Please enter a new password");
      return;
    }

    setIsSubmitting((prev) => ({ ...prev, [userType]: true }));
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/auth/merchant/update-password`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            club_id: clubId,
            newPassword,
            username: form.getValues(`${userType}Username`),
          }),
        }
      );

      if (!response.ok) {
        const error = await response.json();
        toast.error(error.message || "Failed to update password");
        throw new Error(`Failed to update ${userType} password`);
      }

      toast.success("Password updated successfully!");
      form.setValue(`${userType}Password`, "");
    } catch (error) {
      console.error("Error:", error);
    } finally {
      setIsSubmitting((prev) => ({ ...prev, [userType]: false }));
    }
  };

  const onSubmit = async (data: LoginFormValues) => {
    setIsSubmitting((prev) => ({ ...prev, all: true }));
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/auth/merchant/create-merchant-accounts`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            club_id: clubId,
            ownerUsername: data.ownerUsername,
            ownerPassword: data.ownerPassword,
            // managerUsername: data.managerUsername,
            // managerPassword: data.managerPassword,
          }),
        }
      );

      if (!response.ok) throw new Error("Failed to create login credentials");
      refetch()
      toast.success("Login credentials created successfully!");
    } catch (error) {
      console.error("Error:", error);
      toast.error("Failed to create login credentials");
    } finally {
      setIsSubmitting((prev) => ({ ...prev, all: false }));
    }
  };

  // Function to handle account selection
  const handleAccountSelection = (username: string | "new") => {
    if (username === "new") {
      form.reset({
        ownerUsername: "",
        ownerPassword: "",
      });
      form.setValue("ownerUsername", "");
      form.setValue("ownerPassword", "");
      setIsCreatingNew(true);

      // Clear any existing username errors when switching to create new mode
      setOwnerUsernameError(null);
    } else {
      setIsCreatingNew(false);
      setSelectedAccount(username);
      form.setValue("ownerUsername", username);
      form.setValue("ownerPassword", "");
      // Clear any existing username errors when selecting an existing account
      setOwnerUsernameError(null);
    }
  };

  return (
    <Card className="w-full max-w-lg mx-auto bg-white border border-gray-200 rounded-2xl shadow-sm">
      <div className="flex items-center gap-2 border-b border-gray-100 px-6 py-4">
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full hover:bg-gray-100"
          onClick={onBack}
        >
          <ArrowLeft className="h-5 w-5" />
        </Button>
        <ShieldCheck className="h-6 w-6 text-primary" />
        <h2 className="text-lg font-semibold tracking-tight">
          {isCreatingNew ? "Create Club Login" : "Manage Club Login"}
        </h2>
      </div>

      <div className="px-6 py-5 space-y-5">
        <div className="flex items-start gap-3 bg-blue-50 border border-blue-100 rounded-lg px-4 py-3">
          <Zap className="h-5 w-5 text-blue-600 mt-0.5 flex-shrink-0" />
          <p className="text-blue-700 text-sm">
            {isCreatingNew
              ? "Create secure login credentials for your club. Use a strong password and keep it safe."
              : "Update your password securely. Passwords must meet the strength requirements below."}
          </p>
        </div>

        {existingAccounts.length > 0 && (
          <div>
            <div className="mb-2 text-sm font-medium text-gray-700">
              Choose an account or create a new one
            </div>
            <div className="flex flex-wrap gap-2">
              {existingAccounts.map((account) => (
                <Button
                  key={account}
                  variant={selectedAccount === account && !isCreatingNew ? "default" : "outline"}
                  className={cn(
                    "rounded-lg px-3 py-2 text-sm font-mono",
                    selectedAccount === account && !isCreatingNew
                      ? "bg-primary text-white border-primary"
                      : "bg-white border-gray-200 hover:bg-gray-50"
                  )}
                  onClick={() => handleAccountSelection(account)}
                >
                  {account}
                </Button>
              ))}
              <Button
                variant={isCreatingNew ? "default" : "outline"}
                className={cn(
                  "rounded-lg px-3 py-2 text-sm flex items-center gap-1",
                  isCreatingNew
                    ? "bg-primary text-white border-primary ring-2 ring-primary ring-offset-1" // Highlight when selected
                    : "bg-white border-gray-300 hover:bg-gray-50 text-primary border-dashed" // Differentiate when not selected
                )}
                onClick={() => handleAccountSelection("new")}
              >
                <Zap className="h-4 w-4" />
                New Login
              </Button>
            </div>
          </div>
        )}

        <Form {...form}>
          <form
            onSubmit={isCreatingNew ? form.handleSubmit(onSubmit) : undefined}
            className="space-y-5"
          >
            <div className="space-y-4">
              {/* Username */}
              <div>
                <FormLabel className="block text-sm font-medium mb-1">
                  Username
                  {isCreatingNew && <span className="ml-1 text-xs text-rose-500">*</span>}
                </FormLabel>
                <div className={cn(
                  "flex items-center rounded-lg border px-3 py-2 bg-gray-50",
                  ownerUsernameError && isCreatingNew ? "border-rose-300" : "border-gray-200"
                )}>
                  <div className="flex-1">
                    {!isCreatingNew ? (
                      <span className="text-base font-mono">{selectedAccount}</span>
                    ) : (
                      <FormField
                        control={form.control}
                        name="ownerUsername"
                        render={({ field }) => (
                          <FormItem>
                            <FormControl>
                              <Input
                                {...field}
                                type="text"
                                placeholder="Enter username"
                                className={cn(
                                  "h-9 text-base bg-transparent border-0 shadow-none focus-visible:ring-0",
                                  ownerUsernameError && "text-destructive"
                                )}
                              />
                            </FormControl>
                            {ownerUsernameError && (
                              <FormMessage className="text-xs flex items-center mt-1">
                                <X className="h-3.5 w-3.5 mr-1" />
                                {ownerUsernameError}
                              </FormMessage>
                            )}
                          </FormItem>
                        )}
                      />
                    )}
                  </div>
                  {!isCreatingNew && (
                    <Button
                      type="button"
                      variant="ghost"
                      size="icon"
                      onClick={() => copyToClipboard(selectedAccount as string)}
                      className="ml-2 text-muted-foreground hover:text-foreground"
                      disabled={isCopied} // Disable button briefly after copy
                    >
                      {isCopied ? (
                        <ClipboardCheck className="h-4 w-4 text-green-500" /> // Show check icon when copied
                      ) : (
                        <Copy className="h-4 w-4" /> // Show copy icon
                      )}
                    </Button>
                  )}
                </div>
              </div>

              {/* Password */}
              <div>
                <div className="flex items-center justify-between mb-1">
                  <FormLabel className="text-sm font-medium">
                    Password
                    {isCreatingNew && <span className="ml-1 text-xs text-rose-500">*</span>}
                  </FormLabel>
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => togglePasswordVisibility("owner")}
                    className="h-8 text-xs text-muted-foreground hover:text-foreground"
                  >
                    {showOwnerPassword ? (
                      <EyeOff className="h-4 w-4 mr-1" />
                    ) : (
                      <Eye className="h-4 w-4 mr-1" />
                    )}
                    {showOwnerPassword ? "Hide" : "Show"}
                  </Button>
                </div>
                <div className="flex gap-2">
                  <FormField
                    control={form.control}
                    name="ownerPassword"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input
                            type={showOwnerPassword ? "text" : "password"}
                            placeholder={isCreatingNew ? "Create password" : "Enter new password"}
                            className="h-9"
                            {...field}
                          />
                        </FormControl>
                        <FormMessage className="text-xs" />
                      </FormItem>
                    )}
                  />
                  {!isCreatingNew && (
                    <Button
                      type="button"
                      variant="default"
                      disabled={isSubmitting.owner}
                      onClick={() =>
                        updatePassword(
                          "owner",
                          form.getValues("ownerPassword")
                        )
                      }
                      className="h-9 px-4"
                    >
                      {isSubmitting.owner ? (
                        <>
                          <Loader2 className="mr-1 h-4 w-4 animate-spin" />
                          Updating
                        </>
                      ) : (
                        <>
                          <Zap className="mr-1 h-4 w-4" />
                          Update
                        </>
                      )}
                    </Button>
                  )}
                </div>
                <div className="mt-3">
                  <PasswordStrengthIndicator password={form.watch("ownerPassword") || ""} />
                </div>
              </div>
            </div>

            {isCreatingNew && (
              <Button
                type="submit"
                className="w-full h-11 text-base font-semibold rounded-lg mt-2"
                disabled={isSubmitting.all || ownerUsernameError !== null}
              >
                {isSubmitting.all ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Login...
                  </>
                ) : (
                  <>
                    <ShieldCheck className="mr-2 h-4 w-4" />
                    Create Secure Login
                  </>
                )}
              </Button>
            )}
          </form>
        </Form>
      </div>
    </Card>
  );
}
