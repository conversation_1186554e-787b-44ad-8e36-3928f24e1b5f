"use client";

import { useState, useEffect, useCallback } from "react";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Card } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { toast } from "sonner";
import {
  Loader2,
  ShieldCheck,
  Building2,
  Eye,
  EyeOff,
  ArrowLeft,
  Copy,
  Zap,
  Check,
  X,
  Users,
  PlusCircle,
  Trash2,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { cn } from "@/lib/utils";
import { debounce } from "lodash";

interface Branch {
  branch_id: string;
  branch_name: string;
}

interface PasswordRequirement {
  label: string;
  regex: RegExp;
}

const passwordRequirements: PasswordRequirement[] = [
  { label: "At least 8 characters", regex: /.{8,}/ },
  { label: "Contains uppercase letter", regex: /[A-Z]/ },
  { label: "Contains lowercase letter", regex: /[a-z]/ },
  { label: "Contains number", regex: /[0-9]/ },
  { label: "Contains special character", regex: /[^A-Za-z0-9]/ },
];

interface PasswordStrengthIndicatorProps {
  password: string;
}

const PasswordStrengthIndicator = ({
  password,
}: PasswordStrengthIndicatorProps) => {
  const requirements = passwordRequirements.map((req) => ({
    ...req,
    isMet: req.regex.test(password),
  }));

  const strengthPercentage =
    (requirements.filter((req) => req.isMet).length / requirements.length) *
    100;

  return (
    <div className="space-y-2 text-sm">
      <div className="h-1.5 w-full bg-muted rounded-full overflow-hidden">
        <div
          className={cn(
            "h-full transition-all duration-300",
            strengthPercentage === 100
              ? "bg-green-500"
              : strengthPercentage >= 60
              ? "bg-yellow-500"
              : "bg-red-500"
          )}
          style={{ width: `${strengthPercentage}%` }}
        />
      </div>
      <div className="space-y-1">
        {requirements.map((req, index) => (
          <div key={index} className="flex items-center gap-2">
            {req.isMet ? (
              <Check className="h-4 w-4 text-green-500" />
            ) : (
              <X className="h-4 w-4 text-red-500" />
            )}
            <span
              className={cn(
                "text-sm",
                req.isMet ? "text-muted-foreground" : "text-foreground"
              )}
            >
              {req.label}
            </span>
          </div>
        ))}
      </div>
    </div>
  );
};

const passwordSchema = z
  .string()
  .min(8, "Password must be at least 8 characters")
  .regex(/[A-Z]/, "Password must contain at least one uppercase letter")
  .regex(/[a-z]/, "Password must contain at least one lowercase letter")
  .regex(/[0-9]/, "Password must contain at least one number")
  .regex(/[^A-Za-z0-9]/, "Password must contain at least one special character")
  .optional()
  .or(z.literal(""));

const loginFormSchema = z.object({
  ownerUsername: z.string().min(1, "Username is required"),
  ownerPassword: passwordSchema,
  branchPasswords: z.array(
    z.object({
      branch_id: z.string(),
      branch_name: z.string(),
      password: passwordSchema,
    })
  ),
  staffLogins: z.array(
    z.object({
      username: z.string().min(1, "Username is required"),
      password: passwordSchema,
      branch_id: z.string().min(1, "Branch is required"),
    })
  ),
});

type LoginFormValues = {
  ownerUsername: string;
  ownerPassword: string;
  branchPasswords: Array<{
    managerUsername: string;
    branch_id: string;
    branch_name: string;
    password: string;
  }>;
  staffLogins: Array<{
    username: string;
    password: string;
    branch_id: string;
    id?: string;
  }>;
};

interface RestaurantLoginSetupProps {
  restaurantId: string;
  branches: Branch[];
  onBack: () => void;
  isCompleted?: boolean;
}

const formatUserData = (
  data: {
    user_type: string;
    username: string;
    branch_ids: string[];
    id?: string;
  }[]
) => {
  let response: {
    owner: string;
    managers: { [key: string]: string };
    staff: Array<{ id: string; username: string; branch_id: string }>;
  } = {
    owner: "",
    managers: {},
    staff: [],
  };

  data.forEach(
    (user: {
      user_type: string;
      username: string;
      branch_ids: string[];
      id?: string;
    }) => {
      if (user.user_type === "owner") {
        response.owner = user.username;
      } else if (user.user_type === "manager") {
        user.branch_ids.forEach((branch: string) => {
          response.managers[branch] = user.username;
        });
      } else if (user.user_type === "counter-staff") {
        // For staff, we assume they're assigned to the first branch in their branch_ids array
        // If no branch is assigned, we'll handle it in the UI
        const branch_id =
          user.branch_ids && user.branch_ids.length > 0
            ? user.branch_ids[0]
            : "";

        response.staff.push({
          id: user.id || `staff-${response.staff.length}`,
          username: user.username,
          branch_id: branch_id,
        });
      }
    }
  );

  return response;
};

export default function RestaurantLoginSetup({
  restaurantId,
  branches,
  onBack,
  isCompleted = false,
}: RestaurantLoginSetupProps) {
  const [isSubmitting, setIsSubmitting] = useState<{ [key: string]: boolean }>(
    {}
  );
  const [showOwnerPassword, setShowOwnerPassword] = useState(false);
  const [showBranchPasswords, setShowBranchPasswords] = useState<{
    [key: string]: boolean;
  }>({});
  const [showStaffPasswords, setShowStaffPasswords] = useState<{
    [key: string]: boolean;
  }>({});
  const [usernameError, setUsernameError] = useState<string | null>(null);
  const [branchUsernameErrors, setBranchUsernameErrors] = useState<{
    [key: string]: string | null;
  }>({});
  const [staffUsernameErrors, setStaffUsernameErrors] = useState<{
    [key: string]: string | null;
  }>({});
  const [validatedStaffUsernames, setValidatedStaffUsernames] = useState<{
    [key: string]: boolean;
  }>({});
  const [checkingUsernames, setCheckingUsernames] = useState<{
    [key: string]: boolean;
  }>({});
  // logins fetced from backend boolean
  const [isLoginsFetched, setIsLoginsFetched] = useState(false);
  const router = useRouter();
  const [existingLoginBranchIds, setExistingLoginBranchIds] = useState<{
    owner: string;
    managers: { [key: string]: string };
    staff: Array<{ id: string; username: string; branch_id: string }>;
  }>({ owner: "", managers: {}, staff: [] });

  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginFormSchema) as any,
    defaultValues: {
      ownerUsername: "",
      ownerPassword: "",
      branchPasswords: branches.map((branch) => ({
        branch_id: branch.branch_id,
        branch_name: branch.branch_name,
        password: "",
        managerUsername: "",
      })),
      staffLogins: [],
    },
  });

  const { data: existingLogins, refetch } = useQuery({
    queryKey: ["restaurantLogins", restaurantId],
    queryFn: async () => {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FOOD_BACKEND_URL}/auth/restaurant/${restaurantId}/logins`,
        {
          method: "GET",
          headers: {
            accept: "*/*",
          },
        }
      );
      if (!response.ok) {
        throw new Error("Failed to fetch login information");
      }
      return response.json();
    },
    enabled: !!restaurantId,
    refetchInterval: 0,
    refetchOnWindowFocus: false,
    refetchOnReconnect: false,
    refetchOnMount: false,
    refetchIntervalInBackground: false,
  });

  const fetchLoginData = useCallback(async () => {
    if (!existingLogins?.data) {
      console.log("No existing login data available");
      return;
    }

    const loginBranchIds = await formatUserData(existingLogins.data?.data);
    if (!loginBranchIds || !loginBranchIds.managers) {
      console.log("Invalid login branch IDs format");
      return;
    }

    setExistingLoginBranchIds(loginBranchIds);
    setIsLoginsFetched(true);
    // Update form values with existing usernames
    if (loginBranchIds.owner) {
      form.setValue("ownerUsername", loginBranchIds.owner);
    }

    if (branches) {
      branches.forEach((branch, index) => {
        if (
          branch &&
          branch.branch_id &&
          loginBranchIds.managers[branch.branch_id]
        ) {
          const managerUsername = loginBranchIds.managers[branch.branch_id];
          if (managerUsername) {
            form.setValue(
              `branchPasswords.${index}.managerUsername`,
              managerUsername
            );
          }
        }
      });
    }

    // Update staff logins
    if (loginBranchIds.staff && loginBranchIds.staff.length > 0) {
      form.setValue(
        "staffLogins",
        loginBranchIds.staff.map((staff) => ({
          username: staff.username,
          password: "",
          branch_id: staff.branch_id,
          id: staff.id,
        }))
      );
    }
  }, [existingLogins, form, branches]);

  useEffect(() => {
    if (existingLogins && !isLoginsFetched) {
      fetchLoginData();
    }
  }, [existingLogins, fetchLoginData]);

  useEffect(() => {
    if (existingLogins) {
      form.reset({
        ownerPassword: "",
        branchPasswords: branches.map((branch) => ({
          branch_id: branch.branch_id,
          branch_name: branch.branch_name,
          password: "",
        })),
        staffLogins: existingLoginBranchIds.staff.map((staff) => ({
          username: staff.username,
          password: "",
          branch_id: staff.branch_id,
          id: staff.id,
        })),
      });
    }
  }, [existingLogins, form, existingLoginBranchIds.staff]);

  const togglePasswordVisibility = (field: string, id?: string) => {
    if (field === "branch" && id) {
      setShowBranchPasswords((prev) => ({
        ...prev,
        [id]: !prev[id],
      }));
    } else if (field === "staff" && id) {
      setShowStaffPasswords((prev) => ({
        ...prev,
        [id]: !prev[id],
      }));
    } else {
      setShowOwnerPassword(!showOwnerPassword);
    }
  };

  const copyToClipboard = (text: string) => {
    navigator.clipboard
      .writeText(text)
      .then(() => {
        toast.success("Username copied to clipboard!");
      })
      .catch(() => {
        toast.error("Failed to copy username");
      });
  };

  const updateOwnerPassword = async (newPassword: string | undefined) => {
    if (!newPassword) {
      toast.error("Please enter a new password");
      return;
    }

    setIsSubmitting((prev) => ({ ...prev, owner: true }));
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FOOD_BACKEND_URL}/auth/merchant/update-password`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            restaurant_id: restaurantId,
            password: newPassword,
            ownerUsername: form.getValues("ownerUsername"),
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update owner password");
      }

      toast.success("Owner password updated successfully!");
      form.setValue("ownerPassword", "");
      refetch();
    } catch (error) {
      console.error("Error:", error);
      toast.error("Failed to update owner password.");
    } finally {
      setIsSubmitting((prev) => ({ ...prev, owner: false }));
    }
  };

  const updateBranchPassword = async (
    branchId: string,
    newPassword: string | undefined,
    branchIndex: number,
    newBranch = false
  ) => {
    if (!newPassword) {
      toast.error("Please enter a new password");
      return;
    }
    // check if existingLoginBranchIds is available
    const managerUsername = !newBranch
      ? existingLoginBranchIds.managers[branchId]
      : form.getValues(`branchPasswords.${branchIndex}.managerUsername`);
    if (!managerUsername) {
      toast.error("Manager username not found");
      return;
    }

    setIsSubmitting((prev) => ({ ...prev, [branchId]: true }));
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FOOD_BACKEND_URL}/auth/merchant/update-password`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            managerUsername: managerUsername,
            restaurant_id: restaurantId,
            branch_id: branchId,
            password: newPassword,
            newBranch,
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update branch password");
      }
      refetch();
      toast.success("Branch password updated successfully!");
      form.setValue(`branchPasswords.${branchIndex}.password`, "");
    } catch (error) {
      console.error("Error:", error);
      toast.error("Failed to update branch password.");
    } finally {
      setIsSubmitting((prev) => ({ ...prev, [branchId]: false }));
    }
  };

  const updateStaffPassword = async (
    staffId: string,
    newPassword: string | undefined,
    staffIndex: number,
    newStaff = false
  ) => {
    if (!newPassword) {
      toast.error("Please enter a password");
      return;
    }

    const staffUsername = !newStaff
      ? existingLoginBranchIds.staff.find((s) => s.id === staffId)?.username
      : form.getValues(`staffLogins.${staffIndex}.username`);

    if (!staffUsername) {
      toast.error("Staff username not found");
      return;
    }

    const branchId = form.getValues(`staffLogins.${staffIndex}.branch_id`);
    if (!branchId) {
      toast.error("Please select a branch for this staff member");
      return;
    }

    setIsSubmitting((prev) => ({ ...prev, [staffId]: true }));
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FOOD_BACKEND_URL}/auth/merchant/update-password`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            staffUsername: staffUsername,
            restaurant_id: restaurantId,
            branch_id: branchId,
            password: newPassword,
            newStaff,
            user_type: "counter-staff",
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to update staff password");
      }
      refetch();
      toast.success("Staff password updated successfully!");
      form.setValue(`staffLogins.${staffIndex}.password`, "");
    } catch (error) {
      console.error("Error:", error);
      toast.error("Failed to update staff password.");
    } finally {
      setIsSubmitting((prev) => ({ ...prev, [staffId]: false }));
    }
  };

  const onSubmit = async (data: LoginFormValues) => {
    setIsSubmitting((prev) => ({ ...prev, all: true }));
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FOOD_BACKEND_URL}/auth/merchant/create-merchant-accounts`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            restaurant_id: restaurantId,
            ownerPassword: data.ownerPassword,
            branchPasswords: data.branchPasswords.map((branch) => ({
              ...branch,
              managerUsername: form.getValues(
                `branchPasswords.${branches.findIndex(
                  (b) => b.branch_id === branch.branch_id
                )}.managerUsername`
              ),
            })),
            ownerUsername: form.getValues("ownerUsername"),
            staffLogins: data.staffLogins
              .filter((staff) => !staff.id)
              .map((staff) => ({
                username: staff.username,
                password: staff.password,
                branch_id: staff.branch_id,
                user_type: "counter-staff",
              })),
          }),
        }
      );

      if (!response.ok) {
        throw new Error("Failed to create login credentials");
      }

      toast.success("Login credentials created successfully!");
    } catch (error) {
      console.error("Error:", error);
      toast.error("Failed to create login credentials");
    } finally {
      setIsSubmitting((prev) => ({ ...prev, all: false }));
    }
  };

  const checkUsername = async (
    username: string,
    id?: string,
    type: "owner" | "branch" | "staff" = "owner"
  ) => {
    try {
      // dont check for eixsting logins ,it can be any login ,owner ,manager ,staff also
      if (!username) return;

      // Set checking state to true
      if (type === "staff" && id) {
        setCheckingUsernames((prev) => ({
          ...prev,
          [id]: true,
        }));
      } else if (type === "branch" && id) {
        setCheckingUsernames((prev) => ({
          ...prev,
          [id]: true,
        }));
      } else {
        setCheckingUsernames((prev) => ({
          ...prev,
          owner: true,
        }));
      }

      if (existingLoginBranchIds.owner === username) {
        setUsernameError("Username already exists");
        if (type === "staff" && id) {
          setCheckingUsernames((prev) => ({ ...prev, [id]: false }));
        }
        return false;
      }
      if (
        existingLoginBranchIds.managers &&
        Object.values(existingLoginBranchIds.managers).includes(username)
      ) {
        setBranchUsernameErrors((prev) => ({
          ...prev,
          [id || ""]: "Username already exists",
        }));
        if (type === "staff" && id) {
          setCheckingUsernames((prev) => ({ ...prev, [id]: false }));
        }
        return false;
      }
      if (
        existingLoginBranchIds.staff &&
        existingLoginBranchIds.staff.some((s) => s.username === username)
      ) {
        setStaffUsernameErrors((prev) => ({
          ...prev,
          [id || ""]: "Username already exists",
        }));
        if (type === "staff" && id) {
          setCheckingUsernames((prev) => ({ ...prev, [id]: false }));
        }
        return false;
      }
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FOOD_BACKEND_URL}/auth/auth/check-username/${username}`,
        {
          method: "GET",
          headers: {
            "Content-Type": "application/json",
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to check username");
      }

      const data = await response.json();
      if (data?.data) {
        if (type === "branch" && id) {
          setBranchUsernameErrors((prev) => ({
            ...prev,
            [id]: "Username already exists",
          }));
          setCheckingUsernames((prev) => ({ ...prev, [id]: false }));
        } else if (type === "staff" && id) {
          setStaffUsernameErrors((prev) => ({
            ...prev,
            [id]: "Username already exists",
          }));
          // Reset validation state when username exists
          setValidatedStaffUsernames((prev) => ({
            ...prev,
            [id]: false,
          }));
          setCheckingUsernames((prev) => ({ ...prev, [id]: false }));
        } else {
          setUsernameError("Username already exists");
          setCheckingUsernames((prev) => ({ ...prev, owner: false }));
        }
        return false;
      }

      if (type === "branch" && id) {
        setBranchUsernameErrors((prev) => ({
          ...prev,
          [id]: null,
        }));
        setCheckingUsernames((prev) => ({ ...prev, [id]: false }));
      } else if (type === "staff" && id) {
        setStaffUsernameErrors((prev) => ({
          ...prev,
          [id]: null,
        }));
        // Set this username as validated
        setValidatedStaffUsernames((prev) => ({
          ...prev,
          [id]: true,
        }));
        setCheckingUsernames((prev) => ({ ...prev, [id]: false }));
      } else {
        setUsernameError(null);
        setCheckingUsernames((prev) => ({ ...prev, owner: false }));
      }
      return true;
    } catch (error) {
      console.error("Error checking username:", error);
      const errorMessage = "Failed to check username availability";

      if (type === "branch" && id) {
        setBranchUsernameErrors((prev) => ({
          ...prev,
          [id]: errorMessage,
        }));
        setCheckingUsernames((prev) => ({ ...prev, [id]: false }));
      } else if (type === "staff" && id) {
        setStaffUsernameErrors((prev) => ({
          ...prev,
          [id]: errorMessage,
        }));
        // Reset validation state on error
        setValidatedStaffUsernames((prev) => ({
          ...prev,
          [id]: false,
        }));
        setCheckingUsernames((prev) => ({ ...prev, [id]: false }));
      } else {
        setUsernameError(errorMessage);
        setCheckingUsernames((prev) => ({ ...prev, owner: false }));
      }
      return false;
    }
  };

  const debouncedCheckUsername = useCallback(
    debounce(
      (
        username: string,
        id?: string,
        type: "owner" | "branch" | "staff" = "owner"
      ) => checkUsername(username, id, type),
      500
    ),
    []
  );

  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === "ownerUsername" && value.ownerUsername) {
        debouncedCheckUsername(value.ownerUsername);
      }
      if (name?.includes("branchPasswords")) {
        const branchIndex = parseInt(name.split(".")[1]);
        const branchValue = form.getValues(
          `branchPasswords.${branchIndex}.managerUsername`
        );
        const branchId = form.getValues(
          `branchPasswords.${branchIndex}.branch_id`
        );
        if (branchValue) {
          debouncedCheckUsername(branchValue, branchId, "branch");
        }
      }
      if (name?.includes("staffLogins")) {
        const staffIndex = parseInt(name.split(".")[1]);
        const staffValue = form.getValues(`staffLogins.${staffIndex}.username`);
        const staffId =
          form.getValues(`staffLogins.${staffIndex}.id`) ||
          `new-staff-${staffIndex}`;

        // Reset validation state when username changes
        if (name.includes("username")) {
          setValidatedStaffUsernames((prev) => ({
            ...prev,
            [staffId]: false,
          }));
        }

        if (staffValue) {
          debouncedCheckUsername(staffValue, staffId, "staff");
        }
      }
    });

    return () => subscription.unsubscribe();
  }, [form, debouncedCheckUsername]);
  const loginExists =
    existingLogins &&
    existingLogins?.data?.data &&
    existingLogins?.data?.data.length > 0;
  return (
    <Card className="w-full max-w-4xl p-6 space-y-6">
      <div className="flex items-center gap-2">
        <Button
          variant="ghost"
          size="icon"
          className="rounded-full"
          onClick={onBack}
        >
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div className="flex items-center gap-2">
          <ShieldCheck className="h-5 w-5 text-primary" />
          <h2 className="text-2xl font-semibold">
            {loginExists ? "Update Passwords" : "Set Up Login Credentials"}
          </h2>
        </div>
      </div>

      <div className="text-sm text-muted-foreground">
        {loginExists
          ? "Update passwords for your restaurant owner and branch accounts. Each password can be updated individually."
          : "Set up secure login credentials for your restaurant owner and branch accounts. Make sure to use strong passwords and keep them safe."}
      </div>

      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
          <div className="space-y-6">
            {/* Owner Section */}
            <div className="space-y-4 rounded-lg border p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <ShieldCheck className="h-4 w-4 text-primary" />
                  <h3 className="font-medium">Owner Account</h3>
                </div>
              </div>

              <div className="flex items-center gap-2 rounded-md bg-muted p-3">
                <div className="flex-1 space-y-1">
                  <p className="text-sm font-medium">Owner Username</p>
                  {existingLoginBranchIds.owner ? (
                    <p className="text-sm text-muted-foreground font-mono">
                      {existingLoginBranchIds.owner}
                    </p>
                  ) : (
                    <FormField
                      control={form.control}
                      name="ownerUsername"
                      render={({ field }) => (
                        <FormItem>
                          <FormControl>
                            <Input
                              {...field}
                              type="text"
                              placeholder="owner"
                              className={cn(
                                "h-8 font-mono",
                                usernameError && "border-red-500"
                              )}
                            />
                          </FormControl>
                          {usernameError && (
                            <FormMessage>{usernameError}</FormMessage>
                          )}
                        </FormItem>
                      )}
                    />
                  )}
                </div>
                <Button
                  type="button"
                  variant="ghost"
                  size="sm"
                  onClick={() =>
                    copyToClipboard(form.getValues("ownerUsername"))
                  }
                >
                  <Copy className="h-4 w-4" />
                </Button>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <FormLabel>Password</FormLabel>
                  <Button
                    type="button"
                    variant="ghost"
                    size="icon"
                    onClick={() => togglePasswordVisibility("owner")}
                  >
                    {showOwnerPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </Button>
                </div>
                <div className="flex gap-2">
                  <FormField
                    control={form.control}
                    name="ownerPassword"
                    render={({ field }) => (
                      <FormItem className="flex-1">
                        <FormControl>
                          <Input
                            type={showOwnerPassword ? "text" : "password"}
                            placeholder={
                              existingLoginBranchIds.owner
                                ? "Enter new password"
                                : "Set owner password"
                            }
                            {...field}
                          />
                        </FormControl>
                        <div className="mt-2">
                          <PasswordStrengthIndicator password={field.value} />
                        </div>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  {existingLoginBranchIds.owner ? (
                    <Button
                      type="button"
                      disabled={isSubmitting.owner}
                      onClick={() =>
                        updateOwnerPassword(form.getValues("ownerPassword"))
                      }
                    >
                      {isSubmitting.owner ? (
                        <>
                          <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                          Updating...
                        </>
                      ) : (
                        <>
                          <Zap className="mr-2 h-4 w-4" />
                          Update
                        </>
                      )}
                    </Button>
                  ) : null}
                </div>
              </div>
            </div>

            {/* Branch Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Building2 className="h-4 w-4 text-primary" />
                  <h3 className="font-medium">Branch Accounts</h3>
                </div>
                <span className="text-sm text-muted-foreground">
                  {branches.length}{" "}
                  {branches.length === 1 ? "Branch" : "Branches"}
                </span>
              </div>

              <div className="space-y-4">
                {form.watch("branchPasswords").map((branch, index) => (
                  <div
                    key={branch.branch_id}
                    className="rounded-lg border p-4 space-y-4"
                  >
                    <div className="flex items-center justify-between">
                      <FormLabel className="flex items-center gap-2 text-base">
                        {branch.branch_name}
                      </FormLabel>
                    </div>

                    <div className="flex items-center gap-2 rounded-md bg-muted p-3">
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium">Username</p>
                        {existingLoginBranchIds.managers[branch.branch_id] ? (
                          <p className="text-sm text-muted-foreground font-mono">
                            {existingLoginBranchIds.managers[branch.branch_id]}
                          </p>
                        ) : (
                          <FormField
                            control={form.control}
                            name={`branchPasswords.${index}.managerUsername`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    {...field}
                                    type="text"
                                    placeholder="manager"
                                    className={cn(
                                      "h-8 font-mono",
                                      branchUsernameErrors[branch.branch_id] &&
                                        "border-red-500"
                                    )}
                                  />
                                </FormControl>
                                {branchUsernameErrors[branch.branch_id] && (
                                  <FormMessage>
                                    {branchUsernameErrors[branch.branch_id]}
                                  </FormMessage>
                                )}
                              </FormItem>
                            )}
                          />
                        )}
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          copyToClipboard(
                            form.getValues(
                              `branchPasswords.${index}.managerUsername`
                            )
                          )
                        }
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <FormLabel>Password</FormLabel>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() =>
                            togglePasswordVisibility("branch", branch.branch_id)
                          }
                        >
                          {showBranchPasswords[branch.branch_id] ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                      <div className="flex gap-2">
                        <FormField
                          control={form.control}
                          name={`branchPasswords.${index}.password`}
                          render={({ field }) => (
                            <FormItem className="flex-1">
                              <FormControl>
                                <Input
                                  type={
                                    showBranchPasswords[branch.branch_id]
                                      ? "text"
                                      : "password"
                                  }
                                  placeholder={
                                    existingLoginBranchIds.managers[
                                      branch.branch_id
                                    ]
                                      ? "Enter new password"
                                      : "Set branch password"
                                  }
                                  {...field}
                                />
                              </FormControl>
                              <div className="mt-2">
                                <PasswordStrengthIndicator
                                  password={field.value}
                                />
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        {existingLoginBranchIds.managers[branch.branch_id] ? (
                          <Button
                            type="button"
                            disabled={isSubmitting[branch.branch_id]}
                            onClick={() =>
                              updateBranchPassword(
                                branch.branch_id,
                                form.getValues(
                                  `branchPasswords.${index}.password`
                                ),
                                index
                              )
                            }
                          >
                            {isSubmitting[branch.branch_id] ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Updating...
                              </>
                            ) : (
                              <>
                                <Zap className="mr-2 h-4 w-4" />
                                Update
                              </>
                            )}
                          </Button>
                        ) : null}

                        {existingLoginBranchIds.owner &&
                        !existingLoginBranchIds.managers[branch.branch_id] ? (
                          <Button
                            type="button"
                            disabled={isSubmitting[branch.branch_id]}
                            onClick={() =>
                              updateBranchPassword(
                                branch.branch_id,
                                form.getValues(
                                  `branchPasswords.${index}.password`
                                ),
                                index,
                                true
                              )
                            }
                          >
                            {isSubmitting[branch.branch_id] ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Creating...
                              </>
                            ) : (
                              <>
                                <Zap className="mr-2 h-4 w-4" />
                                Create New Account
                              </>
                            )}
                          </Button>
                        ) : null}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Staff Section */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <Users className="h-4 w-4 text-primary" />
                  <h3 className="font-medium">Staff Accounts</h3>
                </div>
                <span className="text-sm text-muted-foreground">
                  {form.watch("staffLogins").length}{" "}
                  {form.watch("staffLogins").length === 1
                    ? "Staff"
                    : "Staff Members"}
                </span>
              </div>

              <div className="space-y-4">
                {form.watch("staffLogins").map((staff, index) => (
                  <div
                    key={staff.id || `new-staff-${index}`}
                    className="rounded-lg border p-4 space-y-4"
                  >
                    <div className="flex items-center justify-between">
                      <FormLabel className="flex items-center gap-2 text-base">
                        Staff Member {index + 1}
                      </FormLabel>
                      {!staff.id && (
                        <Button
                          type="button"
                          variant="ghost"
                          size="sm"
                          onClick={() => {
                            const staffLogins = form.getValues("staffLogins");
                            staffLogins.splice(index, 1);
                            form.setValue("staffLogins", [...staffLogins]);
                          }}
                        >
                          <Trash2 className="h-4 w-4 text-destructive" />
                        </Button>
                      )}
                    </div>

                    <div className="flex items-center gap-2 rounded-md bg-muted p-3">
                      <div className="flex-1 space-y-1">
                        <p className="text-sm font-medium">Username</p>
                        {staff.id ? (
                          <p className="text-sm text-muted-foreground font-mono">
                            {staff.username}
                          </p>
                        ) : (
                          <FormField
                            control={form.control}
                            name={`staffLogins.${index}.username`}
                            render={({ field }) => (
                              <FormItem>
                                <FormControl>
                                  <Input
                                    {...field}
                                    type="text"
                                    placeholder="staff"
                                    className={cn(
                                      "h-8 font-mono",
                                      staffUsernameErrors[
                                        staff.id || `new-staff-${index}`
                                      ] && "border-red-500"
                                    )}
                                  />
                                </FormControl>
                                {staffUsernameErrors[
                                  staff.id || `new-staff-${index}`
                                ] && (
                                  <FormMessage>
                                    {
                                      staffUsernameErrors[
                                        staff.id || `new-staff-${index}`
                                      ]
                                    }
                                  </FormMessage>
                                )}
                                {!staff.id &&
                                  !staffUsernameErrors[
                                    `new-staff-${index}`
                                  ] && (
                                    <p className="text-xs text-muted-foreground mt-1 flex items-center">
                                      {checkingUsernames[
                                        `new-staff-${index}`
                                      ] ? (
                                        <>
                                          <Loader2 className="h-3 w-3 mr-1 animate-spin" />
                                          Checking username...
                                        </>
                                      ) : validatedStaffUsernames[
                                          `new-staff-${index}`
                                        ] ? (
                                        <>
                                          <Check className="h-3 w-3 mr-1 text-green-500" />
                                          Username is available
                                        </>
                                      ) : (
                                        <>
                                          Type a username and wait for
                                          validation
                                        </>
                                      )}
                                    </p>
                                  )}
                              </FormItem>
                            )}
                          />
                        )}
                      </div>
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          copyToClipboard(
                            form.getValues(`staffLogins.${index}.username`)
                          )
                        }
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>

                    <div className="mt-4">
                      <FormField
                        control={form.control}
                        name={`staffLogins.${index}.branch_id`}
                        render={({ field }) => (
                          <FormItem>
                            <FormLabel>Assigned Branch</FormLabel>
                            {staff.id ? (
                              <div className="rounded-md border px-3 py-2 text-sm text-muted-foreground">
                                {branches.find(
                                  (b) => b.branch_id === field.value
                                )?.branch_name || field.value}
                              </div>
                            ) : (
                              <Select
                                value={field.value}
                                onValueChange={field.onChange}
                              >
                                <FormControl>
                                  <SelectTrigger>
                                    <SelectValue placeholder="Select a branch" />
                                  </SelectTrigger>
                                </FormControl>
                                <SelectContent>
                                  {branches.map((branch) => (
                                    <SelectItem
                                      key={branch.branch_id}
                                      value={branch.branch_id}
                                    >
                                      {branch.branch_name}
                                    </SelectItem>
                                  ))}
                                </SelectContent>
                              </Select>
                            )}
                            <FormMessage />
                          </FormItem>
                        )}
                      />
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between">
                        <FormLabel>Password</FormLabel>
                        <Button
                          type="button"
                          variant="ghost"
                          size="icon"
                          onClick={() =>
                            togglePasswordVisibility(
                              "staff",
                              staff.id || `new-staff-${index}`
                            )
                          }
                        >
                          {showStaffPasswords[
                            staff.id || `new-staff-${index}`
                          ] ? (
                            <EyeOff className="h-4 w-4" />
                          ) : (
                            <Eye className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                      <div className="flex gap-2">
                        <FormField
                          control={form.control}
                          name={`staffLogins.${index}.password`}
                          render={({ field }) => (
                            <FormItem className="flex-1">
                              <FormControl>
                                <Input
                                  type={
                                    showStaffPasswords[
                                      staff.id || `new-staff-${index}`
                                    ]
                                      ? "text"
                                      : "password"
                                  }
                                  placeholder="Set staff password"
                                  {...field}
                                />
                              </FormControl>
                              <div className="mt-2">
                                <PasswordStrengthIndicator
                                  password={field.value}
                                />
                              </div>
                              <FormMessage />
                            </FormItem>
                          )}
                        />
                        {staff.id ? (
                          <Button
                            type="button"
                            disabled={isSubmitting[staff.id]}
                            onClick={() =>
                              updateStaffPassword(
                                staff.id || `staff-${index}`,
                                form.getValues(`staffLogins.${index}.password`),
                                index
                              )
                            }
                          >
                            {isSubmitting[staff.id] ? (
                              <>
                                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                Updating...
                              </>
                            ) : (
                              <>
                                <Zap className="mr-2 h-4 w-4" />
                                Update
                              </>
                            )}
                          </Button>
                        ) : (
                          <div className="relative group">
                            <Button
                              type="button"
                              disabled={
                                isSubmitting[`new-staff-${index}`] ||
                                checkingUsernames[`new-staff-${index}`] ||
                                !validatedStaffUsernames[
                                  `new-staff-${index}`
                                ] ||
                                !form.getValues(
                                  `staffLogins.${index}.username`
                                ) ||
                                !form.getValues(
                                  `staffLogins.${index}.password`
                                ) ||
                                !form.getValues(
                                  `staffLogins.${index}.branch_id`
                                )
                              }
                              onClick={() =>
                                updateStaffPassword(
                                  `new-staff-${index}`,
                                  form.getValues(
                                    `staffLogins.${index}.password`
                                  ),
                                  index,
                                  true
                                )
                              }
                            >
                              {isSubmitting[`new-staff-${index}`] ? (
                                <>
                                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                                  Creating...
                                </>
                              ) : (
                                <>
                                  <Zap className="mr-2 h-4 w-4" />
                                  Create
                                </>
                              )}
                            </Button>
                            {(checkingUsernames[`new-staff-${index}`] ||
                              !validatedStaffUsernames[`new-staff-${index}`] ||
                              !form.getValues(
                                `staffLogins.${index}.username`
                              ) ||
                              !form.getValues(
                                `staffLogins.${index}.password`
                              ) ||
                              !form.getValues(
                                `staffLogins.${index}.branch_id`
                              )) && (
                              <div className="absolute left-0 -bottom-10 z-10 hidden group-hover:block bg-black text-white text-xs rounded py-1 px-2 whitespace-nowrap">
                                {!form.getValues(
                                  `staffLogins.${index}.username`
                                )
                                  ? "Enter a username"
                                  : checkingUsernames[`new-staff-${index}`]
                                  ? "Checking username..."
                                  : !validatedStaffUsernames[
                                      `new-staff-${index}`
                                    ]
                                  ? "Username needs validation"
                                  : !form.getValues(
                                      `staffLogins.${index}.password`
                                    )
                                  ? "Enter a password"
                                  : "Select a branch"}
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}

                <Button
                  type="button"
                  variant="outline"
                  className="w-full"
                  onClick={() => {
                    const staffLogins = form.getValues("staffLogins") || [];
                    const newIndex = staffLogins.length;
                    form.setValue("staffLogins", [
                      ...staffLogins,
                      {
                        username: "",
                        password: "",
                        branch_id:
                          branches.length > 0 ? branches[0].branch_id : "",
                      },
                    ]);
                    // Reset validation state for the new staff member
                    setValidatedStaffUsernames((prev) => ({
                      ...prev,
                      [`new-staff-${newIndex}`]: false,
                    }));
                  }}
                >
                  <PlusCircle className="mr-2 h-4 w-4" />
                  Add Staff Member
                </Button>
              </div>
            </div>
          </div>

          {!loginExists && (
            <Button
              type="submit"
              className="w-full"
              disabled={isSubmitting.all}
            >
              {isSubmitting.all ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Creating Login Credentials...
                </>
              ) : (
                <>
                  <Zap className="mr-2 h-4 w-4" />
                  Create Login Credentials
                </>
              )}
            </Button>
          )}
        </form>
      </Form>
    </Card>
  );
}
