export async function POST() {
  try {
    const response = await fetch("https://get-location.qranalytica.com", {
      method: "POST",
    });

    if (!response.ok) {
      return new Response(
        JSON.stringify({ error: "Failed to fetch location" }),
        {
          status: response.status,
        }
      );
    }

    const data = await response.json();

    return new Response(JSON.stringify(data), {
      status: 200,
      headers: {
        "Content-Type": "application/json",
      },
    });
  } catch (error) {
    console.error("Error fetching location:", error);
    return new Response(JSON.stringify({ error: "Internal server error" }), {
      status: 500,
    });
  }
}
