"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter, // Added for potential future links
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useState, useEffect, FormEvent, Suspense } from "react"; // Added FormEvent
import { signIn, useSession } from "next-auth/react";
import { useRouter, useSearchParams } from "next/navigation"; // Combined imports
import { Loader2 } from "lucide-react"; // Added for loading spinner

function LoginPage() {
  const router = useRouter();
  const { status } = useSession(); // Removed unused 'session'
  const searchParams = useSearchParams();
  const callbackUrl = searchParams.get("callbackUrl") || "/admin";

  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);

  // Define onSubmit outside the conditional block
  async function onSubmit(event: FormEvent<HTMLFormElement>) {
    event.preventDefault();
    setIsLoading(true);
    setError(null);

    const formData = new FormData(event.currentTarget);
    try {
      const response = await signIn("credentials", {
        username: formData.get("username"),
        password: formData.get("password"),
        redirect: false,
        callbackUrl,
      });

      if (response?.error) {
        // Handle specific errors if possible, otherwise generic message
        setError("Invalid username or password.");
        setIsLoading(false);
        return;
      }

      if (response?.ok) {
        // Successful login, router.push will be handled by useEffect
        // No need to explicitly push here if useEffect handles authenticated state
        // router.push(callbackUrl); // Can be removed if useEffect handles it reliably
        router.refresh(); // Refresh server components if needed
        return;
      }

      // Handle unexpected non-error responses if necessary
      setError("An unexpected error occurred. Please try again.");
    } catch (err) {
      console.error("Login error:", err);
      setError("An error occurred during login. Please try again.");
    } finally {
      setIsLoading(false);
    }
  }

  useEffect(() => {
    // Redirect authenticated users
    if (status === "authenticated") {
      router.push(callbackUrl);
    }
  }, [status, router, callbackUrl]);

  // Loading state while checking session
  if (status === "loading") {
    return (
      <div className="flex min-h-screen w-full items-center justify-center bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-900 dark:to-gray-800">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
      </div>
    );
  }

  // Render login form for unauthenticated users
  if (status === "unauthenticated") {
    return (
      <div className="flex min-h-screen w-full items-center justify-center bg-gradient-to-br from-gray-100 via-white to-gray-100 dark:from-gray-900 dark:via-black dark:to-gray-900 p-4">
        <Card className="w-full max-w-sm shadow-xl border-border/40">
          <CardHeader className="items-center text-center space-y-2">
            {/* Optional: Add Logo here */}
            {/* <Image src="/logo.png" alt="Logo" width={60} height={60} /> */}
            <CardTitle className="text-2xl font-bold tracking-tight">
              Admin Login
            </CardTitle>
            <CardDescription>
              Enter your credentials to access the admin panel.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <form onSubmit={onSubmit} className="space-y-4">
              <div className="space-y-2">
                <Label htmlFor="username">Username</Label>
                <Input
                  id="username"
                  name="username"
                  placeholder="e.g., admin"
                  required
                  disabled={isLoading}
                  className="h-10" // Consistent height
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="password">Password</Label>
                <Input
                  id="password"
                  name="password"
                  type="password"
                  placeholder="••••••••"
                  required
                  disabled={isLoading}
                  className="h-10" // Consistent height
                />
              </div>
              {error && (
                <div className="text-sm font-medium text-destructive bg-destructive/10 p-2 rounded-md text-center">
                  {error}
                </div>
              )}
              <Button
                className="w-full h-10 font-semibold"
                type="submit"
                disabled={isLoading}
              >
                {isLoading ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Signing in...
                  </>
                ) : (
                  "Sign in"
                )}
              </Button>
            </form>
          </CardContent>
          {/* Optional Footer for links like Forgot Password */}
          {/* <CardFooter className="text-center text-sm">
            <a href="#" className="text-primary hover:underline">Forgot Password?</a>
          </CardFooter> */}
        </Card>
      </div>
    );
  }

  // Fallback for unexpected status (shouldn't normally be reached)
  return null;
}
export default function MainLogin() {
  return (
    <Suspense
      fallback={<Loader2 className="h-8 w-8 animate-spin text-primary" />}
    >
      <LoginPage />
    </Suspense>
  );
}
