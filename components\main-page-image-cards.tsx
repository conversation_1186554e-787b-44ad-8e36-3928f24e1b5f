"use client";
import { useRef } from "react";
import Image from "next/image";
import smallplaceholder from "@/public/PLACEHOLDER2.png";
import foodbox from "@/public/Food box 1.png";
import commercebox from "@/public/commerce Box 1.png";
import sportsbox from "@/public/SportsBox1.png";
import Link from "next/link";

const MainPageImageCards = () => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);

  return (
    <div className="w-full relative mb-10">
      <div
        ref={scrollContainerRef}
        className="w-full flex gap-3 overflow-x-auto pb-4 hide-scrollbar snap-x snap-mandatory [&::-webkit-scrollbar]:hidden [-ms-overflow-style:'none'] [scrollbar-width:'none'] [webkit-overflow-scrolling:touch]"
      >
        <Link
          href="/food"
          passHref
          className="flex-shrink-0 rounded-xl overflow-hidden relative snap-center snap-always aspect-[384/420] w-[200px] sm:w-[322px] lg:w-[388px] h-full"
        >
          <Image
            src={foodbox}
            alt={`Thumbnail Food Card`}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            draggable={false}
            style={{
              objectFit: "cover",
            }}
          />
        </Link>

        <Link
          href="/commerce"
          className="flex-shrink-0 rounded-xl overflow-hidden relative snap-center snap-always aspect-[384/420] w-[200px] sm:w-[322px] lg:w-[388px] h-full"
        >
          <Image
            src={commercebox}
            alt={`Thumbnail Commerce Card`}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            draggable={false}
            style={{
              objectFit: "cover",
            }}
          />
        </Link>

        <Link
          href="/sports"
          className="flex-shrink-0 rounded-xl overflow-hidden relative snap-center snap-always aspect-[384/420] w-[200px] sm:w-[322px] lg:w-[388px] h-full"
        >
          <Image
            src={sportsbox}
            alt={`Thumbnail Sports Card`}
            fill
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            draggable={false}
            style={{
              objectFit: "cover",
            }}
            unoptimized
          />
        </Link>
      </div>
    </div>
  );
};

export default MainPageImageCards;
