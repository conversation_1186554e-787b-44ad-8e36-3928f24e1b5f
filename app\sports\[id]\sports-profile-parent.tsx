"use client";
import React from "react";
import { useQuery } from "@tanstack/react-query";
import SportsProfile from "./sports-profile";

export default function SportsProfileParent({ clubId }: { clubId: string }) {
  //useQuery on get api here
  const { data: clubProfileData } = useQuery({
    queryKey: ["getClubProfileInfo", clubId],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL +
          "/clubs/landing-page/profile-page-details/" +
          clubId
      );
      const data = await response.json();
    //   console.log(data.data);
      return data.data;
    },
  });

  const { data: clubFAQData } = useQuery({
    queryKey: ["getClubFAQ", clubId],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL +
          "/clubs/landing-page/get-club-faqs/" +
          clubId
      );
      const data = await response.json();
      // console.log(data.data);
      return data.data;
    },
  });
  return (
    <>
      {clubProfileData && (
        <SportsProfile
          clubProfileData={clubProfileData}
          clubFAQData={clubFAQData}
        />
      )}
    </>
  );
}
