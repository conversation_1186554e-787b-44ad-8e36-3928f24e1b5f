"use client";
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { Card, CardContent } from "@/components/ui/card";
import {
  Search,
  Plus,
  Edit,
  CheckCircle,
  Clock,
  ArrowUpDown,
  Building2,
  MoreVertical,
  Trash2,
  AlertCircle,
  Loader2,
  Eye,
  EyeOff,
  Copy,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { FaWhatsapp } from "react-icons/fa";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import DeleteConfirmationDialog from "@/components/ui/delete-confirmation-dialog";
import { toast } from "sonner";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import CopyMenuModal from "@/components/ui/copy-menu-modal";

// Interface for shop data structure
interface Shop {
  id: string;
  shopId: string;
  name: string;
  phone: string;
  isWhatsappOnboardingCompleted: boolean;
  addedOn: string;
  is_only_for_listing?: boolean;
  isVisible?: boolean; // Add visibility property
}

// Function to fetch shops from API
const fetchShops = async (token: string | undefined) => {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL}/shops/onboarding-dashboard/shops-list`,
    {
      headers: {
        accept: "application/json",
        Authorization: token ? `Bearer ${token}` : "",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch shops");
  }

  const data = await response.json();
  return data.data;
};

// Add function to toggle shop visibility
const toggleShopVisibility = async (
  shopId: string,
  isVisible: boolean,
  token: string
) => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL}/shops/toggle-visibility/${shopId}`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ status: isVisible }),
      }
    );

    if (!response.ok) {
      throw new Error("Failed to update shop visibility");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

const ShopPage = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [filter, setFilter] = useState("");
  const [listingTypeFilter, setListingTypeFilter] = useState<string>("all"); // Add new filter state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [shopToDelete, setShopToDelete] = useState<Shop | null>(null);
  const [copyProductsModalOpen, setCopyProductsModalOpen] = useState(false);
  const [selectedShop, setSelectedShop] = useState<Shop | null>(null);

  const { data: session } = useSession();
  const accessToken = session?.user.access_token;

  const queryClient = useQueryClient();

  // Delete shop mutation
  const { mutate: deleteShop, isPending: isDeleting } = useMutation({
    mutationFn: async (shopId: string) => {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_COMMERCE_BACKEND_URL}/shops/delete/${shopId}`,
        {
          method: "DELETE",
          headers: {
            accept: "application/json",
            Authorization: accessToken ? `Bearer ${accessToken}` : "",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete shop");
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success("Shop deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["shops"] });
      setDeleteDialogOpen(false);
      setShopToDelete(null);
    },
    onError: (error) => {
      toast.error(`Error deleting shop: ${error.message}`);
      setDeleteDialogOpen(false);
    },
  });

  const handleDeleteClick = (shop: Shop) => {
    setShopToDelete(shop);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (shopToDelete) {
      deleteShop(shopToDelete.id);
    }
  };

  // Mutation for toggling visibility
  const { mutate: updateVisibility } = useMutation({
    mutationFn: async ({
      shopId,
      isVisible,
    }: {
      shopId: string;
      isVisible: boolean;
    }) => {
      return toggleShopVisibility(shopId, isVisible, accessToken || "");
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["shops"] });
      toast.success("Shop visibility updated successfully");
    },
    onError: (error) => {
      toast.error(`Error updating shop visibility: ${error.message}`);
    },
  });

  const handleVisibilityToggle = (shop: Shop, newVisibility: boolean) => {
    updateVisibility({ shopId: shop.id, isVisible: newVisibility });
  };

  const handleCopyProductsClick = (shop: Shop) => {
    setSelectedShop(shop);
    setCopyProductsModalOpen(true);
  };

  // Fetch shops with React Query
  const {
    data: shopsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["shops", accessToken],
    queryFn: () => fetchShops(accessToken),
    enabled: !!accessToken,
  });

  // Use fetched data if available, otherwise empty array
  const shops = shopsData || [];

  // Filter shops based on search term, active tab, and listing type
  const filteredShops = shops.filter((shop: Shop) => {
    const matchesSearch =
      filter === "" || shop.name.toLowerCase().includes(filter.toLowerCase());

    // Filter by tab (onboarding status)
    let matchesTab = true;
    if (activeTab === "onboarded") {
      matchesTab = shop.isWhatsappOnboardingCompleted;
    } else if (activeTab === "pending") {
      matchesTab = !shop.isWhatsappOnboardingCompleted;
    }

    // Filter by listing type
    let matchesListingType = true;
    if (listingTypeFilter === "cravin_users") {
      matchesListingType = !shop.is_only_for_listing;
    } else if (listingTypeFilter === "only_listing") {
      matchesListingType = !!shop.is_only_for_listing;
    }

    return matchesSearch && matchesTab && matchesListingType;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredShops.length / itemsPerPage);

  // Get current page data
  const paginatedShops = filteredShops.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Count for each category
  const onboardedCount = shops.filter(
    (shop: Shop) => shop.isWhatsappOnboardingCompleted
  ).length;
  const pendingCount = shops.filter(
    (shop: Shop) => !shop.isWhatsappOnboardingCompleted
  ).length;

  // Handle loading state
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 flex items-center justify-center h-[50vh]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">Loading shops data...</p>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load shops data. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 pb-4 border-b">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">Shop Management</h1>
          <p className="text-muted-foreground mt-2 text-base">
            Manage and monitor all shops in the Cravin platform
          </p>
        </div>
        <div className="flex gap-3 self-end sm:self-auto">
          <Link href="/admin/commerce/onboarding">
            <Button className="gap-2 shadow-sm">
              <Plus size={16} />
              Onboard New Shop
            </Button>
          </Link>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 gap-4">
          <TabsList className="w-full sm:w-auto bg-muted/60 p-1 rounded-lg">
            <TabsTrigger
              value="all"
              className="flex gap-2 rounded data-[state=active]:bg-background data-[state=active]:shadow-sm"
            >
              All
              <Badge variant="secondary" className="ml-1 bg-primary/10">
                {shops.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger
              value="onboarded"
              className="flex gap-2 rounded data-[state=active]:bg-background data-[state=active]:shadow-sm"
            >
              Onboarded
              <Badge
                variant="secondary"
                className="ml-1 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
              >
                {onboardedCount}
              </Badge>
            </TabsTrigger>
            <TabsTrigger
              value="pending"
              className="flex gap-2 rounded data-[state=active]:bg-background data-[state=active]:shadow-sm"
            >
              Pending
              <Badge
                variant="secondary"
                className="ml-1 bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400"
              >
                {pendingCount}
              </Badge>
            </TabsTrigger>
          </TabsList>

          <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search shops..."
                className="pl-9 w-full border-muted bg-background focus-visible:ring-1"
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
              />
            </div>

            {/* Add listing type filter */}
            <Select
              value={listingTypeFilter}
              onValueChange={(value) => {
                setListingTypeFilter(value);
                setCurrentPage(1); // Reset to first page when filter changes
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="cravin_users">Cravin Users</SelectItem>
                <SelectItem value="only_listing">Only For Listings</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <TabsContent value="all" className="m-0 mt-0">
          <ShopsTable
            shops={paginatedShops}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={filteredShops.length}
            onPreviousPage={() =>
              setCurrentPage((prev) => Math.max(prev - 1, 1))
            }
            onNextPage={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            setFilter={setFilter}
            onDeleteClick={handleDeleteClick}
            setCurrentPage={setCurrentPage}
            setListingTypeFilter={setListingTypeFilter}
            onVisibilityToggle={handleVisibilityToggle}
            onCopyProductsClick={handleCopyProductsClick}
          />
        </TabsContent>

        <TabsContent value="onboarded" className="m-0 mt-0">
          <ShopsTable
            shops={paginatedShops}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={filteredShops.length}
            onPreviousPage={() =>
              setCurrentPage((prev) => Math.max(prev - 1, 1))
            }
            onNextPage={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            setFilter={setFilter}
            onDeleteClick={handleDeleteClick}
            setCurrentPage={setCurrentPage}
            setListingTypeFilter={setListingTypeFilter}
            onVisibilityToggle={handleVisibilityToggle}
            onCopyProductsClick={handleCopyProductsClick}
          />
        </TabsContent>

        <TabsContent value="pending" className="m-0 mt-0">
          <ShopsTable
            shops={paginatedShops}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={filteredShops.length}
            onPreviousPage={() =>
              setCurrentPage((prev) => Math.max(prev - 1, 1))
            }
            onNextPage={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            setFilter={setFilter}
            onDeleteClick={handleDeleteClick}
            setCurrentPage={setCurrentPage}
            setListingTypeFilter={setListingTypeFilter}
            onVisibilityToggle={handleVisibilityToggle}
            onCopyProductsClick={handleCopyProductsClick}
          />
        </TabsContent>
      </Tabs>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Shop"
        description={`This shop will be deleted, along with all of its Branches, Products.`}
        onConfirm={confirmDelete}
        isLoading={isDeleting}
        confirmText="delete my shop"
      />

      {selectedShop && (
        <CopyMenuModal
          isOpen={copyProductsModalOpen}
          onClose={() => setCopyProductsModalOpen(false)}
          product={selectedShop}
          accessToken={accessToken || ""}
          productType="commerce"
        />
      )}
    </div>
  );
};

// Separated table component with pagination
const ShopsTable = ({
  shops,
  currentPage,
  totalPages,
  totalItems,
  onPreviousPage,
  onNextPage,
  setFilter,
  onDeleteClick,
  setCurrentPage,
  setListingTypeFilter,
  onVisibilityToggle,
  onCopyProductsClick,
}: {
  shops: Shop[];
  currentPage: number;
  totalPages: number;
  totalItems: number;
  onPreviousPage: () => void;
  onNextPage: () => void;
  setFilter: (value: string) => void;
  onDeleteClick: (shop: Shop) => void;
  setCurrentPage: (page: number) => void;
  setListingTypeFilter: (value: string) => void;
  onVisibilityToggle: (shop: Shop, isVisible: boolean) => void;
  onCopyProductsClick: (shop: Shop) => void;
}) => {
  return (
    <Card className="shadow-sm border-muted overflow-hidden">
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/30 hover:bg-muted/30">
                <TableHead className="w-[250px] font-semibold">
                  <div className="flex items-center gap-1">
                    Shop Name
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 ml-1 rounded-full"
                    >
                      <ArrowUpDown
                        size={14}
                        className="text-muted-foreground"
                      />
                    </Button>
                  </div>
                </TableHead>

                <TableHead className="font-semibold">
                  WhatsApp Onboarding
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-1">
                    Added On
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 ml-1 rounded-full"
                    >
                      <ArrowUpDown
                        size={14}
                        className="text-muted-foreground"
                      />
                    </Button>
                  </div>
                </TableHead>
                <TableHead className="font-semibold text-center">
                  Access Toggle
                </TableHead>
                <TableHead className="text-right font-semibold">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {shops.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={8}
                    className="text-center py-12 text-muted-foreground h-[200px]"
                  >
                    <div className="flex flex-col items-center gap-2">
                      <Search className="h-8 w-8 text-muted-foreground/60" />
                      <p>No shops found matching your criteria</p>
                      <Button
                        variant="link"
                        className="mt-2"
                        onClick={() => {
                          setFilter("");
                          setCurrentPage(1); // Reset to first page when filter is cleared
                          setListingTypeFilter("all"); // Reset listing type filter
                        }}
                      >
                        Clear filters
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                shops.map((shop) => (
                  <TableRow key={shop.id} className="group">
                    <TableCell className="font-medium">
                      <div className="flex flex-col">
                        <span className="text-base">{shop.name}</span>
                        <span className="text-xs text-muted-foreground mt-1">
                          ID: {shop.shopId}
                        </span>
                      </div>
                    </TableCell>

                    <TableCell>
                      {shop.isWhatsappOnboardingCompleted ? (
                        <div className="flex items-center gap-2">
                          <Badge
                            variant="outline"
                            className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950/30 dark:text-green-400 dark:border-green-800 flex items-center gap-1.5 py-1.5"
                          >
                            <CheckCircle className="h-3.5 w-3.5" />
                            <span>Completed</span>
                          </Badge>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Badge
                            variant="outline"
                            className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950/30 dark:text-amber-400 dark:border-amber-800 flex items-center gap-1.5 py-1.5"
                          >
                            <Clock className="h-3.5 w-3.5" />
                            <span>Pending</span>
                          </Badge>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(shop.addedOn).toLocaleDateString("en-US", {
                        year: "numeric",
                        month: "short",
                        day: "numeric",
                      })}
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center gap-2">
                        <span className="text-sm text-muted-foreground mr-2">
                          {shop.isVisible !== false ? (
                            <Eye className="h-4 w-4 text-green-600" />
                          ) : (
                            <EyeOff className="h-4 w-4 text-gray-400" />
                          )}
                        </span>
                        <Switch
                          checked={shop.isVisible !== false}
                          onCheckedChange={(checked) =>
                            onVisibilityToggle(shop, checked)
                          }
                          className="data-[state=checked]:bg-green-500"
                        />
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        {!shop.isWhatsappOnboardingCompleted ? (
                          <Link
                            href={`/admin/onboard-whatsapp/commerce/${shop.id}`}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 shadow-sm bg-white hover:bg-green-50 text-green-600 border-green-200 hover:border-green-300"
                            >
                              <FaWhatsapp className="h-3.5 w-3.5 mr-1" />
                              Onboard WhatsApp
                            </Button>
                          </Link>
                        ) : (
                          <Link
                            href={`/admin/onboard-whatsapp/profile/commerce/${shop.id}`}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 shadow-sm bg-white hover:bg-green-50 text-green-600 border-green-200 hover:border-green-300"
                            >
                              <FaWhatsapp className="h-3.5 w-3.5 mr-1" />
                              Edit WhatsApp
                            </Button>
                          </Link>
                        )}

                        <Link
                          href={`/admin/commerce/onboarding/listing-details?id=${shop.id}`}
                        >
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 shadow-sm bg-white hover:bg-orange-50 text-orange-600 border-orange-200 hover:border-orange-300"
                          >
                            <Building2 className="h-3.5 w-3.5 mr-1" />
                            Store Listings
                          </Button>
                        </Link>

                        <Link href={`/admin/commerce/onboarding?id=${shop.id}`}>
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 shadow-sm hover:bg-muted"
                          >
                            <Edit className="h-3.5 w-3.5 mr-1" />
                            Edit Details
                          </Button>
                        </Link>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              className="flex items-center cursor-pointer"
                              onClick={() => onCopyProductsClick(shop)}
                            >
                              <Copy className="h-4 w-4 mr-2" />
                              Copy Products to Store
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-destructive focus:text-destructive flex items-center cursor-pointer"
                              onClick={() => onDeleteClick(shop)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete Shop
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
        <div className="flex items-center justify-between px-6 py-4 border-t">
          <div className="text-sm text-muted-foreground">
            Showing <strong>{shops.length}</strong> of{" "}
            <strong>{totalItems}</strong> shops
          </div>
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage <= 1}
              onClick={onPreviousPage}
              className="h-8 px-3"
            >
              Previous
            </Button>
            {totalPages > 0 && (
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-4 bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground"
              >
                {currentPage}
              </Button>
            )}
            {totalPages > 1 && currentPage < totalPages && (
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-4"
                onClick={() => onNextPage()}
              >
                {currentPage + 1}
              </Button>
            )}
            {totalPages > 2 && currentPage < totalPages - 1 && (
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-4"
                onClick={() => onNextPage()}
              >
                {currentPage + 2}
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage >= totalPages || totalPages === 0}
              onClick={onNextPage}
              className="h-8 px-3"
            >
              Next
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default ShopPage;
