import Sidebar from "@/components/admin/sidebar";
import { ReactNode } from "react";
import { Toaster } from "@/components/ui/sonner";
import "./style.css"
export default function AdminLayout({ children }: { children: ReactNode }) {
  return (
    <div className="flex h-screen w-full overflow-hidden bg-muted/10">
      <Sidebar />
      <div className="flex flex-col flex-1 h-full overflow-hidden">
        {/* <Header /> */}
        <Toaster position='top-center' richColors />
        <main className="flex-1 overflow-y-auto">
          {children}
        </main>
      </div>
    </div>
  );
}
