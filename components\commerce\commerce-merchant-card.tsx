import React from "react";
import {
  MdAccessTimeFilled,
  MdDirectionsBike,
  MdLocationPin,
  MdMyLocation,
} from "react-icons/md";
import Image from "next/image";
import testlistingimg from "@/public/Cravin_food_placeholder.png";
import { Badge } from "../ui/badge";
import dayjs from "dayjs";
import customParseFormat from "dayjs/plugin/customParseFormat";
import { useRouter } from "next/navigation";
dayjs.extend(customParseFormat);

const CommerceMerchantCard = ({ shop }: { shop: any }) => {
  const router = useRouter();
  const currentDayOfWeek = dayjs().day();

  const timing = Array.isArray(shop?.branchTiming)
    ? shop.branchTiming.find((timing: any) => timing.day === currentDayOfWeek)
    : null;

  return (
    <div
      className="w-full h-full max-w-[384px] min-h-[354px] flex flex-col items-start justify-start bg-white rounded-xl cursor-pointer overflow-hidden border border-gray-100 transition-all duration-300 hover:drop-shadow-lg hover:shadow-lg"
      onClick={() => {
        router.push(`/commerce/${shop.branchId}`);
      }}
    >
      <div className="relative w-full h-[206px] overflow-hidden">
        <Image
          alt={shop?.branchDisplayName || "Outlet listing image"}
          src={
            shop.listingImage && shop.listingImage.length > 0
              ? shop.listingImage
              : testlistingimg
          }
          fill
          sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
          style={{
            objectFit: "cover",
          }}
        />
        {!shop?.onlyForListing && (
          <div className="absolute top-0 right-5 bg-gradient-to-l from-orange-500 to-orange-600 text-white py-1 px-2 rounded-bl-lg rounded-br-lg font-medium shadow text-xs">
            Order with Cravin
          </div>
        )}
      </div>

      <div className="flex flex-col gap-2 w-full p-4 font-normal text-sm">
        <p className="text-lg font-medium line-clamp-1">
          {shop?.branchDisplayName}
        </p>
        <div className="flex w-full justify-between gap-2">
          <div className="flex items-center gap-2 text-gray-700 font-normal">
            <MdAccessTimeFilled
              size={18}
              className="text-gray-700 flex-shrink-0"
            />
            {shop?.branchTiming && timing && (
              <div className="line-clamp-1 w-full">
                {timing.start} to {timing.end}
              </div>
            )}
          </div>
          {/* {shop?.averageSpend && (
            <Badge className="text-xs max-w-[180px] line-clamp-1 font-normal text-gray-700 bg-gray-100 hover:bg-gray-200 px-2 py-1">
              AED {shop.averageSpend} for two
            </Badge>
          )} */}
        </div>

        <div className="flex items-center gap-2 text-gray-700 font-normal">
          <MdLocationPin size={18} className="text-gray-700 flex-shrink-0" />
          <p className="line-clamp-1">{shop?.address}</p>
        </div>

        {shop?.distanceFromUser && (
          <div className="flex w-full justify-between">
            <div className="flex items-center gap-2 text-gray-700 font-normal">
              <MdMyLocation size={18} className="text-gray-700 flex-shrink-0" />
              <p className="line-clamp-1">
                {shop?.distanceFromUser.toFixed(1)} km away
              </p>
            </div>

            {shop?.deliverable && (
              <Badge className="bg-green-100 hover:bg-green-200 text-green-700 border-blue-200 text-xs font-normal flex items-center gap-1">
                <MdDirectionsBike size={14} />
                Delivers to your location
              </Badge>
            )}
          </div>
        )}

        <div className="w-full truncate mt-2">
          {shop?.branchTags &&
            shop?.branchTags.length > 0 &&
            shop?.branchTags?.map((tag: any) => (
              <Badge
                key={tag}
                className="bg-gray-100 hover:bg-gray-200 text-gray-700 border-none font-normal mr-1  capitalize"
                variant="outline"
              >
                {tag}
              </Badge>
            ))}
        </div>
      </div>
    </div>
  );
};

export default CommerceMerchantCard;
