import { redirect } from "next/navigation";
import React from "react";
import { getServerSession } from "next-auth/next";
 import { HydrationBoundary, dehydrate } from "@tanstack/react-query";
import { queryClient } from "@/lib/query-client";
import OnboardingPage from "./onboarding-page";
import { authOptions } from "@/lib/auth";

export default async function Page({ params }: { params: { slug: string[] } }) {
  const session = await getServerSession(authOptions);

  if (!session) {
    redirect("/login");
  }

  const access_token = session?.user.access_token;

  return (
    <HydrationBoundary state={dehydrate(queryClient)}>
      <OnboardingPage token={access_token} params={params} />
    </HydrationBoundary>
  );
}
