"use client";
import React from "react";
import FoodProfile from "./food-profile";
import { useQuery } from "@tanstack/react-query";

export default function FoodProfileParent({ branchId }: { branchId: string }) {
  //useQuery on get api here
  const { data: profileData } = useQuery({
    queryKey: ["getProfileInfo", branchId],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_FOOD_BACKEND_URL +
          "/branches/landing-page/profile-page-details/" +
          branchId
      );
      const data = await response.json();
      // console.log(data.data);
      return data.data;
    },
  });

  const { data: foodFAQData } = useQuery({
    queryKey: ["getFoodFAQ", branchId],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_FOOD_BACKEND_URL +
          "/branches/landing-page/get-branch-faqs/" +
          branchId
      );
      const data = await response.json();
      // console.log(data.data);
      return data.data;
    },
  });
  return (
    <>
      {profileData &&
        profileData.branchTiming &&
        profileData.branchTiming.length > 0 && (
          <FoodProfile profileData={profileData} foodFAQData={foodFAQData} />
        )}
    </>
  );
}
