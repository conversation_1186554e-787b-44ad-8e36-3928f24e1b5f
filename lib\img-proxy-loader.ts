export default function imgProxyLoader({
  src,
  width,
  height,
  quality,
}: {
  src: string;
  width: number;
  height: number;
  quality: number;
}) {
  if (
    src.startsWith("/_next/static/") ||
    src.startsWith("/static/") ||
    src.startsWith("/images/") ||
    src.startsWith("/assets/") ||
    src.startsWith("data:") ||
    (!src.startsWith("http") && src.startsWith("/"))
  ) {
    const safeSrc = src.replace(/ /g, "%20");
    const baseUrl =
      typeof window !== "undefined"
        ? window.location.origin
        : process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";
    return `${baseUrl}${safeSrc}`;
  }

  const baseUrl =
    typeof window !== "undefined"
      ? window.location.origin
      : process.env.NEXT_PUBLIC_APP_URL || "http://localhost:3000";

  const params = new URLSearchParams({
    url: src,
    w: width?.toString() || "200",
    h: height?.toString() || "0",
    q: quality?.toString() || "75",
  });

  return `${baseUrl}/api/img?${params.toString()}`;
}
