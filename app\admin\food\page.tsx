"use client";
import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import {
  Search,
  Plus,
  Filter,
  MoreHorizontal,
  Edit,
  Trash,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  Download,
  ArrowUpDown,
  Utensils,
  MapPin,
  Phone,
  CheckSquare,
  Users,
  MessageSquare,
  Copy,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { FaWhatsapp } from "react-icons/fa";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { useSession } from "next-auth/react";
import { Loader2, AlertCircle, MoreVertical, Trash2 } from "lucide-react";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import DeleteConfirmationDialog from "@/components/ui/delete-confirmation-dialog";
import { toast } from "sonner";
import { Switch } from "@/components/ui/switch";
import { EyeOff } from "lucide-react";
import CopyMenuModal from "@/components/ui/copy-menu-modal";
 
// Interface for restaurant data structure
interface Restaurant {
  id: string;
  restaurantId: string;
  name: string;
  phone: string;
  isWhatsappOnboardingCompleted: boolean;
  addedOn: string;
  is_only_for_listing?: boolean;
  isVisible?: boolean; // Add visibility property
}

// Function to fetch restaurants from API
const fetchRestaurants = async (token: string | undefined) => {
  const response = await fetch(
    `${process.env.NEXT_PUBLIC_FOOD_BACKEND_URL}/restaurants/onboarding-dashboard/restaurant-list`,
    {
      headers: {
        accept: "application/json",
        Authorization: token ? `Bearer ${token}` : "",
      },
    }
  );

  if (!response.ok) {
    throw new Error("Failed to fetch restaurants");
  }

  const data = await response.json();
  return data.data;
};

// Add function to toggle restaurant visibility
const toggleRestaurantVisibility = async (
  restaurantId: string,
  isVisible: boolean,
  token: string
) => {
  try {
    const response = await fetch(
      `${process.env.NEXT_PUBLIC_FOOD_BACKEND_URL}/restaurants/toggle-visibility/${restaurantId}`,
      {
        method: "PATCH",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify({ status: isVisible }),
      }
    );

    if (!response.ok) {
      throw new Error("Failed to update restaurant visibility");
    }

    return await response.json();
  } catch (error) {
    throw error;
  }
};

const RestaurantPage = () => {
  const [activeTab, setActiveTab] = useState("all");
  const [filter, setFilter] = useState("");
  const [listingTypeFilter, setListingTypeFilter] = useState<string>("all"); // Add new filter state
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [restaurantToDelete, setRestaurantToDelete] =
    useState<Restaurant | null>(null);
  const [copyMenuDialogOpen, setCopyMenuDialogOpen] = useState(false);
  const [selectedRestaurant, setSelectedRestaurant] =
    useState<Restaurant | null>(null);
  const [copyMenuModalOpen, setCopyMenuModalOpen] = useState(false);

  const { data: session } = useSession();
  const accessToken = session?.user.access_token;

  const queryClient = useQueryClient();

  // Delete restaurant mutation
  const { mutate: deleteRestaurant, isPending: isDeleting } = useMutation({
    mutationFn: async (restaurantId: string) => {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_FOOD_BACKEND_URL}/restaurants/delete/${restaurantId}`,
        {
          method: "DELETE",
          headers: {
            accept: "application/json",
            Authorization: accessToken ? `Bearer ${accessToken}` : "",
          },
        }
      );

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || "Failed to delete restaurant");
      }

      return response.json();
    },
    onSuccess: () => {
      toast.success("Restaurant deleted successfully");
      queryClient.invalidateQueries({ queryKey: ["restaurants"] });
      setDeleteDialogOpen(false);
      setRestaurantToDelete(null);
    },
    onError: (error) => {
      toast.error(`Error deleting restaurant: ${error.message}`);
      setDeleteDialogOpen(false);
    },
  });

  const handleDeleteClick = (restaurant: Restaurant) => {
    setRestaurantToDelete(restaurant);
    setDeleteDialogOpen(true);
  };

  const confirmDelete = () => {
    if (restaurantToDelete) {
      deleteRestaurant(restaurantToDelete.id);
    }
  };

  // Mutation for toggling visibility
  const { mutate: updateVisibility } = useMutation({
    mutationFn: async ({
      restaurantId,
      isVisible,
    }: {
      restaurantId: string;
      isVisible: boolean;
    }) => {
      return toggleRestaurantVisibility(
        restaurantId,
        isVisible,
        accessToken || ""
      );
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["restaurants"] });
      toast.success("Restaurant visibility updated successfully");
    },
    onError: (error) => {
      toast.error(`Error updating restaurant visibility: ${error.message}`);
    },
  });

  const handleVisibilityToggle = (
    restaurant: Restaurant,
    newVisibility: boolean
  ) => {
    updateVisibility({ restaurantId: restaurant.id, isVisible: newVisibility });
  };

  const handleCopyMenuClick = (restaurant: Restaurant) => {
    setSelectedRestaurant(restaurant);
    setCopyMenuModalOpen(true);
  };

  // Fetch restaurants with React Query
  const {
    data: restaurantsData,
    isLoading,
    error,
  } = useQuery({
    queryKey: ["restaurants", accessToken],
    queryFn: () => fetchRestaurants(accessToken),
    enabled: !!accessToken,
  });

  // Use fetched data if available, otherwise empty array
  const restaurants = restaurantsData || [];

  // Filter restaurants based on search term, active tab and listing type
  const filteredRestaurants = restaurants.filter((restaurant: Restaurant) => {
    const matchesSearch =
      filter === "" ||
      restaurant.name.toLowerCase().includes(filter.toLowerCase());

    // Filter by tab (onboarding status)
    let matchesTab = true;
    if (activeTab === "onboarded") {
      matchesTab = restaurant.isWhatsappOnboardingCompleted;
    } else if (activeTab === "pending") {
      matchesTab = !restaurant.isWhatsappOnboardingCompleted;
    }

    // Filter by listing type
    let matchesListingType = true;
    if (listingTypeFilter === "cravin_users") {
      matchesListingType = !restaurant.is_only_for_listing;
    } else if (listingTypeFilter === "only_listing") {
      matchesListingType = !!restaurant.is_only_for_listing;
    }

    return matchesSearch && matchesTab && matchesListingType;
  });

  // Pagination logic
  const totalPages = Math.ceil(filteredRestaurants.length / itemsPerPage);

  // Get current page data
  const paginatedRestaurants = filteredRestaurants.slice(
    (currentPage - 1) * itemsPerPage,
    currentPage * itemsPerPage
  );

  // Count for each category
  const onboardedCount = restaurants.filter(
    (restaurant: Restaurant) => restaurant.isWhatsappOnboardingCompleted
  ).length;
  const pendingCount = restaurants.filter(
    (restaurant: Restaurant) => !restaurant.isWhatsappOnboardingCompleted
  ).length;

  // Handle loading state
  if (isLoading) {
    return (
      <div className="container mx-auto py-8 flex items-center justify-center h-[50vh]">
        <div className="flex flex-col items-center space-y-4">
          <Loader2 className="h-8 w-8 animate-spin text-primary" />
          <p className="text-sm text-muted-foreground">
            Loading restaurants...
          </p>
        </div>
      </div>
    );
  }

  // Handle error state
  if (error) {
    return (
      <div className="container mx-auto py-8">
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>
            Failed to load restaurant data. Please try refreshing the page.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 space-y-8">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-6 pb-4 border-b">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">
            Restaurant Management
          </h1>
          <p className="text-muted-foreground mt-2 text-base">
            Manage and monitor all restaurants in the Cravin platform
          </p>
        </div>
        <div className="flex gap-3 self-end sm:self-auto">
          <Link href="/admin/food/onboarding">
            <Button className="gap-2 shadow-sm">
              <Plus size={16} />
              Onboard New Restaurant
            </Button>
          </Link>
        </div>
      </div>

      <Tabs defaultValue="all" className="w-full" onValueChange={setActiveTab}>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6 gap-4 pt-2">
          <TabsList className="w-full sm:w-auto bg-muted/60 p-1 rounded-lg">
            <TabsTrigger
              value="all"
              className="flex gap-2 rounded data-[state=active]:bg-background data-[state=active]:shadow-sm"
            >
              All
              <Badge variant="secondary" className="ml-1 bg-primary/10">
                {restaurants.length}
              </Badge>
            </TabsTrigger>
            <TabsTrigger
              value="onboarded"
              className="flex gap-2 rounded data-[state=active]:bg-background data-[state=active]:shadow-sm"
            >
              Onboarded
              <Badge
                variant="secondary"
                className="ml-1 bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400"
              >
                {onboardedCount}
              </Badge>
            </TabsTrigger>
            <TabsTrigger
              value="pending"
              className="flex gap-2 rounded data-[state=active]:bg-background data-[state=active]:shadow-sm"
            >
              Pending
              <Badge
                variant="secondary"
                className="ml-1 bg-amber-100 text-amber-700 dark:bg-amber-900/30 dark:text-amber-400"
              >
                {pendingCount}
              </Badge>
            </TabsTrigger>
          </TabsList>

          <div className="flex flex-col sm:flex-row gap-3 w-full sm:w-auto">
            <div className="relative flex-grow">
              <Search className="absolute left-3 top-2.5 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search restaurants..."
                className="pl-9 w-full border-muted bg-background focus-visible:ring-1"
                value={filter}
                onChange={(e) => setFilter(e.target.value)}
              />
            </div>

            {/* Add listing type filter */}
            <Select
              value={listingTypeFilter}
              onValueChange={(value) => {
                setListingTypeFilter(value);
                setCurrentPage(1); // Reset to first page when filter changes
              }}
            >
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="cravin_users">Cravin Users</SelectItem>
                <SelectItem value="only_listing">Only For Listings</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <TabsContent value="all" className="m-0 mt-0">
          <RestaurantsTable
            restaurants={paginatedRestaurants}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={filteredRestaurants.length}
            onPreviousPage={() =>
              setCurrentPage((prev) => Math.max(prev - 1, 1))
            }
            onNextPage={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            setFilter={setFilter}
            onDeleteClick={handleDeleteClick}
            setCurrentPage={setCurrentPage}
            setListingTypeFilter={setListingTypeFilter} // Pass down the setter function
            onVisibilityToggle={handleVisibilityToggle}
            onCopyMenuClick={handleCopyMenuClick}
          />
        </TabsContent>

        <TabsContent value="onboarded" className="m-0 mt-0">
          <RestaurantsTable
            restaurants={paginatedRestaurants}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={filteredRestaurants.length}
            onPreviousPage={() =>
              setCurrentPage((prev) => Math.max(prev - 1, 1))
            }
            onNextPage={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            setFilter={setFilter}
            onDeleteClick={handleDeleteClick}
            setCurrentPage={setCurrentPage}
            setListingTypeFilter={setListingTypeFilter} // Pass down the setter function
            onVisibilityToggle={handleVisibilityToggle}
            onCopyMenuClick={handleCopyMenuClick}
          />
        </TabsContent>

        <TabsContent value="pending" className="m-0 mt-0">
          <RestaurantsTable
            restaurants={paginatedRestaurants}
            currentPage={currentPage}
            totalPages={totalPages}
            totalItems={filteredRestaurants.length}
            onPreviousPage={() =>
              setCurrentPage((prev) => Math.max(prev - 1, 1))
            }
            onNextPage={() =>
              setCurrentPage((prev) => Math.min(prev + 1, totalPages))
            }
            setFilter={setFilter}
            onDeleteClick={handleDeleteClick}
            setCurrentPage={setCurrentPage}
            setListingTypeFilter={setListingTypeFilter} // Pass down the setter function
            onVisibilityToggle={handleVisibilityToggle}
            onCopyMenuClick={handleCopyMenuClick}
          />
        </TabsContent>
      </Tabs>

      <DeleteConfirmationDialog
        open={deleteDialogOpen}
        onOpenChange={setDeleteDialogOpen}
        title="Delete Restaurant"
        description={`This restaurant will be deleted, along with all of its Branches, Menus`}
        onConfirm={confirmDelete}
        isLoading={isDeleting}
        confirmText="delete my restaurant"
      />

      {selectedRestaurant && (
        <CopyMenuModal
          isOpen={copyMenuModalOpen}
          onClose={() => setCopyMenuModalOpen(false)}
          product={selectedRestaurant}
          accessToken={accessToken || ""}
          productType="food"
        />
      )}
    </div>
  );
};

// Separated table component for better organization with pagination
const RestaurantsTable = ({
  restaurants,
  currentPage,
  totalPages,
  totalItems,
  onPreviousPage,
  onNextPage,
  setFilter,
  onDeleteClick,
  setCurrentPage,
  setListingTypeFilter,
  onVisibilityToggle,
  onCopyMenuClick,
}: {
  restaurants: Restaurant[];
  currentPage: number;
  totalPages: number;
  totalItems: number;
  onPreviousPage: () => void;
  onNextPage: () => void;
  setFilter: (value: string) => void;
  onDeleteClick: (restaurant: Restaurant) => void;
  setCurrentPage: (page: number) => void;
  setListingTypeFilter: (value: string) => void;
  onVisibilityToggle: (restaurant: Restaurant, isVisible: boolean) => void;
  onCopyMenuClick: (restaurant: Restaurant) => void;
}) => {
  return (
    <Card className="shadow-sm border-muted overflow-hidden">
      <CardContent className="p-0">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-muted/30 hover:bg-muted/30">
                <TableHead className="w-[250px] font-semibold">
                  <div className="flex items-center gap-1">Restaurant Name</div>
                </TableHead>

                <TableHead className="font-semibold">
                  WhatsApp Onboarding
                </TableHead>
                <TableHead className="font-semibold">
                  <div className="flex items-center gap-1">Added On</div>
                </TableHead>
                <TableHead className="font-semibold text-center">
                Access Toggle
                </TableHead>
                <TableHead className="text-right font-semibold">
                  Actions
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {restaurants.length === 0 ? (
                <TableRow>
                  <TableCell
                    colSpan={8}
                    className="text-center py-12 text-muted-foreground h-[200px]"
                  >
                    <div className="flex flex-col items-center gap-2">
                      <Search className="h-8 w-8 text-muted-foreground/60" />
                      <p>No restaurants found matching your criteria</p>
                      <Button
                        variant="link"
                        className="mt-2"
                        onClick={() => {
                          setFilter("");
                          setCurrentPage(1); // Reset to first page when filter is cleared
                          setListingTypeFilter("all"); // Reset listing type filter
                        }}
                      >
                        Clear filters
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ) : (
                restaurants.map((restaurant) => (
                  <TableRow key={restaurant.id} className="group">
                    <TableCell className="font-medium">
                      <div className="flex flex-col">
                        <span className="text-base">{restaurant.name}</span>
                        <span className="text-xs text-muted-foreground mt-1">
                          ID: {restaurant.restaurantId}
                        </span>
                      </div>
                    </TableCell>

                    <TableCell>
                      {restaurant.isWhatsappOnboardingCompleted ? (
                        <div className="flex items-center gap-2">
                          <Badge
                            variant="outline"
                            className="bg-green-50 text-green-700 border-green-200 dark:bg-green-950/30 dark:text-green-400 dark:border-green-800 flex items-center gap-1.5 py-1.5"
                          >
                            <CheckCircle className="h-3.5 w-3.5" />
                            <span>Completed</span>
                          </Badge>
                        </div>
                      ) : (
                        <div className="flex items-center gap-2">
                          <Badge
                            variant="outline"
                            className="bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-950/30 dark:text-amber-400 dark:border-amber-800 flex items-center gap-1.5 py-1.5"
                          >
                            <Clock className="h-3.5 w-3.5" />
                            <span>Pending</span>
                          </Badge>
                        </div>
                      )}
                    </TableCell>
                    <TableCell>
                      {new Date(restaurant.addedOn).toLocaleDateString(
                        "en-US",
                        {
                          year: "numeric",
                          month: "short",
                          day: "numeric",
                        }
                      )}
                    </TableCell>
                    <TableCell className="text-center">
                      <div className="flex items-center justify-center gap-2">
                        <span className="text-sm text-muted-foreground mr-2">
                          {restaurant.isVisible !== false ? (
                            <Eye className="h-4 w-4 text-green-600" />
                          ) : (
                            <EyeOff className="h-4 w-4 text-gray-400" />
                          )}
                        </span>
                        <Switch
                          checked={restaurant.isVisible !== false}
                          onCheckedChange={(checked) =>
                            onVisibilityToggle(restaurant, checked)
                          }
                          className="data-[state=checked]:bg-green-500"
                        />
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <div className="flex items-center justify-end gap-2">
                        {!restaurant.isWhatsappOnboardingCompleted ? (
                          <Link
                            href={`/admin/onboard-whatsapp/food/${restaurant.id}`}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 shadow-sm bg-white hover:bg-green-50 text-green-600 border-green-200 hover:border-green-300"
                            >
                              <FaWhatsapp className="h-3.5 w-3.5 mr-1" />
                              Onboard WhatsApp
                            </Button>
                          </Link>
                        ) : (
                          <Link
                            href={`/admin/onboard-whatsapp/profile/food/${restaurant.id}`}
                          >
                            <Button
                              variant="outline"
                              size="sm"
                              className="h-8 shadow-sm bg-white hover:bg-green-50 text-green-600 border-green-200 hover:border-green-300"
                            >
                              <FaWhatsapp className="h-3.5 w-3.5 mr-1" />
                              Edit WhatsApp
                            </Button>
                          </Link>
                        )}

                        <Link
                          href={`/admin/food/onboarding/listing-details?id=${restaurant.id}`}
                        >
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 shadow-sm bg-white hover:bg-orange-50 text-orange-600 border-orange-200 hover:border-orange-300"
                          >
                            <Utensils className="h-3.5 w-3.5 mr-1" />
                            Branch Listings
                          </Button>
                        </Link>

                        <Link
                          href={`/admin/food/onboarding?id=${restaurant.id}`}
                        >
                          <Button
                            variant="outline"
                            size="sm"
                            className="h-8 shadow-sm hover:bg-muted"
                          >
                            <Edit className="h-3.5 w-3.5 mr-1" />
                            Edit Details
                          </Button>
                        </Link>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button
                              variant="ghost"
                              size="sm"
                              className="h-8 w-8 p-0"
                            >
                              <MoreVertical className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              className="flex items-center cursor-pointer"
                              onClick={() => onCopyMenuClick(restaurant)}
                            >
                              <Copy className="h-4 w-4 mr-2" />
                              Copy Menu to Branch
                            </DropdownMenuItem>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem
                              className="text-destructive focus:text-destructive flex items-center cursor-pointer"
                              onClick={() => onDeleteClick(restaurant)}
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete Restaurant
                            </DropdownMenuItem>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
        <div className="flex items-center justify-between px-6 py-4 border-t">
          <div className="text-sm text-muted-foreground">
            Showing <strong>{restaurants.length}</strong> of{" "}
            <strong>{totalItems}</strong> restaurants
          </div>
          <div className="flex gap-1">
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage <= 1}
              onClick={onPreviousPage}
              className="h-8 px-3"
            >
              Previous
            </Button>
            {totalPages > 0 && (
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-4 bg-primary text-primary-foreground hover:bg-primary/90 hover:text-primary-foreground"
              >
                {currentPage}
              </Button>
            )}
            {totalPages > 1 && currentPage < totalPages && (
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-4"
                onClick={() => onNextPage()}
              >
                {currentPage + 1}
              </Button>
            )}
            {totalPages > 2 && currentPage < totalPages - 1 && (
              <Button
                variant="outline"
                size="sm"
                className="h-8 px-4"
                onClick={() => onNextPage()}
              >
                {currentPage + 2}
              </Button>
            )}
            <Button
              variant="outline"
              size="sm"
              disabled={currentPage >= totalPages || totalPages === 0}
              onClick={onNextPage}
              className="h-8 px-3"
            >
              Next
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default RestaurantPage;
