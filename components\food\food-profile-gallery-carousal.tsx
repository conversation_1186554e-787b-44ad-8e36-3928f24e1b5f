import * as React from "react";
import Image from "next/image";
import testlistingimg from "@/public/Cravin_food_placeholder.png";
import {
  Carousel,
  CarouselContent,
  CarouselItem,
  CarouselNext,
  CarouselPrevious,
  type CarouselApi,
} from "@/components/ui/carousel";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";

export function FoodProfileGalleryCarousal({
  profileData,
  position,
}: {
  profileData: any;
  position: number;
}) {
  const [api, setApi] = React.useState<CarouselApi>();
  const [current, setCurrent] = React.useState(0);
  const [count, setCount] = React.useState(0);

  React.useEffect(() => {
    if (!api) {
      return;
    }

    api.scrollTo(position);
    setCount(api.scrollSnapList().length);
    setCurrent(api.selectedScrollSnap() + 1);

    api.on("select", () => {
      setCurrent(api.selectedScrollSnap() + 1);
    });
  }, [api]);
  return (
    <Dialog>
      <DialogTrigger asChild>
        {position === 1 || position === 2 || position === 3 ? (
          <div className="relative w-[200px] h-full rounded-lg overflow-hidden cursor-pointer">
            <Image
              alt="merchantlogo"
              src={
                profileData?.imageGallery &&
                profileData?.imageGallery?.length > 0 &&
                profileData?.imageGallery?.length >= position
                  ? profileData?.imageGallery[position - 1]
                  : testlistingimg
              }
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              style={{
                objectFit: "cover",
              }}
            />
          </div>
        ) : (
          <div className="relative w-full h-[206px] md:h-[370px] rounded-lg overflow-hidden cursor-pointer">
            <Image
              alt="merchantlogo"
              src={
                profileData?.listingImage &&
                profileData?.listingImage?.length > 0
                  ? profileData?.listingImage
                  : testlistingimg
              }
              fill
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 85vw, 65vw"
              style={{
                objectFit: "cover",
              }}
            />
          </div>
        )}
      </DialogTrigger>
      <DialogContent
        aria-describedby={undefined}
        className="flex flex-col justify-center items-center max-w-[80dvw] max-h-[95dvh] bg-black"
      >
        <DialogHeader className="hidden">
          <DialogTitle className="text-xl font-medium">Title</DialogTitle>
        </DialogHeader>
        <Carousel setApi={setApi} className="w-[70%] h-full">
          <CarouselContent className="w-full h-full">
            {profileData.listingImage &&
              profileData.listingImage.length > 0 && (
                <CarouselItem
                  className="w-full h-[80dvh] flex justify-center items-center bg-black"
                  key={0}
                >
                  <div className="relative w-full h-full rounded-lg overflow-hidden">
                    <Image
                      alt="itemimg"
                      src={
                        profileData.listingImage &&
                        profileData.listingImage.length > 0
                          ? profileData.listingImage
                          : testlistingimg
                      }
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                      style={{
                        objectFit: "contain",
                      }}
                    />
                  </div>
                </CarouselItem>
              )}

            {profileData.imageGallery &&
              profileData.imageGallery.length > 0 &&
              profileData.imageGallery.map((galleryitem: any, index: any) => (
                <CarouselItem
                  className="w-full h-[80dvh] flex justify-center items-center bg-black"
                  key={index + 1}
                >
                  <div className="relative w-full h-full rounded-lg overflow-hidden">
                    <Image
                      alt="itemimg"
                      src={
                        galleryitem.length > 0 ? galleryitem : testlistingimg
                      }
                      fill
                      sizes="(max-width: 768px) 100vw, (max-width: 1200px) 90vw, 80vw"
                      style={{
                        objectFit: "contain",
                      }}
                    />
                  </div>
                </CarouselItem>
              ))}
          </CarouselContent>
          <CarouselPrevious />
          <CarouselNext />
        </Carousel>
        <div className="text-white text-center text-sm text-muted-foreground h-fit">
          Slide {current} of {count}
        </div>
      </DialogContent>
    </Dialog>
  );
}
