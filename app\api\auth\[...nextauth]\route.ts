import NextAuth from "next-auth";
import Cred<PERSON><PERSON><PERSON>rovider from "next-auth/providers/credentials";
import { DefaultSession } from "next-auth";
import { authOptions } from "@/lib/auth";

declare module "next-auth" {
  interface Session extends DefaultSession {
    user: {
      id: string;
      name: string;
      user_type: string;
      access_token: string;
    } & DefaultSession["user"]
  }
  interface JWT {
    id: string;
    name: string;
    user_type: string;
    accessToken: string;
  }
}


const handler = NextAuth(authOptions);

export { handler as GET, handler as POST };
