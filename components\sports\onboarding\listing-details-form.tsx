"use client";

import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  X,
  Plus,
  Upload,
  Loader2,
  Info,
  Check,
  MapPin,
  ChevronDown,
} from "lucide-react";
import { useQuery } from "@tanstack/react-query";

import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Switch } from "@/components/ui/switch";
import { Textarea } from "@/components/ui/textarea";
import { cn } from "@/lib/utils";
import CardIconSet from "@/components/sports/card-icon-set";
import CategoryNameSet from "@/components/sports/cateory-name";

import {
  listingFormSchema,
  type ListingFormValues,
  type FacilityDetails,
} from "./listing-details-types";
import { getDefaultValues } from "./listing-details-utils";
import { useSession } from "next-auth/react";
import { toast } from "sonner";
import Image from "next/image";
import { redirect } from "next/navigation";

interface ListingDetailsFormProps {
  clubId: string;
}

// Facility categories data
const FACILITY_CATEGORIES = [
  { categoryName: "Badminton" },
  { categoryName: "Nets" },
  { categoryName: "Bowling" },
  { categoryName: "Football" },
  { categoryName: "Basketball" },
  { categoryName: "Cricket" },
  { categoryName: "Volleyball" },
  { categoryName: "Throwball" },
  { categoryName: "Tennis" },
  { categoryName: "Futsal" },
  { categoryName: "Table Tennis" },
  { categoryName: "Golf" },
  { categoryName: "Squash" },
  { categoryName: "Padel" },
];

// Helper function for image upload
const uploadImage = async (file: File, clubId: string, token: string) => {
  const formData = new FormData();
  formData.append("file", file);

  const response = await fetch(
    `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/common/upload-images/${clubId}`,
    {
      method: "POST",
      body: formData,
      headers: {
        Authorization: `Bearer ${token}`,
      },
    }
  );

  if (!response.ok) {
    toast.error("Failed to upload image");
    throw new Error("Failed to upload image");
  }

  const jsonResponse = await response.json();
  return jsonResponse.message;
};

export default function ListingDetailsForm({
  clubId,
}: ListingDetailsFormProps) {
  const session = useSession();
  const token = session.data?.user?.access_token;
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [newTag, setNewTag] = useState("");
  const [newAmenity, setNewAmenity] = useState("");
  const [newPhoneNumber, setNewPhoneNumber] = useState("");
  const [uploadingImages, setUploadingImages] = useState(false);
  const [uploadingPricingImages, setUploadingPricingImages] = useState(false);
  const [isDeletingImage, setIsDeletingImage] = useState(false);
  const [firstTimeLoading, setFirstTimeLoading] = useState(false);
  const [newPricingName, setNewPricingName] = useState("");
  const [currentPricingIndex, setCurrentPricingIndex] = useState<number | null>(null);

  // Add handler for social links
  const handleSocialLinkChange = (platform: string, value: string) => {
    const currentLinks = form.getValues("social_links") || {};
    form.setValue("social_links", {
      ...currentLinks,
      [platform]: value,
    });
  };

  const { data: clubData, isLoading } = useQuery({
    queryKey: ["clubData", clubId],
    queryFn: async () => {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/clubs/landing-page-listing-details/${clubId}`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );
      return response.json();
    },
    enabled: !firstTimeLoading,
  });

  const form = useForm<ListingFormValues>({
    resolver: zodResolver(listingFormSchema) as any,
    defaultValues: clubData?.data || getDefaultValues(),
  });

  // Helper function to parse branch_pricings from JSON strings
  const parseBranchPricings = (pricings: any) => {
    if (!pricings) return [];

    if (Array.isArray(pricings)) {
      return pricings.map((pricingItem: any) => {
        // If it's a JSON string, try to parse it
        if (typeof pricingItem === 'string') {
          try {
            const parsedPricing = JSON.parse(pricingItem);
            return {
              pricingName: parsedPricing.pricingName || "Pricing",
              pricingImages: Array.isArray(parsedPricing.pricingImages)
                ? parsedPricing.pricingImages.filter((img: any) =>
                    typeof img === 'string' && img.startsWith('http')
                  )
                : []
            };
          } catch (e) {
            // If it's a string but not valid JSON, check if it's a URL
            if (typeof pricingItem === 'string' && pricingItem.startsWith('http')) {
              return {
                pricingName: "Pricing",
                pricingImages: [pricingItem]
              };
            }
            // If parsing fails, return empty pricing
            return {
              pricingName: "Pricing",
              pricingImages: []
            };
          }
        }
        // If it's already an object, validate it
        else if (typeof pricingItem === 'object' && pricingItem !== null) {
          return {
            pricingName: pricingItem.pricingName || "Pricing",
            pricingImages: Array.isArray(pricingItem.pricingImages)
              ? pricingItem.pricingImages.filter((img: any) =>
                  typeof img === 'string' && img.startsWith('http')
                )
              : []
          };
        }
        // Default fallback
        return {
          pricingName: "Pricing",
          pricingImages: []
        };
      });
    }
    return [];
  };

  useEffect(() => {
    if (clubData?.data && !firstTimeLoading) {
      // Parse branch_pricings from JSON if needed
      const formattedData = {
        ...clubData.data,
        branch_pricings: parseBranchPricings(clubData.data.branch_pricings)
      };

      form.reset(formattedData);
      setFirstTimeLoading(true);
    }
  }, [clubData?.data, firstTimeLoading, form]);

  const onSubmit = async (data: ListingFormValues) => {
    setIsSubmitting(true);
    try {
      // Ensure branch_pricings is properly formatted
      const formattedData = {
        ...data,
        // Make sure branch_pricings is properly formatted for API
        branch_pricings: data.branch_pricings || []
      };

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/clubs/landing-page-listing/${clubId}`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
          },
          body: JSON.stringify(formattedData),
        }
      );

      if (!response.ok) throw new Error("Failed to save venue details");
      toast.success("Venue details saved successfully");
    } catch (error) {
      toast.error("Failed to save venue details");
      console.error(error);
    } finally {
      setIsSubmitting(false);
    }
  };

  const deleteImage = async (imageUrl: string): Promise<boolean> => {
    setIsDeletingImage(true);
    try {
      const encodedUrl = encodeURIComponent(imageUrl);
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL}/common/delete-image/${encodedUrl}`,
        {
          method: "DELETE",
          headers: {
            Authorization: `Bearer ${token}`,
          },
        }
      );

      if (!response.ok) {
        throw new Error("Failed to delete image");
      }
      return true;
    } catch (error) {
      console.error("Error deleting image:", error);
      toast.error("Failed to delete image");
      return false;
    } finally {
      setIsDeletingImage(false);
    }
  };

  const handleImageUploadChange = async (
    e: React.ChangeEvent<HTMLInputElement>,
    isGallery: boolean
  ) => {
    try {
      if (!token) {
        toast.error("Session expired. Please log in again.");
        redirect("/auth/login");
      }
      setUploadingImages(true);
      const files = e.target.files;
      if (!files) return;

      if (isGallery) {
        const uploadPromises = Array.from(files).map((file) =>
          uploadImage(file, clubId, token)
        );
        const uploadedUrls = await Promise.all(uploadPromises);

        // Get current gallery images (or initialize as empty array if undefined)
        const currentGallery = form.getValues("image_gallery") || [];

        // Update the form with new array that combines existing and new images
        form.setValue("image_gallery", [...currentGallery, ...uploadedUrls]);

        // Trigger form validation after setting the value
        form.trigger("image_gallery");

        // Show success message
        toast.success(`${uploadedUrls.length} image(s) uploaded successfully`);
      } else {
        const uploadedUrl = await uploadImage(files[0], clubId, token);
        form.setValue("listing_image", uploadedUrl);
        form.trigger("listing_image");
        toast.success("Main image uploaded successfully");
      }
    } catch (error) {
      console.error("Image upload failed:", error);
      toast.error("Failed to upload image(s). Please try again.");
    } finally {
      setUploadingImages(false);

      // Clear the file input to allow uploading the same file again if needed
      const fileInput = isGallery
        ? (document.getElementById("gallery-images") as HTMLInputElement)
        : (document.getElementById("listing-image") as HTMLInputElement);

      if (fileInput) fileInput.value = "";
    }
  };

  // Add a separate handler for pricing image uploads
  const handlePricingImageUploadChange = async (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    try {
      if (!token) {
        toast.error("Session expired. Please log in again.");
        redirect("/auth/login");
      }

      if (currentPricingIndex === null) {
        toast.error("Please select or create a pricing section first");
        return;
      }

      setUploadingPricingImages(true);
      const files = e.target.files;
      if (!files) return;

      const uploadPromises = Array.from(files).map((file) =>
        uploadImage(file, clubId, token)
      );
      const uploadedUrls = await Promise.all(uploadPromises);

      // Get current pricing sections
      const currentPricings = [...(form.getValues("branch_pricings") || [])];

      // Add images to the selected pricing section
      if (currentPricingIndex !== null && currentPricings[currentPricingIndex]) {
        const existingPricingImages = currentPricings[currentPricingIndex].pricingImages || [];

        // Filter out any non-string or non-URL values
        const validUploadedUrls = uploadedUrls.filter(url =>
          typeof url === 'string' && url.startsWith('http')
        );

        currentPricings[currentPricingIndex] = {
          ...currentPricings[currentPricingIndex],
          pricingName: currentPricings[currentPricingIndex].pricingName || 'Pricing',
          pricingImages: [
            ...existingPricingImages,
            ...validUploadedUrls
          ]
        };
      }

      // Update form value
      form.setValue("branch_pricings", currentPricings);
      toast.success(`${uploadedUrls.length} pricing image(s) uploaded successfully`);
    } catch (error) {
      console.error("Pricing image upload failed:", error);
      toast.error("Failed to upload pricing images");
    } finally {
      setUploadingPricingImages(false);

      // Clear the file input
      const fileInput = document.getElementById("pricing-images") as HTMLInputElement;
      if (fileInput) fileInput.value = "";
    }
  };

  // Add a function to create a new pricing section
  const handleAddPricing = () => {
    if (!newPricingName || !newPricingName.trim()) {
      toast.error("Please enter a pricing name");
      return;
    }

    const currentPricings = [...(form.getValues("branch_pricings") || [])];
    currentPricings.push({
      pricingName: newPricingName.trim(),
      pricingImages: []
    });

    form.setValue("branch_pricings", currentPricings);
    setNewPricingName("");
    setCurrentPricingIndex(currentPricings.length - 1);
  };

  // Enhanced multi-value input renderer with improved UI
  const renderEnhancedMultiInput = (
    value: string,
    setValue: (val: string) => void,
    placeholder: string,
    items: string[],
    onAdd: (item: string) => void,
    onRemove: (index: number) => void
  ) => {
    const handleAdd = () => {
      if (!value.trim()) return;
      onAdd(value.trim());
      setValue("");
    };

    const handleKeyDown = (e: React.KeyboardEvent) => {
      if (e.key === "Enter" && value.trim()) {
        e.preventDefault();
        handleAdd();
      }
    };

    return (
      <div className="space-y-2">
        <div className="flex gap-2 max-w-md">
          <Input
            value={value}
            onChange={(e) => {
              setValue(e.target.value);
            }}
            placeholder={placeholder}
            className="focus:ring-1 focus:ring-primary/20"
            onKeyDown={handleKeyDown}
          />
          <Button
            type="button"
            variant="secondary"
            size="sm"
            onClick={handleAdd}
            disabled={!value.trim()}
            className="shrink-0"
          >
            <Plus className="h-4 w-4 mr-1" />
            Add
          </Button>
        </div>

        {items?.length > 0 && (
          <div className="max-w-3xl">
            <div className="flex flex-wrap gap-2">
              {items?.map((item, index) => (
                <div
                  key={index}
                  className="flex items-center gap-1 bg-secondary/20 text-secondary-foreground px-2 py-1 rounded-md"
                >
                  <span className="text-sm">{item}</span>
                  <button
                    type="button"
                    onClick={() => onRemove(index)}
                    className="text-muted-foreground hover:text-destructive"
                  >
                    <X className="h-3.5 w-3.5" />
                  </button>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    );
  };

  if (isLoading) {
    return (
      <div className="bg-white p-6 md:p-8 rounded-xl shadow-lg border">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header Skeleton */}
          <div className="bg-gray-100 p-6 rounded-xl animate-pulse">
            <div className="flex items-start gap-4">
              <div className="w-6 h-6 bg-gray-200 rounded-full" />
              <div className="space-y-3 flex-1">
                <div className="h-6 bg-gray-200 rounded w-1/3" />
                <div className="h-4 bg-gray-200 rounded w-2/3" />
              </div>
            </div>
          </div>

          {/* Basic Information Skeleton */}
          <div className="space-y-6">
            <div className="border-b pb-2">
              <div className="h-6 bg-gray-200 rounded w-1/4 mb-2" />
              <div className="h-4 bg-gray-200 rounded w-1/3" />
            </div>
            <div className="grid md:grid-cols-2 gap-6">
              {[1, 2].map((i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4" />
                  <div className="h-10 bg-gray-100 rounded" />
                </div>
              ))}
            </div>
          </div>

          {/* Details Skeleton */}
          <div className="space-y-6">
            <div className="border-b pb-2">
              <div className="h-6 bg-gray-200 rounded w-1/4 mb-2" />
              <div className="h-4 bg-gray-200 rounded w-1/3" />
            </div>
            <div className="space-y-4">
              {[1, 2, 3].map((i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4" />
                  <div className="h-24 bg-gray-100 rounded" />
                </div>
              ))}
            </div>
          </div>

          {/* Images Section Skeleton */}
          <div className="space-y-6">
            <div className="border-b pb-2">
              <div className="h-6 bg-gray-200 rounded w-1/4 mb-2" />
              <div className="h-4 bg-gray-200 rounded w-1/3" />
            </div>
            <div className="grid md:grid-cols-2 gap-8">
              {[1, 2].map((i) => (
                <div key={i} className="space-y-4">
                  <div className="h-4 bg-gray-200 rounded w-1/4" />
                  <div className="h-10 bg-gray-100 rounded" />
                  <div className="aspect-video bg-gray-100 rounded" />
                </div>
              ))}
            </div>
          </div>

          {/* Contact Info Skeleton */}
          <div className="space-y-6">
            <div className="border-b pb-2">
              <div className="h-6 bg-gray-200 rounded w-1/4 mb-2" />
              <div className="h-4 bg-gray-200 rounded w-1/3" />
            </div>
            <div className="grid md:grid-cols-3 gap-6">
              {[1, 2, 3].map((i) => (
                <div key={i} className="space-y-2">
                  <div className="h-4 bg-gray-200 rounded w-1/4" />
                  <div className="h-10 bg-gray-100 rounded" />
                </div>
              ))}
            </div>
          </div>

          {/* Submit Button Skeleton */}
          <div className="flex justify-end pt-6 border-t">
            <div className="h-11 bg-gray-200 rounded w-[200px]" />
          </div>
        </div>
      </div>
    );
  }

  return (
    <Form {...form}>
      <div className="bg-white p-6 md:p-8 rounded-xl shadow-lg border">
        <div className="max-w-4xl mx-auto space-y-8">
          {/* Header */}
          <div className="bg-gradient-to-r from-primary/5 to-primary/10 p-6 rounded-xl border border-primary/20">
            <div className="flex items-start gap-4">
              <Info className="text-primary mt-1 h-6 w-6 flex-shrink-0" />
              <div className="space-y-1">
                <h2 className="text-xl font-semibold text-primary">
                  Configure Club Details - ({clubId})
                </h2>
                <p className="text-sm text-muted-foreground">
                  Provide comprehensive details about your venue to help
                  customers discover and choose your services
                </p>
              </div>
            </div>
          </div>

          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-10">
            {/* Basic Information */}
            <section className="space-y-6">
              <div className="border-b pb-2">
                <h3 className="text-lg font-semibold">Basic Information</h3>
                <p className="text-sm text-muted-foreground">
                  Provide essential details about your venue
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-6">
                <FormField
                  control={form.control}
                  name="club_emirate"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Club Emirate</FormLabel>
                      <FormControl>
                        <Input
                          {...field}
                          placeholder="Enter emirate location"
                          className="focus:ring-2 focus:ring-primary/30"
                        />
                      </FormControl>
                      <FormDescription>
                        The emirate where your club is located
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="show_in_landing_page"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4 hover:bg-secondary/5 transition-colors">
                      <div className="space-y-0.5">
                        <FormLabel>Show in Landing Page</FormLabel>
                        <FormDescription>
                          Display this venue on the landing page
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>
            </section>

            {/* Details & Features */}
            <section className="space-y-6">
              <div className="border-b pb-2">
                <h3 className="text-lg font-semibold">Club Details</h3>
                <p className="text-sm text-muted-foreground">
                  Help customers understand your venue
                </p>
              </div>

              <div className="grid gap-6">
                <FormField
                  control={form.control}
                  name="about_venue"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>About Venue</FormLabel>
                      <FormControl>
                        <Textarea
                          {...field}
                          placeholder="Describe your venue..."
                          className="min-h-[100px] resize-none"
                        />
                      </FormControl>
                      <FormDescription>
                        Provide a detailed description of your venue
                      </FormDescription>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="club_tags"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Club Tags</FormLabel>
                      <FormDescription className="mb-2">
                        Add tags to help customers find your venue
                      </FormDescription>
                      {renderEnhancedMultiInput(
                        newTag,
                        setNewTag,
                        "Add a tag and press Enter",
                        field.value || [],
                        (tag) => field.onChange([...(field.value || []), tag]),
                        (index) =>
                          field.onChange(
                            field.value?.filter((_, i) => i !== index) || []
                          )
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="amenities"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Amenities</FormLabel>
                      <FormDescription className="mb-2">
                        List the amenities and features available at your venue
                      </FormDescription>
                      {renderEnhancedMultiInput(
                        newAmenity,
                        setNewAmenity,
                        "Add an amenity and press Enter",
                        field.value || [],
                        (amenity) =>
                          field.onChange([...(field.value || []), amenity]),
                        (index) =>
                          field.onChange(
                            field.value?.filter((_, i) => i !== index) || []
                          )
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="facility_details"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Facility Details</FormLabel>
                      <FormDescription className="mb-2">
                        Select the facility categories available at your venue
                      </FormDescription>
                      <div>
                        <div className="flex flex-wrap gap-3">
                          {FACILITY_CATEGORIES.map((category) => {
                            const isSelected = field.value?.some(
                              (f) =>
                                f.facilityCategory === category.categoryName
                            );

                            return (
                              <div
                                key={category.categoryName}
                                className={cn(
                                  "flex items-center gap-2 border rounded-full pl-2 pr-1 py-1 cursor-pointer transition-colors",
                                  isSelected
                                    ? "bg-primary/10 border-primary"
                                    : "bg-background hover:bg-secondary/5"
                                )}
                                onClick={() => {
                                  if (isSelected) {
                                    // Remove this category
                                    field.onChange(
                                      field.value?.filter(
                                        (f) =>
                                          f.facilityCategory !==
                                          category.categoryName
                                      ) || []
                                    );
                                  } else {
                                    // Add this category
                                    const newFacility: FacilityDetails = {
                                      facilityName: category.categoryName,
                                      facilityCategory: category.categoryName,
                                    };
                                    field.onChange([
                                      ...(field.value || []),
                                      newFacility,
                                    ]);
                                  }
                                }}
                              >
                                <div className="flex items-center gap-1.5">
                                  <div className="w-5 h-5">
                                    <CardIconSet text={category.categoryName} />
                                  </div>
                                  <CategoryNameSet
                                    text={category.categoryName}
                                  />
                                </div>
                                {isSelected ? (
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    className="h-5 w-5 p-0 rounded-full hover:bg-secondary/10"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      field.onChange(
                                        field.value?.filter(
                                          (f) =>
                                            f.facilityCategory !==
                                            category.categoryName
                                        ) || []
                                      );
                                    }}
                                  >
                                    <X className="h-3 w-3" />
                                  </Button>
                                ) : (
                                  <Button
                                    type="button"
                                    variant="ghost"
                                    size="sm"
                                    className="h-5 w-5 p-0 rounded-full hover:bg-secondary/10"
                                    onClick={(e) => {
                                      e.stopPropagation();
                                      const newFacility: FacilityDetails = {
                                        facilityName: category.categoryName,
                                        facilityCategory: category.categoryName,
                                      };
                                      field.onChange([
                                        ...(field.value || []),
                                        newFacility,
                                      ]);
                                    }}
                                  >
                                    <Plus className="h-3 w-3" />
                                  </Button>
                                )}
                              </div>
                            );
                          })}
                        </div>


                      </div>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </section>

            {/* Images */}
            <section className="space-y-6">
              <div className="border-b pb-2">
                <h3 className="text-lg font-semibold">Media Gallery</h3>
                <p className="text-sm text-muted-foreground">
                  Showcase your venue with high-quality images
                </p>
              </div>

              <div className="grid md:grid-cols-2 gap-8">
                <FormField
                  control={form.control}
                  name="listing_image"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Listing Image</FormLabel>
                      <FormDescription className="mb-4">
                        Main image displayed in search results
                      </FormDescription>
                      <FormControl>
                        <div className="space-y-4">
                          <div>
                            <Input
                              type="file"
                              accept="image/*"
                              onChange={(e) =>
                                handleImageUploadChange(e, false)
                              }
                              className="hidden"
                              id="listing-image"
                            />
                            <Button
                              type="button"
                              variant="secondary"
                              onClick={() =>
                                document
                                  .getElementById("listing-image")
                                  ?.click()
                              }
                              disabled={uploadingImages}
                              className="w-full"
                            >
                              {uploadingImages ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              ) : (
                                <Upload className="h-4 w-4 mr-2" />
                              )}
                              Upload Main Image
                            </Button>
                          </div>
                          {field.value && (
                            <div className="relative aspect-video w-full overflow-hidden rounded-lg border">
                              <Image
                                src={field.value}
                                alt="Main listing"
                                fill
                                className="object-cover"
                              />
                              <button
                                type="button"
                                disabled={isDeletingImage}
                                onClick={async () => {
                                  const success = await deleteImage(
                                    field.value
                                  );
                                  if (success) field.onChange("");
                                }}
                                className="absolute top-2 right-2 bg-destructive text-white p-1.5 rounded-full shadow-lg hover:bg-destructive/90 transition-colors"
                              >
                                {isDeletingImage ? (
                                  <Loader2 className="h-4 w-4 animate-spin" />
                                ) : (
                                  <X className="h-4 w-4" />
                                )}
                              </button>
                            </div>
                          )}
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="image_gallery"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Image Gallery</FormLabel>
                      <FormDescription className="mb-4">
                        Additional images of your venue
                      </FormDescription>
                      <FormControl>
                        <div className="space-y-4">
                          <div>
                            <Input
                              type="file"
                              accept="image/*"
                              multiple
                              onChange={(e) => handleImageUploadChange(e, true)}
                              className="hidden"
                              id="gallery-images"
                            />
                            <Button
                              type="button"
                              variant="secondary"
                              onClick={() =>
                                document
                                  .getElementById("gallery-images")
                                  ?.click()
                              }
                              disabled={uploadingImages}
                              className="w-full"
                            >
                              {uploadingImages ? (
                                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                              ) : (
                                <Upload className="h-4 w-4 mr-2" />
                              )}
                              Upload Gallery Images
                            </Button>
                          </div>
                          <div className="grid grid-cols-2 gap-4">
                            {field?.value?.map((image, index) => (
                              <div
                                key={index}
                                className="relative aspect-video overflow-hidden rounded-lg border"
                              >
                                <Image
                                  src={image}
                                  alt={`Gallery ${index + 1}`}
                                  fill
                                  className="object-cover"
                                />
                                <button
                                  type="button"
                                  disabled={isDeletingImage}
                                  onClick={async () => {
                                    const success = await deleteImage(image);
                                    if (success) {
                                      field.onChange(
                                        field.value.filter(
                                          (_, i) => i !== index
                                        )
                                      );
                                    }
                                  }}
                                  className="absolute top-2 right-2 bg-destructive text-white p-1.5 rounded-full shadow-lg hover:bg-destructive/90 transition-colors"
                                >
                                  {isDeletingImage ? (
                                    <Loader2 className="h-4 w-4 animate-spin" />
                                  ) : (
                                    <X className="h-4 w-4" />
                                  )}
                                </button>
                              </div>
                            ))}
                          </div>
                        </div>
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>
            </section>

            {/* Branch Pricings */}
            <section className="space-y-6">
              <div className="border-b pb-2">
                <h3 className="text-lg font-semibold">Club Pricings</h3>
                <p className="text-sm text-muted-foreground">
                  Upload pricing information for your sports facility
                </p>
              </div>

              <FormField
                control={form.control}
                name="branch_pricings"
                render={({ field }) => (
                  <FormItem>
                    <FormDescription className="text-xs text-muted-foreground mb-2">
                      Create pricing sections with names and upload multiple images for each section
                    </FormDescription>
                    <FormControl>
                      <div className="space-y-6">
                        {/* Pricing Creation Section */}
                        <div className="space-y-4 border rounded-md p-4">
                          <div className="flex flex-col space-y-4">
                            <div className="flex gap-2">
                              <Input
                                value={newPricingName}
                                onChange={(e) => setNewPricingName(e.target.value)}
                                placeholder="Enter pricing name (e.g., Weekday Rates)"
                                className="focus:ring-1 focus:ring-primary/20"
                              />
                              <Button
                                type="button"
                                variant="secondary"
                                onClick={handleAddPricing}
                                disabled={!newPricingName.trim()}
                              >
                                <Plus className="h-4 w-4 mr-1" />
                                Add Pricing
                              </Button>
                            </div>
                          </div>

                          {/* Pricing List */}
                          {field.value && field.value.length > 0 && (
                            <div className="mt-4">
                              <div className="text-sm font-medium mb-2">Your Pricing Sections:</div>
                              <div className="flex flex-wrap gap-2">
                                {field.value.map((pricing, index) => (
                                  <div
                                    key={index}
                                    onClick={() => setCurrentPricingIndex(index)}
                                    className={cn(
                                      "px-3 py-1.5 rounded-md text-sm cursor-pointer transition-colors",
                                      currentPricingIndex === index
                                        ? "bg-primary text-primary-foreground"
                                        : "bg-secondary/50 hover:bg-secondary"
                                    )}
                                  >
                                    {pricing.pricingName}
                                    {pricing.pricingImages?.length > 0 && (
                                      <span className="ml-1.5 text-xs opacity-70">
                                        ({pricing.pricingImages.length})
                                      </span>
                                    )}
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}
                        </div>

                        {/* Pricing Image Upload Section - Only show when a pricing is selected */}
                        {currentPricingIndex !== null && field.value && field.value[currentPricingIndex] && (
                          <div className="space-y-4 border rounded-md p-4">
                            <div className="flex items-center justify-between">
                              <div className="font-medium">
                                {field.value[currentPricingIndex].pricingName} Images
                              </div>
                              <div className="flex items-center gap-4">
                                <Input
                                  type="file"
                                  accept="image/*"
                                  multiple
                                  onChange={handlePricingImageUploadChange}
                                  className="hidden"
                                  id="pricing-images"
                                />
                                <Button
                                  type="button"
                                  variant="outline"
                                  onClick={() =>
                                    document.getElementById("pricing-images")?.click()
                                  }
                                  disabled={uploadingPricingImages}
                                  size="sm"
                                >
                                  {uploadingPricingImages ? (
                                    <Loader2 className="h-3.5 w-3.5 animate-spin mr-1" />
                                  ) : (
                                    <Upload className="h-3.5 w-3.5 mr-1" />
                                  )}
                                  Upload Images
                                </Button>
                                <Button
                                  type="button"
                                  variant="ghost"
                                  size="sm"
                                  className="text-destructive hover:text-destructive/90 hover:bg-destructive/10"
                                  onClick={() => {
                                    // Remove this pricing section
                                    const updatedPricings = field.value.filter(
                                      (_, i) => i !== currentPricingIndex
                                    );
                                    field.onChange(updatedPricings);
                                    setCurrentPricingIndex(null);
                                  }}
                                >
                                  <X className="h-3.5 w-3.5 mr-1" />
                                  Remove Section
                                </Button>
                              </div>
                            </div>

                            {/* Display pricing images */}
                            {field.value[currentPricingIndex].pricingImages?.length > 0 ? (
                              <div className="grid grid-cols-2 gap-4 mt-4">
                                {field.value[currentPricingIndex].pricingImages.map(
                                  (image, imageIndex) => (
                                    <div
                                      key={imageIndex}
                                      className="relative aspect-video overflow-hidden rounded-lg border"
                                    >
                                      <Image
                                        src={image}
                                        alt={`${field.value[currentPricingIndex].pricingName} ${imageIndex + 1}`}
                                        fill
                                        className="object-cover"
                                      />
                                      <button
                                        type="button"
                                        disabled={isDeletingImage}
                                        onClick={async () => {
                                          const success = await deleteImage(image);
                                          if (success) {
                                            // Create a new array with the updated pricing section
                                            const updatedPricings = [...field.value];
                                            updatedPricings[currentPricingIndex] = {
                                              ...updatedPricings[currentPricingIndex],
                                              pricingImages: updatedPricings[
                                                currentPricingIndex
                                              ].pricingImages.filter(
                                                (_, i) => i !== imageIndex
                                              ),
                                            };
                                            field.onChange(updatedPricings);
                                          }
                                        }}
                                        className="absolute top-2 right-2 bg-destructive text-white p-1.5 rounded-full shadow-lg hover:bg-destructive/90 transition-colors"
                                      >
                                        {isDeletingImage ? (
                                          <Loader2 className="h-4 w-4 animate-spin" />
                                        ) : (
                                          <X className="h-4 w-4" />
                                        )}
                                      </button>
                                    </div>
                                  )
                                )}
                              </div>
                            ) : (
                              <div className="text-center py-8 text-muted-foreground text-sm border border-dashed rounded-md">
                                No pricing images uploaded yet. Click {"Upload Images"} to add some.
                              </div>
                            )}
                          </div>
                        )}
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </section>

            {/* Contact & Additional Info */}
            <section className="space-y-6">
              <div className="border-b pb-2">
                <h3 className="text-lg font-semibold">
                  Contact & Additional Info
                </h3>
                <p className="text-sm text-muted-foreground">
                  Help customers reach and find your venue
                </p>
              </div>

              <div className="space-y-6">
                <FormField
                  control={form.control}
                  name="club_landline_numbers"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Club Landline Numbers</FormLabel>
                      <FormDescription className="mb-2">
                        Add landline numbers where customers can reach your
                        venue
                      </FormDescription>
                      {renderEnhancedMultiInput(
                        newPhoneNumber,
                        setNewPhoneNumber,
                        "Enter landline number and press Enter",
                        field.value || [],
                        (number) =>
                          field.onChange([...(field.value || []), number]),
                        (index) =>
                          field.onChange(
                            field.value?.filter((_, i) => i !== index) || []
                          )
                      )}
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="border-t pt-4 mt-4">
                  <h4 className="text-base font-semibold mb-4">
                    Social Media Links
                  </h4>
                  <div className="grid md:grid-cols-2 gap-4">
                    <FormField
                      control={form.control}
                      name="social_links.facebook"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Facebook</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="https://facebook.com/yourclub"
                              className="focus:ring-2 focus:ring-primary/30"
                              onChange={(e) =>
                                handleSocialLinkChange(
                                  "facebook",
                                  e.target.value
                                )
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="social_links.instagram"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Instagram</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="https://instagram.com/yourclub"
                              className="focus:ring-2 focus:ring-primary/30"
                              onChange={(e) =>
                                handleSocialLinkChange(
                                  "instagram",
                                  e.target.value
                                )
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="social_links.twitter"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Twitter</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="https://twitter.com/yourclub"
                              className="focus:ring-2 focus:ring-primary/30"
                              onChange={(e) =>
                                handleSocialLinkChange(
                                  "twitter",
                                  e.target.value
                                )
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />

                    <FormField
                      control={form.control}
                      name="social_links.website"
                      render={({ field }) => (
                        <FormItem>
                          <FormLabel>Website</FormLabel>
                          <FormControl>
                            <Input
                              {...field}
                              placeholder="https://yourclub.com"
                              className="focus:ring-2 focus:ring-primary/30"
                              onChange={(e) =>
                                handleSocialLinkChange(
                                  "website",
                                  e.target.value
                                )
                              }
                            />
                          </FormControl>
                          <FormMessage />
                        </FormItem>
                      )}
                    />
                  </div>
                </div>

                <div className="grid md:grid-cols-3 gap-6">
                  <FormField
                    control={form.control}
                    name="average_spend"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Average Spend</FormLabel>
                        <FormControl>
                          <Input
                            {...field}
                            type="number"
                            min="0"
                            placeholder="Enter amount in AED"
                            onChange={(e) =>
                              field.onChange(Number(e.target.value))
                            }
                          />
                        </FormControl>
                        <FormDescription>
                          Average spend per person in AED
                        </FormDescription>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="latitude"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Latitude</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input {...field} placeholder="e.g., 25.2048" />
                            <MapPin className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="longitude"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Longitude</FormLabel>
                        <FormControl>
                          <div className="relative">
                            <Input {...field} placeholder="e.g., 55.2708" />
                            <MapPin className="absolute right-3 top-2.5 h-4 w-4 text-muted-foreground" />
                          </div>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>
              </div>
            </section>

            <div className="flex justify-end pt-6 border-t">
              <Button
                type="submit"
                size="lg"
                className="min-w-[200px] shadow-lg"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Saving...
                  </>
                ) : (
                  <>
                    <Check className="mr-2 h-5 w-5" />
                    Save Changes
                  </>
                )}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </Form>
  );
}
