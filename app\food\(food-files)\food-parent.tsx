"use client";
import React from "react";
import Food from "./food";
import { useSearchParams } from "next/navigation";
import { useQuery } from "@tanstack/react-query";
import { useGetLocation } from "@/components/use-get-location-hook";

export default function FoodParent() {
  const { latitude, longitude, loading, refreshLocation } = useGetLocation();
  const searchParams = useSearchParams();
  const queryEmirate = searchParams.get("emirate");
  const querySearch = searchParams.get("search");
  const queryPage = searchParams.get("page") || "1";

  // console.log({
  //   queryEmirate: queryEmirate,
  //   querySearch: querySearch,
  //   queryLatitude: queryLatitude,
  //   queryLongitude: queryLongitude,
  // });

  const { data: foodListData, isLoading } = useQuery({
    queryKey: [
      "getFoodListings",
      latitude,
      longitude,
      loading,
      queryPage,
      queryEmirate,
      querySearch,
    ],
    queryFn: async () => {
      const response = await fetch(
        process.env.NEXT_PUBLIC_FOOD_BACKEND_URL +
          `/restaurants/landing-page/listing-page-details?pageNumber=${queryPage}${
            queryEmirate && queryEmirate !== "All"
              ? `&emirate=${queryEmirate}`
              : ""
          }${querySearch ? `&search=${querySearch}` : ""}${
            latitude && longitude
              ? `&latitude=${latitude}&longitude=${longitude}`
              : ""
          }`
      );
      const data = await response.json();
      // console.log(data.data);
      return data.data;
    },
    enabled: !loading,
  });

  return (
    <>
      {/* {foodListData && ( */}
      <Food
        queryEmirate={queryEmirate}
        querySearch={querySearch}
        foodListData={foodListData?.listingData}
        currentPage={foodListData?.currentPage || 1}
        totalPages={foodListData?.totalPages || 1}
        isLoading={isLoading}
        refreshLocation={refreshLocation}
      />
      {/* )} */}
    </>
  );
}
