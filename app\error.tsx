"use client";
import { But<PERSON> } from "@/components/ui/button";
import { useMutation } from "@tanstack/react-query";
import { useEffect, useState } from "react";

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string };
  reset: () => void;
}) {
  const [browserInfo, setBrowserInfo] = useState({ browser: "", os: "" });

  const { mutateAsync: appRootError } = useMutation({
    mutationKey: ["AppRootError"],
    mutationFn: async ({
      type,
      typeOf,
      errorMessage,
      errorName,
      os,
      browser,
      additionalInfo,
      url,
      user_agent,
    }: {
      type: string;
      typeOf: string;
      errorMessage?: any;
      errorName?: any;
      os?: any;
      browser?: any;
      additionalInfo?: any;
      url?: any;
      user_agent?: any;
    }) => {
      return fetch(
        process.env.NEXT_PUBLIC_SPORTS_BACKEND_URL + "/common/log-errors",
        {
          method: "POST",
          headers: new Headers({
            "content-type": "application/json",
          }),
          body: JSON.stringify({
            type: type,
            typeOf: typeOf,
            errorName: errorName,
            errorMessage: errorMessage,
            os,
            browser,
            additionalInfo: additionalInfo,
            url: url,
            user_agent: user_agent,
          }),
        }
      );
    },
    onSuccess: async (data) => {
      const response = await data.json();
      if (response.error && response.statusCode === 400) {
        console.log(response.error);
      }
    },
    onError: (e) => {
      console.log(e);
    },
  });

  useEffect(() => {
    if (typeof window !== "undefined") {
      const userAgent = navigator.userAgent.toLowerCase();
      let browser = "Unknown";
      let os = "Unknown";

      const errorUrl = window.location.href;

      // THIS IS WHERE WE CHECK FOR OS
      if (userAgent.includes("android")) os = "Android";
      else if (userAgent.includes("iphone") || userAgent.includes("ipad"))
        os = "iOS";
      else if (userAgent.includes("win")) os = "Windows";
      else if (userAgent.includes("mac")) os = "MacOS";
      else if (userAgent.includes("linux")) os = "Linux";

      // THIS IS WHERE WE CHECK BROWSER, IOS WILL ALWAYS SAY SAFARI BECOS WEBKIT, BRAVE WILL SAY CHROME
      if (userAgent.includes("chrome") && !userAgent.includes("edg"))
        browser = "Chrome";
      else if (userAgent.includes("edg")) browser = "Edge";
      else if (userAgent.includes("safari") && !userAgent.includes("chrome"))
        browser = "Safari";
      else if (userAgent.includes("firefox")) browser = "Firefox";
      else if (userAgent.includes("opr") || userAgent.includes("opera"))
        browser = "Opera";
      else if (userAgent.includes("msie") || userAgent.includes("trident"))
        browser = "Internet Explorer";

      setBrowserInfo({ browser, os });
      appRootError({
        type: "JUSTCRAVIN_APP_ERROR",
        typeOf: "justcravin_app_error",
        errorName: error.name,
        errorMessage: error.message,
        os: os,
        browser: browser,
        url: errorUrl,
        user_agent: userAgent,
      });
    }
    console.error(error);
  }, [error]);

  return (
    <div className="h-screen w-full flex flex-col items-center justify-center relative overflow-hidden text-center px-6 bg-white">
      {/*Random robot svg i got online, we can change if we want*/}
      <div className="mb-8">
        <svg
          className="w-48 h-48 mx-auto text-black"
          viewBox="0 0 64 64"
          fill="none"
          xmlns="http://www.w3.org/2000/svg"
        >
          <rect
            x="8"
            y="16"
            width="48"
            height="36"
            rx="4"
            stroke="currentColor"
            strokeWidth="2"
            fill="currentColor"
            fillOpacity="0.05"
          />

          <circle cx="20" cy="32" r="3" fill="currentColor" />
          <line
            x1="42"
            y1="29"
            x2="46"
            y2="33"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
          />
          <line
            x1="46"
            y1="29"
            x2="42"
            y2="33"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
          />

          <path
            d="M24 42 Q32 36 40 42"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            fill="none"
          />

          <line
            x1="32"
            y1="10"
            x2="32"
            y2="16"
            stroke="currentColor"
            strokeWidth="2"
          />
          <circle cx="32" cy="8" r="2" fill="currentColor" />

          <circle cx="6" cy="28" r="2" fill="currentColor" />
          <circle cx="58" cy="28" r="2" fill="currentColor" />
        </svg>
      </div>

      <h1 className="text-4xl font-bold text-foreground mb-4">
        Something went wrong
      </h1>

      <p className="text-lg text-muted-foreground max-w-md mb-6">
        Try refreshing the page — we’re working on getting things back to
        normal.
      </p>

      <Button
        onClick={() => window.location.reload()}
        className="px-6 py-3 bg-black hover:bg-black text-white font-medium rounded-lg flex items-center gap-2"
      >
        <svg
          className="w-5 h-5"
          fill="none"
          stroke="currentColor"
          viewBox="0 0 24 24"
        >
          <path
            strokeLinecap="round"
            strokeLinejoin="round"
            strokeWidth="2"
            d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"
          />
        </svg>
        Refresh Page
      </Button>

      <p className="mt-6 text-sm text-muted-foreground">
        Still not working? Our team&apos;s on it — thanks for your patience.
      </p>
    </div>
  );
}
