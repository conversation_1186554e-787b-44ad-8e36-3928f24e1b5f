import type { <PERSON>ada<PERSON>, Viewport } from "next";
import localFont from "next/font/local";
import { Inter } from "next/font/google";
import "./globals.css";
import TanstackProvider from "@/providers/TanstackProvider";
import AuthProvider from "@/components/providers/auth-provider";
import Script from "next/script";
import { Toaster } from "@/components/ui/sonner";
// const inter = Inter({
//   subsets: ["latin"],
//   variable: "--font-inter",
//   display: "swap",
// });

export const fetchCache = "force-no-store";
const inter = localFont({
  src: [
    {
      path: "../public/Font/Inter_18pt-Black.ttf",
      weight: "900",
      style: "normal",
    },
    {
      path: "../public/Font/Inter_18pt-Bold.ttf",
      weight: "500",
      style: "normal",
    },
    {
      path: "../public/Font/Inter_18pt-Medium.ttf",
      weight: "400",
      style: "normal",
    },
    {
      path: "../public/Font/Inter_18pt-Light.ttf",
      weight: "300",
      style: "normal",
    },
  ],
  variable: "--font-inter-custom",
});

const minako = localFont({
  src: [
    {
      path: "../public/Font/Minako-Regular.ttf",
      weight: "500",
      style: "normal",
    },
  ],
  variable: "--font-minako",
});

export const metadata: Metadata = {
  title: "Cravin | Explore Our Top Products & Associated Listings",
  description:
    "Showcase of Cravin's exclusive products and associated listings, offering a variety of selections among restaurants, commercial outlets, and sports venues.",
  icons: {
    icon: "/Cravin_logo_favi.png",
  },
  openGraph: {
    title: "Cravin | Explore Our Top Products & Associated Listings",
    description:
      "Showcase of Cravin's exclusive products and associated listings, offering a variety of selections among restaurants, commercial outlets, and sports venues.",
    url: "https://justcravin.com",
    type: "website",
  },
  alternates: {
    canonical: "https://justcravin.com",
  },
};

export const viewport: Viewport = {
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
  // Also supported by less commonly used
  // interactiveWidget: 'resizes-visual',
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en" className={inter.variable}>
      <head>
        <link
          rel="stylesheet"
          href="https://unpkg.com/leaflet@1.9.4/dist/leaflet.css"
          integrity="sha256-p4NxAoJBhIIN+hmNHrzRCf9tD/miZyoHS5obTRR9BMY="
          crossOrigin=""
        />
        <Script id="microsoft-clarity" strategy="afterInteractive">
          {`
						(function(c,l,a,r,i,t,y){
							c[a]=c[a]||function(){(c[a].q=c[a].q||[]).push(arguments)};
							t=l.createElement(r);t.async=1;t.src="https://www.clarity.ms/tag/"+i;
							y=l.getElementsByTagName(r)[0];y.parentNode.insertBefore(t,y);
						})(window, document, "clarity", "script", "rlx0rvqsrt");
					`}
        </Script>
        <meta
          name="google-site-verification"
          content="jfwZ-65iGxPHVi2RIjUlR4zq4sgQ0mbBmkcx2ajSGFI"
        />
      </head>
      <body>
        <AuthProvider>
          <TanstackProvider>
            <Toaster position="top-center" richColors />

            {children}
            <Script
              src="https://unpkg.com/leaflet@1.9.4/dist/leaflet.js"
              integrity="sha256-20nQCchB9co0qIjJZRGuk2/Z9VM+kNiyxNV1lvTlZBo="
              crossOrigin=""
            />
          </TanstackProvider>
        </AuthProvider>
      </body>
    </html>
  );
}
