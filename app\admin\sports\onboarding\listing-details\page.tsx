"use client";

import { useSearchParams } from "next/navigation";
import Link from "next/link";
import {
  ChevronLeft,
  Building2,
  Edit,
  ArrowLeft,
  Store,
  Zap,
  FolderPlus,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import ListingDetailsForm from "@/components/sports/onboarding/listing-details-form";
import { FaWhatsapp } from "react-icons/fa";
import React from "react";

function ListingDetailsPage() {
  const searchParams = useSearchParams();
  const clubId = searchParams.get("id") as string;

  return (
    <main className="container mx-auto py-6 space-y-6">
      {/* Header with improved navigation */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 pb-4 border-b">
        <div className="flex items-center gap-4">
          <Link href={`/admin/sports/onboarding?id=${clubId}`}>
            <Button variant="outline" size="icon" className="h-8 w-8">
              <ChevronLeft className="h-4 w-4" />
            </Button>
          </Link>
          <div>
            <h1 className="text-2xl font-bold tracking-tight">
              Court Listings
            </h1>
            <p className="text-muted-foreground mt-1">
              Configure your sports club courts and facilities
            </p>
          </div>
        </div>

        <div className="flex flex-wrap gap-2">
          <Link href={`/admin/sports`}>
            <Button variant="outline" size="sm" className="gap-2">
              <Store className="h-4 w-4" />
              All Clubs
            </Button>
          </Link>

          {/* WhatsApp onboarding */}
          <Link href={`/admin/onboard-whatsapp/sports/${clubId}`}>
            <Button
              variant="outline"
              size="sm"
              className="gap-2 bg-white hover:bg-green-50 text-green-600 border-green-200 hover:border-green-300"
            >
              <FaWhatsapp className="h-3.5 w-3.5" />
              WhatsApp Setup
            </Button>
          </Link>

          {/* Club details */}
          <Link href={`/admin/sports/onboarding?id=${clubId}`}>
            <Button variant="outline" size="sm" className="gap-2">
              <Edit className="h-4 w-4" />
              Club Details
            </Button>
          </Link>
        </div>
      </div>

      {/* Breadcrumb navigation for better context */}
      <div className="flex items-center text-sm text-muted-foreground mb-4">
        <Link href="/admin/sports" className="hover:text-primary">
          Sports Clubs
        </Link>
        <ArrowLeft className="h-3 w-3 mx-2 rotate-180" />
        <Link
          href={`/admin/sports/onboarding?id=${clubId}`}
          className="hover:text-primary"
        >
          Club Details
        </Link>
        <ArrowLeft className="h-3 w-3 mx-2 rotate-180" />
        <span className="text-foreground font-medium">Court Listings</span>
      </div>

      <ListingDetailsForm clubId={clubId} />
    </main>
  );
}

export default function MainListingPage() {
  // add suspense and return fallback UI
  return (
    <React.Suspense
      fallback={
        <div className="flex items-center justify-center h-screen">
          Loading...
        </div>
      }
    >
      <ListingDetailsPage />
    </React.Suspense>
  );
}
