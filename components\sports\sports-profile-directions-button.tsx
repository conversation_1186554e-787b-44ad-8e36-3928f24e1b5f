import React from "react";
import { Button } from "../ui/button";
import { FaDirections } from "react-icons/fa";

const SportsProfileDirectionsButton = ({
  latitude,
  longitude,
}: {
  latitude: any;
  longitude: any;
}) => {
  const handleOpenGoogleMaps = () => {
    const googleMapsUrl = `https://www.google.com/maps/dir/?api=1&destination=${latitude},${longitude}`;

    window.open(googleMapsUrl, "_blank");
  };

  return (
    <Button
      className="w-full sm:w-fit h-8 px-3 flex gap-1 justify-center items-center border-[2px] border-black   bg-[#d5ff01] hover:bg-[#d5ff01] text-black rounded-full"
      onClick={handleOpenGoogleMaps}
    >
      <FaDirections size={14} />
      {" Directions"}
    </Button>
  );
};

export default SportsProfileDirectionsButton;
