import React from "react";
import { But<PERSON> } from "../ui/button";
import { FaShare } from "react-icons/fa";

const CommerceProfileShareButton = () => {
  const handleShare = () => {
    if (navigator.share) {
      navigator
        .share({
          title: "Cravin Commerce", // The title of the shared content
          text: "Check out this awesome outlet!", // Optional text to share
          url: window.location.href, // The URL to share
        })
        .then(() => console.log("Successfully shared"))
        .catch((error) => console.log("Error sharing:", error));
    } else {
      alert("Sharing is not supported on this browser.");
    }
  };

  return (
    <Button
      className="w-full sm:w-fit h-8 px-3 flex gap-1 justify-center items-center bg-white border-[2px] border-[#ff8a00] text-[#ff8a00] hover:bg-white rounded-full"
      onClick={handleShare}
    >
      <FaShare size={14} /> {" Share"}
    </Button>
  );
};

export default CommerceProfileShareButton;
